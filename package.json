{"name": "andor", "version": "1.0.0", "scripts": {"script": "ts-node", "watch-script": "nodemon", "test-efile": "ts-node api/scripts/efiling.ts", "test-intake": "ts-node api/scripts/intake.ts", "dev-api": "nodemon api/src/index.ts", "dev-worker": "nodemon api/src/worker.ts", "dev-configure-queue": "nodemon api/src/configure-queue.ts", "dev-web": "vite --config web/vite.config.ts", "build-api": "NODE_ENV=production ./node_modules/.bin/tsc --project api/tsconfig.build.json && ./node_modules/.bin/tsc-alias -p api/tsconfig.build.json", "build-web": "NODE_ENV=production ./node_modules/.bin/vite build --config web/vite.config.ts", "migrate": "./node_modules/.bin/drizzle-kit migrate", "configure-queue": "node dist/api/src/configure-queue.js", "start-api": "node dist/api/src/index.js", "start-worker": "node dist/api/src/worker.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@algolia/client-search": "^5.12.0", "@azure/msal-node": "^2.16.2", "@faker-js/faker": "^9.2.0", "@floating-ui/react": "^0.27.3", "@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.3", "@logtail/node": "^0.5.2", "@tanstack/query-sync-storage-persister": "^5.62.0", "@tanstack/react-query": "^5.40.0", "@tanstack/react-query-devtools": "^5.40.0", "@tanstack/react-query-persist-client": "^5.62.0", "@types/react-copy-to-clipboard": "^5.0.7", "@types/tmp": "^0.2.6", "autoprefixer": "^10.4.20", "axios": "^1.7.8", "classnames": "^2.5.1", "clsx": "^2.1.1", "connect-pg-simple": "^10.0.0", "dayjs": "^1.11.13", "dompurify": "^3.2.3", "dotenv": "^16.4.5", "drizzle-kit": "^0.28.1", "drizzle-orm": "^0.38.3", "drizzle-zod": "^0.6.1", "envalid": "^8.0.0", "express": "^5.0.1", "express-session": "^1.18.1", "helmet": "^8.0.0", "lodash": "^4.17.21", "mailgun.js": "^10.4.0", "pg": "^8.13.1", "pg-boss": "^10.1.5", "pluralize": "^8.0.0", "puppeteer-core": "^23.11.1", "query-string": "^9.0.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-copy-to-clipboard": "^5.1.0", "react-daisyui": "^5.0.5", "react-datepicker": "^7.4.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-hook-form": "^7.51.5", "react-router-dom": "^6.23.1", "react-select": "^5.8.2", "react-textarea-autosize": "^8.5.6", "react-time-picker": "^7.0.0", "react-tiny-popover": "^8.1.4", "tinycolor2": "^1.6.0", "tmp": "^0.2.3", "typescript-eslint": "^8.12.2", "unique-filename": "^4.0.0", "use-sync-external-store": "^1.2.2", "usehooks-ts": "^3.1.0", "uuid": "^11.0.3", "vite-plugin-webfont-dl": "^3.9.5", "zod": "^3.23.8", "zod-class": "^0.0.15", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@eslint/js": "^9.13.0", "@tanstack/eslint-plugin-query": "^5.35.6", "@types/connect-pg-simple": "^7.0.3", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/lodash": "^4.17.13", "@types/node": "^22.8.1", "@types/pg": "^8.11.10", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "concurrently": "^9.0.1", "daisyui": "^4.11.1", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "JSONStream": "^1.3.5", "nodemon": "^3.1.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-webfont-dl": "^3.9.4"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.29.1"}}