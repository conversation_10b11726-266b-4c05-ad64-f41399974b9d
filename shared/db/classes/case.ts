import { createSelectSchema } from "drizzle-zod";
import { forEach } from "lodash";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseSchema } from "../base";
import {
  AdditionalDefendantsSchema,
  CaseDatesSchema,
  CaseRentAndFeesSchema,
  Cases,
  CommNoticesORSchema,
  noticeTypeLabels,
  ResNoticesORSchema,
} from "../schema/cases";
import { ChecklistItem } from "../schema/checklist-items";
import { Client } from "../schema/clients";
import { Comment } from "../schema/comments";
import { Contact } from "../schema/contacts";
import { Defendant } from "../schema/defendants";
import { Property } from "../schema/properties";

export const BaseCaseSchema = createSelectSchema(Cases);
export const CaseSchema = BaseCaseSchema.extend({
  ...baseSchema,
  createdAt: z.coerce.date().nullish(),
  updatedAt: z.coerce.date().nullish(),
  nextActivityExpectedDate: z.coerce.date().nullish(),
  defendant: z.instanceof(Defendant).nullish(),
  property: z.instanceof(Property).nullish(),
  client: z.instanceof(Client).nullish(),
  opposingCounsel: z.instanceof(Contact).nullish(),
  caseDates: CaseDatesSchema.nullish(),
  additionalDefendants: AdditionalDefendantsSchema.nullish(),
  resNoticesOR: ResNoticesORSchema.nullish(),
  commNoticesOR: CommNoticesORSchema.nullish(),
  caseRentAndFees: CaseRentAndFeesSchema.nullish(),
  comments: z.array(z.instanceof(Comment)).nullish(),
  checklistItems: z.array(z.instanceof(ChecklistItem)).nullish(),
});

export class CaseClass extends zodClass.class(CaseSchema.shape) {
  constructor(data: any) {
    const client = data.client ? new Client(data.client) : null;
    const property = data.property ? new Property(data.property) : null;
    const defendant = data.defendant ? new Defendant(data.defendant) : null;
    const opposingCounsel = data.opposingCounsel
      ? new Contact(data.opposingCounsel)
      : null;
    const comments = data.comments
      ? data.comments?.map((comment: any) => {
          return new Comment(comment);
        })
      : null;
    const checklistItems = data.checklistItems
      ? data.checklistItems?.map((checklistItem: any) => {
          return new ChecklistItem(checklistItem);
        })
      : null;

    super({
      ...data,
      client,
      property,
      defendant,
      opposingCounsel,
      comments,
      checklistItems,
    });
  }

  getDefendantName(): string {
    return this.defendant?.getName() || "";
  }

  getCaseCaption(): string {
    return `${this.client?.name}, agent for ${
      this.property?.name
    } vs ${this.getDefendantName()}`;
  }

  getNotices(): string[] {
    const notices: string[] = [];
    const isOregon = this.property?.state === "OR";
    const isWashington = this.property?.state === "WA";

    if (isWashington) notices.push(this.waNoticeType || "");
    if (isOregon) {
      const orNotices: any =
        this.residentialCommercial === "Residential"
          ? this.resNoticesOR
          : this.commNoticesOR;
      forEach(orNotices, (value, key) => {
        let label = noticeTypeLabels[key];

        if (label === "30-day with cause" && orNotices.causeIsNonpayment)
          label = `${label} (nonpayment)`;
        if (value === true) notices.push(label);
      });
    }

    return notices;
  }
}
