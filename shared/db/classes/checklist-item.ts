import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { ChecklistItems } from "../schema/checklist-items";

export const BaseChecklistItemSchema = createSelectSchema(ChecklistItems);
export const ChecklistItemSchema = BaseChecklistItemSchema.extend({
  createdAt: z.coerce.date().nullish(),
  updatedAt: z.coerce.date().nullish(),
  deletedAt: z.coerce.date().nullish(),
});

export class ChecklistItem extends zodClass.class(ChecklistItemSchema.shape) {}
