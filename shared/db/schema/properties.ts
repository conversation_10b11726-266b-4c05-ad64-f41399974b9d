import { relations } from "drizzle-orm";
import { boolean, integer, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { BaseClientSchema, Client, Clients } from "./clients";
import { Contact, Contacts } from "./contacts";

export const Properties = pgTable(
  "properties",
  {
    ...baseColumns,

    // Basic columns
    uuid: uuid().unique(),
    name: text(),
    clientName: text(),
    clientUuid: uuid(),
    clientId: integer(),
    actionServices: text(),
    propertyNotes: text(),
    address1: text(),
    address2: text(),
    city: text(),
    state: text(),
    zip: text(),
    county: text(),
    isNew: boolean(),
    doNotUse: boolean().default(false),
    isHomeForward: boolean(),
  },
  (table) => ({ ...baseIndexes(table) })
);

// Join Table: ContactToProperties
export const ContactToProperties = pgTable("contact_properties", {
  contactId: integer("contact_id")
    .references(() => Contacts.id)
    .notNull(),
  propertyId: integer("property_id")
    .references(() => Properties.id)
    .notNull(),
});

// Relations for ContactProperties (Join Table)
export const ContactPropertiesRelations = relations(
  ContactToProperties,
  ({ one }) => ({
    contact: one(Contacts, {
      fields: [ContactToProperties.contactId],
      references: [Contacts.id],
    }),
    property: one(Properties, {
      fields: [ContactToProperties.propertyId],
      references: [Properties.id],
    }),
  })
);

// Relations for Properties
export const PropertiesRelations = relations(Properties, ({ many, one }) => ({
  contacts: many(ContactToProperties),
  client: one(Clients, {
    fields: [Properties.clientId],
    references: [Clients.id],
  }),
}));

export const BasePropertySchema = createSelectSchema(Properties);
export const PropertySchema = BasePropertySchema.extend({
  ...baseSchema,
  contacts: z.array(z.instanceof(Contact)).nullish(),
  client: z.lazy(() => BaseClientSchema).nullish(),
});

export class Property extends zodClass.class(PropertySchema.shape) {
  constructor(data: any) {
    const contacts = data.contacts?.map(
      (contact: any) => new Contact(contact.contact)
    );

    const client = data.client ? new Client(data.client) : null;

    super({ ...data, client, contacts });
  }

  getAddressForUrl() {
    return `${this.address1} ${this.address2} ${this.city} ${this.state} ${this.zip}`.replace(
      / /g,
      "+"
    );
  }
}
