import { integer, pgEnum, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import path from "path";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const statusEnum = pgEnum("status", ["pending", "failed", "complete"]);
export const Documents = pgTable(
  "documents",
  {
    ...baseColumns,

    caseId: integer(),
    fileName: text(),
    filePath: text(),
    status: statusEnum(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseDocumentSchema = createSelectSchema(Documents);
export const DocumentSchema = BaseDocumentSchema.extend({
  ...baseSchema,
});

export class Document extends zodClass.class(DocumentSchema.shape) {
  getExtension() {
    return path.parse(this.fileName!).ext.slice(1);
  }

  getPrefix() {
    return path.parse(this.fileName!).name;
  }
}
