import { jsonb, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

const EmailAddressesSchema = z.array(z.string());
export const EmailTemplates = pgTable(
  "email_templates",
  {
    ...baseColumns,

    name: text(),
    type: text(),
    purpose: text(),
    audience: text(),
    to: jsonb().$type<z.infer<typeof EmailAddressesSchema>>(),
    cc: jsonb().$type<z.infer<typeof EmailAddressesSchema>>(),
    bcc: jsonb().$type<z.infer<typeof EmailAddressesSchema>>(),
    subject: text(),
    body: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseEmailTemplateSchema = createSelectSchema(EmailTemplates);
export const EmailTemplateSchema = BaseEmailTemplateSchema.extend({
  ...baseSchema,
  to: EmailAddressesSchema.nullish(),
  cc: EmailAddressesSchema.nullish(),
  bcc: EmailAddressesSchema.nullish(),
});
``;
export class EmailTemplate extends zodClass.class(EmailTemplateSchema.shape) {
  constructor(data: any) {
    super({ ...data });
  }
}
