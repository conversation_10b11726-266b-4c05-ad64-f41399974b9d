import { relations } from "drizzle-orm";
import { boolean, integer, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { Contact, Contacts } from "./contacts";
import { Properties, Property } from "./properties";

export const Clients = pgTable(
  "clients",
  {
    ...baseColumns,

    // Basic columns
    uuid: uuid().unique(),
    name: text(),
    isNew: boolean(),
    companyType: text(),
    clientNotes: text(),
    address1: text(),
    address2: text(),
    city: text(),
    state: text(),
    zip: text(),
    doNotUse: boolean().default(false),
  },
  (table) => ({ ...baseIndexes(table) })
);

// Join Table: ContactToClients
export const ContactToClients = pgTable("contact_clients", {
  contactId: integer("contact_id")
    .references(() => Contacts.id)
    .notNull(),
  clientId: integer("client_id")
    .references(() => Clients.id)
    .notNull(),
});

// Relations for ContactClients (Join Table)
export const ContactClientsRelations = relations(
  ContactToClients,
  ({ one }) => ({
    contact: one(Contacts, {
      fields: [ContactToClients.contactId],
      references: [Contacts.id],
    }),
    client: one(Clients, {
      fields: [ContactToClients.clientId],
      references: [Clients.id],
    }),
  })
);

// Relations for Clients
export const ClientsRelations = relations(Clients, ({ many }) => ({
  contacts: many(ContactToClients),
  properties: many(Properties),
}));

export const BaseClientSchema = createSelectSchema(Clients);
export const ClientSchema = BaseClientSchema.extend({
  ...baseSchema,
  contacts: z.array(z.instanceof(Contact)).optional(),
  properties: z.array(z.instanceof(Property)).optional(),
});

export class Client extends zodClass.class(ClientSchema.shape) {
  constructor(data: any) {
    const contacts = data.contacts?.map(
      (contact: any) => new Contact(contact.contact)
    );

    const properties = data.properties?.map(
      (property: any) => new Property(property)
    );

    super({ ...data, contacts, properties });
  }

  getAddressForUrl() {
    return `${this.address1} ${this.address2} ${this.city} ${this.state} ${this.zip}`.replace(
      / /g,
      "+"
    );
  }
}
