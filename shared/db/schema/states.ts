import { pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const States = pgTable(
  "states",
  {
    ...baseColumns,

    name: text(),
    fullName: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseStateSchema = createSelectSchema(States);
export const StateSchema = BaseStateSchema.extend({
  ...baseSchema,
});

export class State extends zodClass.class(StateSchema.shape) {}
