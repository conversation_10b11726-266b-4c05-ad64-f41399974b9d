import { jsonb, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

const CustomFieldsSchema = z.array(
  z.object({
    label: z.string(),
    type: z.string(),
    key: z.string(),
  })
);

const UsageSchema = z.array(z.string());

export const Templates = pgTable(
  "templates",
  {
    ...baseColumns,

    uuid: text(),
    name: text(),
    state: text(),
    county: text(),
    courtType: text(),
    category: text(),
    downloadFormat: text(),

    customFields: jsonb().$type<z.infer<typeof CustomFieldsSchema>>(),
    usage: jsonb().$type<z.infer<typeof UsageSchema>>(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseTemplateSchema = createSelectSchema(Templates);
export const TemplateSchema = BaseTemplateSchema.extend({
  ...baseSchema,
  customFields: CustomFieldsSchema.nullish(),
  usage: UsageSchema.nullish(),
});

export class Template extends zodClass.class(TemplateSchema.shape) {
  getFileExtension(customFields: { [key: string]: any }) {
    let outputFormat = this.downloadFormat!;
    if (customFields.pdf) outputFormat = "pdf";

    return outputFormat;
  }
  getFileName(customFields: { [key: string]: any }) {
    let outputFormat = this.downloadFormat!;
    if (customFields.pdf) outputFormat = "pdf";

    return `${this.name}.${outputFormat}`;
  }
}
