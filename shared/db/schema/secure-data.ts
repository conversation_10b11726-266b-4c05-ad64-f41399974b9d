import { pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const SecureData = pgTable(
  "secure_data",
  {
    ...baseColumns,
    iv: text().notNull(),
    encryptedData: text().notNull(),
    authTag: text().notNull(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseSecureDataSchema = createSelectSchema(SecureData);
export const SecuredDataSchema = BaseSecureDataSchema.extend({
  ...baseSchema,
});

export class SecuredData extends zodClass.class(SecuredDataSchema.shape) {}
