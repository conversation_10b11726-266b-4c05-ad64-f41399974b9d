import { relations } from "drizzle-orm";
import {
  boolean,
  integer,
  pgTable,
  text,
  timestamp,
} from "drizzle-orm/pg-core";
import { createSelectSchema, createUpdateSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { Cases } from "./cases";
import { User, Users } from "./users";

export const ChecklistItems = pgTable(
  "checklist_items",
  {
    ...baseColumns,

    caseId: integer().references(() => Cases.id),
    completedById: integer().references(() => Users.id),
    description: text(),
    status: text(), // questionable. maybe rename as status_at_completion or remove it.
    stage: text(),
    type: text(),
    order: integer(),
    complete: boolean(),
    completedDate: timestamp({ withTimezone: true }),
    sop: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const ChecklistItemRelations = relations(ChecklistItems, ({ one }) => ({
  case: one(Cases, {
    fields: [ChecklistItems.caseId],
    references: [Cases.id],
  }),
  completedBy: one(Users, {
    fields: [ChecklistItems.completedById],
    references: [Users.id],
  }),
}));

export const BaseChecklistItemSchema = createSelectSchema(ChecklistItems);
export const ChecklistItemSchema = BaseChecklistItemSchema.extend({
  ...baseSchema,
  completedDate: z.coerce.date().nullish(),
  completedBy: z.instanceof(User).nullish(),
});

export const BaseChecklistItemUpdateSchema = createUpdateSchema(ChecklistItems);
export const ChecklistItemUpdateSchema = BaseChecklistItemUpdateSchema.extend({
  completedDate: z.coerce.date().nullish(),
});

export class ChecklistItem extends zodClass.class(ChecklistItemSchema.shape) {
  constructor(data: any) {
    const completedBy = data.completedBy ? new User(data.completedBy) : null;
    super({ ...data, completedBy });
  }
}
