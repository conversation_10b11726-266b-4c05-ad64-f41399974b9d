import { boolean, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const Users = pgTable(
  "users",
  {
    ...baseColumns,

    uid: text().unique(),
    name: text(),
    initials: text(),
    email: text(),
    isAttorney: boolean(),
    osbNumber: text(),
    wsbNumber: text(),
    mfaPin: text(),
    fileAndServeEmail: text(),
    fileAndServePassword: text(),
    isDisabled: boolean().default(false),
    isArchived: boolean().default(false),
    isAdmin: boolean().default(false),
    isNonStaff: boolean().default(false),

    defaultState: text(),
    badgeColor: text(),
    badgeTextColor: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const BaseUserSchema = createSelectSchema(Users);
export const UserSchema = BaseUserSchema.extend({
  ...baseSchema,
});

export class User extends zodClass.class(UserSchema.shape) {
  isActive(): boolean {
    return !this.isDisabled && !this.isArchived;
  }

  getStatus(): string {
    if (this.isArchived === true) return "Archived";
    if (this.isDisabled === true) return "Disabled";

    return "Active";
  }
}
