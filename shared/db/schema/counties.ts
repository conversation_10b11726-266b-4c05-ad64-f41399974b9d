import { jsonb, numeric, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

const AlternateCourtSchema = z.object({
  address: z.string().nullish(),
  courtName: z.string().nullish(),
  faTime: z.string().nullish(),
  zips: z.array(z.string()).nullish(),
});

const JusticeCourtSchema = z.object({
  address: z.string().nullish(),
  courtName: z.string().nullish(),
  faTime: z.string().nullish(),
});

export const Counties = pgTable(
  "counties",
  {
    ...baseColumns,
    uuid: text(),
    name: text(),
    courtAddress: text(),
    state: text(),
    courtName: text(),
    defaultCourtType: text().default("circuit"),
    faTime: text(),
    serviceFee: numeric({ precision: 10, scale: 2 }),
    faFee: numeric({ precision: 10, scale: 2 }),
    filingFee: numeric({ precision: 10, scale: 2 }),
    sAndCFee: numeric({ precision: 10, scale: 2 }),
    circuitFee: numeric({ precision: 10, scale: 2 }),
    courtFee: numeric({ precision: 10, scale: 2 }),
    justiceFee: numeric({ precision: 10, scale: 2 }),

    justiceCourt: jsonb().$type<z.infer<typeof JusticeCourtSchema>>(),
    alternateCourt: jsonb().$type<z.infer<typeof AlternateCourtSchema>>(),
  },
  (table) => ({ ...baseIndexes(table) })
);

const BaseCountySchema = createSelectSchema(Counties);
export const CountySchema = BaseCountySchema.extend({
  ...baseSchema,
  justiceCourt: JusticeCourtSchema.nullish(),
  alternateCourt: AlternateCourtSchema.nullish(),
});

export class County extends zodClass.class(CountySchema.shape) {
  getCourtInfo(zip, courtType = "circuit") {
    const court = {
      courtAddress: this.courtAddress,
      courtName: this.courtName,
    };

    if (courtType === "justice") {
      court.courtAddress = this.justiceCourt?.address!;
      court.courtName = this.justiceCourt?.courtName!;
    }

    if (this.alternateCourt?.zips?.includes(zip)) {
      court.courtAddress = this.alternateCourt.address!;
      court.courtName = this.alternateCourt.courtName!;
    }

    return court;
  }
}
