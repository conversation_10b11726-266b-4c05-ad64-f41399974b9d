import { relations } from "drizzle-orm";
import { integer, pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { CaseClass, Cases } from "./cases";
import { Clients } from "./clients";
import { Defendant, Defendants } from "./defendants";
import { Properties } from "./properties";
import { User, Users } from "./users";

export const EfileOperations = pgTable(
  "efile_operations",
  {
    ...baseColumns,

    uuid: text().unique(),
    caseUuid: uuid(),
    defendantUuid: uuid(),
    userUid: text(),
    caseId: integer().references(() => Cases.id),
    defendantId: integer().references(() => Defendants.id),
    clientId: integer().references(() => Clients.id),
    propertyId: integer().references(() => Properties.id),
    userId: integer().references(() => Users.id),

    caseNumber: text(),
    defendantName: text(),
    timestamp: timestamp({ withTimezone: true }),
    botId: text(),
    eFileUrl: text(),
    statusMessage: text(),
    status: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const EfileOpertionRelations = relations(EfileOperations, ({ one }) => ({
  case: one(Cases, {
    fields: [EfileOperations.caseId],
    references: [Cases.id],
  }),
  client: one(Clients, {
    fields: [EfileOperations.clientId],
    references: [Clients.id],
  }),
  defendant: one(Defendants, {
    fields: [EfileOperations.defendantId],
    references: [Defendants.id],
  }),
  property: one(Properties, {
    fields: [EfileOperations.propertyId],
    references: [Properties.id],
  }),
  user: one(Users, {
    fields: [EfileOperations.userId],
    references: [Users.id],
  }),
}));

const BaseEfileOperationsSchema = createSelectSchema(EfileOperations);
export const EfileOperationsSchema = BaseEfileOperationsSchema.extend({
  ...baseSchema,
  timestamp: z.coerce.date().nullish(),
  case: z.instanceof(CaseClass).nullish(),
  defendant: z.instanceof(Defendant).nullish(),
  user: z.instanceof(User).nullish(),
});

export class EfileOperation extends zodClass.class(
  EfileOperationsSchema.shape
) {
  constructor(data: any) {
    data.case ? (data.case = new CaseClass(data.case)) : null;
    data.user ? (data.user = new User(data.user)) : null;
    data.defendant ? (data.defendant = new Defendant(data.defendant)) : null;

    super(data);
  }
}
