import { jsonb, pgTable, text } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const Settings = pgTable(
  "settings",
  {
    ...baseColumns,

    key: text().notNull().unique(),
    value: jsonb(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const BaseSettingSchema = createSelectSchema(Settings);
export const SettingSchema = BaseSettingSchema.extend({
  ...baseSchema,
  value: z.any().nullish(),
});

export class Setting extends zodClass.class(SettingSchema.shape) {}
