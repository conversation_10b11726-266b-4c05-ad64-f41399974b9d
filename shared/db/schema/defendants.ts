import { relations } from "drizzle-orm";
import {
  boolean,
  integer,
  jsonb,
  pgTable,
  text,
  uuid,
} from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { Properties, Property } from "./properties";

const MailingSchema = z.object({
  line1: z.string().nullish(),
  line2: z.string().nullish(),
  city: z.string().nullish(),
  state: z.string().nullish(),
  zip: z.string().nullish(),
});

export const Defendants = pgTable(
  "defendants",
  {
    ...baseColumns,

    // Basic columns
    uuid: uuid(),
    propertyId: integer().references(() => Properties.id),
    ssn: text(),
    dob: text(),
    status: text(),
    firstName: text(),
    middleName: text(),
    lastName: text(),
    suffix: text(),
    businessName: text(),
    isBusiness: boolean(),
    phone: text(),

    address1: text(),
    address2: text(),
    city: text(),
    state: text(),
    zip: text(),
    useMailingAddress: boolean(),

    mailing: jsonb().$type<z.infer<typeof MailingSchema>>(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const DefendantRelations = relations(Defendants, ({ one }) => ({
  property: one(Properties, {
    fields: [Defendants.propertyId],
    references: [Properties.id],
  }),
}));

export const BaseDefendantSchema = createSelectSchema(Defendants);
export const DefendantSchema = BaseDefendantSchema.extend({
  ...baseSchema,
  mailing: MailingSchema.nullish(),
  property: z.instanceof(Property).nullish(),
});

export class Defendant extends zodClass.class(DefendantSchema.shape) {
  constructor(data: any) {
    const property = data.property ? new Property(data.property) : null;

    super({ ...data, property });
  }

  getName() {
    if (this.isBusiness) return this.businessName;

    let name = this.firstName;

    if (this.middleName) name += ` ${this.middleName}`;
    if (this.lastName) name += ` ${this.lastName}`;
    if (this.suffix) name += ` ${this.suffix}`;

    return name;
  }

  getAddressForUrl() {
    return `${this.address1} ${this.address2} ${this.city} ${this.state} ${this.zip}`.replace(
      / /g,
      "+"
    );
  }

  getMailingAddressForUrl() {
    if (!this.useMailingAddress) return this.getAddressForUrl();

    return `${this.mailing?.line1} ${this.mailing?.line2} ${this.mailing?.city} ${this.mailing?.state} ${this.mailing?.zip}`.replace(
      / /g,
      "+"
    );
  }
}
