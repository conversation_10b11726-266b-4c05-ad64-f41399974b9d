import { boolean, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";

export const Contacts = pgTable(
  "contacts",
  {
    ...baseColumns,

    uuid: uuid().unique(),
    clientUuid: uuid(),
    propertyUuid: uuid(),
    isNew: boolean(),
    name: text(),
    title: text(),
    email: text(),
    fax: text(),
    phone: text(),
    cell: text(),
    other: text(),
    notes: text(),
    type: text(),
    barNumber: text(),
  },
  (table) => ({ ...baseIndexes(table) })
);

export const BaseContactSchema = createSelectSchema(Contacts);
export const ContactSchema = BaseContactSchema.extend({
  ...baseSchema,
});

export class Contact extends zodClass.class(ContactSchema.shape) {}
