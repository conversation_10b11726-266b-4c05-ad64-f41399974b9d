import { relations } from "drizzle-orm";
import {
  bigint,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
} from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { Z as zodClass } from "zod-class";
import { baseColumns, baseIndexes, baseSchema } from "../base";
import { Cases } from "./cases";
import { IntakeFormResponses } from "./intake-form-responses";

export const ClioContacts = pgTable(
  "clio_contacts",
  {
    ...baseColumns,

    // Clio contact fields
    clioId: text().notNull(),
    type: text(), // Person or Company
    first_name: text(),
    last_name: text(),
    title: text(),
    prefix: text(),

    // Name - combined field for easier querying
    name: text().notNull(),

    // Contact details
    email: text(),
    phone: text(),

    // Address
    address: jsonb().$type<{
      street?: string;
      city?: string;
      state?: string;
      country?: string;
      postal_code?: string;
    }>(),

    // Sync metadata
    lastSynced: timestamp(),
    metadata: jsonb().$type<any>(),
  },
  (table) => ({
    ...baseIndexes(table),
    clioIdIdx: index("clio_contact_clio_id_idx").on(table.clioId),
    nameIdx: index("clio_contact_name_idx").on(table.name),
  })
);

export const ClioMatters = pgTable(
  "clio_matters",
  {
    ...baseColumns,

    // Clio matter fields
    clioId: text().notNull(),
    clioClientId: bigint({ mode: "number" }),
    matterName: text().notNull(),
    description: text().notNull(),
    status: text(),
    practiceArea: text(),
    openDate: timestamp(),
    closeDate: timestamp(),

    // Client/contact reference
    localClioContactId: integer().references(() => ClioContacts.id),

    // Sync metadata
    lastSynced: timestamp(),
    metadata: jsonb().$type<any>(),
  },
  (table) => ({
    ...baseIndexes(table),
    clioIdIdx: index("clio_matter_clio_id_idx").on(table.clioId),
    matterNameIdx: index("clio_matter_matter_name_idx").on(table.matterName),
  })
);

// Add relationships
export const ClioMatterRelations = relations(ClioMatters, ({ many, one }) => ({
  intakeRequests: many(IntakeFormResponses),
  cases: many(Cases), // I think this needs to be one
  contact: one(ClioContacts, {
    fields: [ClioMatters.localClioContactId],
    references: [ClioContacts.id],
  }),
}));

export const ClioContactRelations = relations(ClioContacts, ({ many }) => ({
  matters: many(ClioMatters),
}));

// Define schemas
const BaseClioMatterSchema = createSelectSchema(ClioMatters);
export const ClioMatterSchema = BaseClioMatterSchema.extend({
  ...baseSchema,
  lastSynced: z.coerce.date().nullish(),
  openDate: z.coerce.date().nullish(),
  closeDate: z.coerce.date().nullish(),
  contact: z.lazy(() => ClioContactSchema).nullish(),
});

const BaseClioContactSchema = createSelectSchema(ClioContacts);
export const ClioContactSchema = BaseClioContactSchema.extend({
  ...baseSchema,
  lastSynced: z.coerce.date().nullish(),
  address: z
    .object({
      street: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      postal_code: z.string().optional(),
    })
    .optional(),
});

// Create classes
export class ClioMatter extends zodClass.class(ClioMatterSchema.shape) {
  constructor(data: any) {
    const contact =
      data.contact && data.contact.id ? new ClioContact(data.contact) : null;

    super({
      ...data,
      contact,
    });
  }
}

export class ClioContact extends zodClass.class(ClioContactSchema.shape) {
  constructor(data: any) {
    super(data);
  }
}
