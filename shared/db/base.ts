import { getTableName } from "drizzle-orm";
import { index, integer, timestamp } from "drizzle-orm/pg-core";
import { z } from "zod";
export const baseColumns = {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  createdAt: timestamp({ withTimezone: true }).defaultNow(),
  updatedAt: timestamp({ withTimezone: true })
    .defaultNow()
    .$onUpdate(() => new Date()),
  deletedAt: timestamp({ withTimezone: true }),
};

export function baseIndexes(table: any) {
  const tableName = getTableName(table.id.table);

  return {
    createdAt: index(`${tableName}_created_at_index`).on(table.createdAt),
    updatedAt: index(`${tableName}_updated_at_index`).on(table.updatedAt),
    deletedAt: index(`${tableName}_deleted_at_index`).on(table.deletedAt),
  };
}

export const baseSchema = {
  createdAt: z.coerce.date().nullish(),
  updatedAt: z.coerce.date().nullish(),
  deletedAt: z.coerce.date().nullish(),
};
