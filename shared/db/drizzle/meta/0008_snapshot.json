{"id": "a7ca635c-7413-4ba9-a970-b179caa6e6d4", "prevId": "75acc772-2c12-4eb1-9cbb-b3fffd5e3c21", "version": "7", "dialect": "postgresql", "tables": {"public.cases": {"name": "cases", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "cases_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "stage": {"name": "stage", "type": "text", "primaryKey": false, "notNull": false}, "case_number": {"name": "case_number", "type": "text", "primaryKey": false, "notNull": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": false}, "county": {"name": "county", "type": "text", "primaryKey": false, "notNull": false}, "attorney": {"name": "attorney", "type": "text", "primaryKey": false, "notNull": false}, "staff": {"name": "staff", "type": "text", "primaryKey": false, "notNull": false}, "plaintiffs": {"name": "plaintiffs", "type": "text", "primaryKey": false, "notNull": false}, "next_activity_expected_date": {"name": "next_activity_expected_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_pickup_case": {"name": "is_pickup_case", "type": "boolean", "primaryKey": false, "notNull": false}, "is_original_create_date": {"name": "is_original_create_date", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "client_uuid": {"name": "client_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "property_uuid": {"name": "property_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "defendant_uuid": {"name": "defendant_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "integer", "primaryKey": false, "notNull": false}, "defendant_id": {"name": "defendant_id", "type": "integer", "primaryKey": false, "notNull": false}, "opposing_counsel_id": {"name": "opposing_counsel_id", "type": "integer", "primaryKey": false, "notNull": false}, "trial": {"name": "trial", "type": "boolean", "primaryKey": false, "notNull": false}, "client_contact": {"name": "client_contact", "type": "text", "primaryKey": false, "notNull": false}, "court_type": {"name": "court_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'circuit'"}, "has_reciprocal_service_wording": {"name": "has_reciprocal_service_wording", "type": "boolean", "primaryKey": false, "notNull": false}, "first_appearance_terms": {"name": "first_appearance_terms", "type": "text", "primaryKey": false, "notNull": false}, "limited_or_general_judgment": {"name": "limited_or_general_judgment", "type": "text", "primaryKey": false, "notNull": false}, "fee_paid": {"name": "fee_paid", "type": "boolean", "primaryKey": false, "notNull": false}, "general_notes": {"name": "general_notes", "type": "text", "primaryKey": false, "notNull": false}, "issue_summary_sheet": {"name": "issue_summary_sheet", "type": "text", "primaryKey": false, "notNull": false}, "order_or_judgment": {"name": "order_or_judgment", "type": "text", "primaryKey": false, "notNull": false}, "residential_commercial": {"name": "residential_commercial", "type": "text", "primaryKey": false, "notNull": false}, "lease_type": {"name": "lease_type", "type": "text", "primaryKey": false, "notNull": false}, "property_contact": {"name": "property_contact", "type": "text", "primaryKey": false, "notNull": false}, "military_affidavit_notes": {"name": "military_affidavit_notes", "type": "text", "primaryKey": false, "notNull": false}, "noncompliance_desc": {"name": "noncompliance_desc", "type": "text", "primaryKey": false, "notNull": false}, "wa_notice_type": {"name": "wa_notice_type", "type": "text", "primaryKey": false, "notNull": false}, "courtroom_number": {"name": "courtroom_number", "type": "text", "primaryKey": false, "notNull": false}, "additional_defendants": {"name": "additional_defendants", "type": "jsonb", "primaryKey": false, "notNull": false}, "res_notices_or": {"name": "res_notices_or", "type": "jsonb", "primaryKey": false, "notNull": false}, "comm_notices_or": {"name": "comm_notices_or", "type": "jsonb", "primaryKey": false, "notNull": false}, "case_rent_and_fees": {"name": "case_rent_and_fees", "type": "jsonb", "primaryKey": false, "notNull": false}, "case_dates": {"name": "case_dates", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"cases_created_at_index": {"name": "cases_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cases_updated_at_index": {"name": "cases_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cases_property_id_properties_id_fk": {"name": "cases_property_id_properties_id_fk", "tableFrom": "cases", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cases_client_id_clients_id_fk": {"name": "cases_client_id_clients_id_fk", "tableFrom": "cases", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cases_defendant_id_defendants_id_fk": {"name": "cases_defendant_id_defendants_id_fk", "tableFrom": "cases", "tableTo": "defendants", "columnsFrom": ["defendant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cases_opposing_counsel_id_contacts_id_fk": {"name": "cases_opposing_counsel_id_contacts_id_fk", "tableFrom": "cases", "tableTo": "contacts", "columnsFrom": ["opposing_counsel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "clients_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "is_new": {"name": "is_new", "type": "boolean", "primaryKey": false, "notNull": false}, "company_type": {"name": "company_type", "type": "text", "primaryKey": false, "notNull": false}, "client_notes": {"name": "client_notes", "type": "text", "primaryKey": false, "notNull": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"clients_created_at_index": {"name": "clients_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clients_updated_at_index": {"name": "clients_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clients_uuid_unique": {"name": "clients_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "checkConstraints": {}}, "public.contact_clients": {"name": "contact_clients", "schema": "", "columns": {"contact_id": {"name": "contact_id", "type": "integer", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"contact_clients_contact_id_contacts_id_fk": {"name": "contact_clients_contact_id_contacts_id_fk", "tableFrom": "contact_clients", "tableTo": "contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "contact_clients_client_id_clients_id_fk": {"name": "contact_clients_client_id_clients_id_fk", "tableFrom": "contact_clients", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.contacts": {"name": "contacts", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "contacts_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "client_uuid": {"name": "client_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "property_uuid": {"name": "property_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "is_new": {"name": "is_new", "type": "boolean", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "fax": {"name": "fax", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "cell": {"name": "cell", "type": "text", "primaryKey": false, "notNull": false}, "other": {"name": "other", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "bar_number": {"name": "bar_number", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"contacts_created_at_index": {"name": "contacts_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contacts_updated_at_index": {"name": "contacts_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contacts_uuid_unique": {"name": "contacts_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "checkConstraints": {}}, "public.counties": {"name": "counties", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "counties_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "court_address": {"name": "court_address", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "court_name": {"name": "court_name", "type": "text", "primaryKey": false, "notNull": false}, "default_court_type": {"name": "default_court_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'circuit'"}, "fa_time": {"name": "fa_time", "type": "text", "primaryKey": false, "notNull": false}, "service_fee": {"name": "service_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "fa_fee": {"name": "fa_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "filing_fee": {"name": "filing_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "s_and_c_fee": {"name": "s_and_c_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "circuit_fee": {"name": "circuit_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "court_fee": {"name": "court_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "justice_fee": {"name": "justice_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "justice_court": {"name": "justice_court", "type": "jsonb", "primaryKey": false, "notNull": false}, "alternate_court": {"name": "alternate_court", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"counties_created_at_index": {"name": "counties_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "counties_updated_at_index": {"name": "counties_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.defendants": {"name": "defendants", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "defendants_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": false}, "ssn": {"name": "ssn", "type": "text", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "suffix": {"name": "suffix", "type": "text", "primaryKey": false, "notNull": false}, "business_name": {"name": "business_name", "type": "text", "primaryKey": false, "notNull": false}, "is_business": {"name": "is_business", "type": "boolean", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": false}, "use_mailing_address": {"name": "use_mailing_address", "type": "boolean", "primaryKey": false, "notNull": false}, "mailing": {"name": "mailing", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"defendants_created_at_index": {"name": "defendants_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "defendants_updated_at_index": {"name": "defendants_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"defendants_property_id_properties_id_fk": {"name": "defendants_property_id_properties_id_fk", "tableFrom": "defendants", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "documents_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "case_id": {"name": "case_id", "type": "integer", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"documents_created_at_index": {"name": "documents_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documents_updated_at_index": {"name": "documents_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.efile_operations": {"name": "efile_operations", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "efile_operations_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": false}, "case_uuid": {"name": "case_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "defendant_uuid": {"name": "defendant_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": false}, "case_id": {"name": "case_id", "type": "integer", "primaryKey": false, "notNull": false}, "defendant_id": {"name": "defendant_id", "type": "integer", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "integer", "primaryKey": false, "notNull": false}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "case_number": {"name": "case_number", "type": "text", "primaryKey": false, "notNull": false}, "defendant_name": {"name": "defendant_name", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "bot_id": {"name": "bot_id", "type": "text", "primaryKey": false, "notNull": false}, "e_file_url": {"name": "e_file_url", "type": "text", "primaryKey": false, "notNull": false}, "status_message": {"name": "status_message", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"efile_operations_created_at_index": {"name": "efile_operations_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "efile_operations_updated_at_index": {"name": "efile_operations_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"efile_operations_case_id_cases_id_fk": {"name": "efile_operations_case_id_cases_id_fk", "tableFrom": "efile_operations", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "efile_operations_defendant_id_defendants_id_fk": {"name": "efile_operations_defendant_id_defendants_id_fk", "tableFrom": "efile_operations", "tableTo": "defendants", "columnsFrom": ["defendant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "efile_operations_client_id_clients_id_fk": {"name": "efile_operations_client_id_clients_id_fk", "tableFrom": "efile_operations", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "efile_operations_property_id_properties_id_fk": {"name": "efile_operations_property_id_properties_id_fk", "tableFrom": "efile_operations", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "efile_operations_user_id_users_id_fk": {"name": "efile_operations_user_id_users_id_fk", "tableFrom": "efile_operations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"efile_operations_uuid_unique": {"name": "efile_operations_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "checkConstraints": {}}, "public.intake_form_responses": {"name": "intake_form_responses", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "intake_form_responses_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "tenant_name": {"name": "tenant_name", "type": "text", "primaryKey": false, "notNull": false}, "property_name": {"name": "property_name", "type": "text", "primaryKey": false, "notNull": false}, "form_name": {"name": "form_name", "type": "text", "primaryKey": false, "notNull": false}, "client": {"name": "client", "type": "text", "primaryKey": false, "notNull": false}, "unit_number": {"name": "unit_number", "type": "text", "primaryKey": false, "notNull": false}, "clio_folder_id": {"name": "clio_folder_id", "type": "numeric(25, 0)", "primaryKey": false, "notNull": false}, "form_entry_id": {"name": "form_entry_id", "type": "text", "primaryKey": false, "notNull": false}, "form_data": {"name": "form_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "submitted_date": {"name": "submitted_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"intake_form_responses_created_at_index": {"name": "intake_form_responses_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "intake_form_responses_updated_at_index": {"name": "intake_form_responses_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.contact_properties": {"name": "contact_properties", "schema": "", "columns": {"contact_id": {"name": "contact_id", "type": "integer", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"contact_properties_contact_id_contacts_id_fk": {"name": "contact_properties_contact_id_contacts_id_fk", "tableFrom": "contact_properties", "tableTo": "contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "contact_properties_property_id_properties_id_fk": {"name": "contact_properties_property_id_properties_id_fk", "tableFrom": "contact_properties", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "properties_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "text", "primaryKey": false, "notNull": false}, "client_uuid": {"name": "client_uuid", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "integer", "primaryKey": false, "notNull": false}, "action_services": {"name": "action_services", "type": "text", "primaryKey": false, "notNull": false}, "property_notes": {"name": "property_notes", "type": "text", "primaryKey": false, "notNull": false}, "address1": {"name": "address1", "type": "text", "primaryKey": false, "notNull": false}, "address2": {"name": "address2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": false}, "county": {"name": "county", "type": "text", "primaryKey": false, "notNull": false}, "is_new": {"name": "is_new", "type": "boolean", "primaryKey": false, "notNull": false}, "do_not_use": {"name": "do_not_use", "type": "boolean", "primaryKey": false, "notNull": false}, "is_home_forward": {"name": "is_home_forward", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"properties_created_at_index": {"name": "properties_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_updated_at_index": {"name": "properties_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"properties_uuid_unique": {"name": "properties_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "checkConstraints": {}}, "public.secure_data": {"name": "secure_data", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "secure_data_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "iv": {"name": "iv", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_data": {"name": "encrypted_data", "type": "text", "primaryKey": false, "notNull": true}, "auth_tag": {"name": "auth_tag", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"secure_data_created_at_index": {"name": "secure_data_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "secure_data_updated_at_index": {"name": "secure_data_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sid": {"name": "sid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "sess": {"name": "sess", "type": "json", "primaryKey": false, "notNull": false}, "expire": {"name": "expire", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"IDX_session_expire": {"name": "IDX_session_expire", "columns": [{"expression": "expire", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "settings_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"settings_created_at_index": {"name": "settings_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "settings_updated_at_index": {"name": "settings_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"settings_key_unique": {"name": "settings_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "checkConstraints": {}}, "public.states": {"name": "states", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "states_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"states_created_at_index": {"name": "states_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "states_updated_at_index": {"name": "states_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "templates_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "county": {"name": "county", "type": "text", "primaryKey": false, "notNull": false}, "court_type": {"name": "court_type", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "download_format": {"name": "download_format", "type": "text", "primaryKey": false, "notNull": false}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "primaryKey": false, "notNull": false}, "usage": {"name": "usage", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"templates_created_at_index": {"name": "templates_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_updated_at_index": {"name": "templates_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "users_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "initials": {"name": "initials", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "is_attorney": {"name": "is_attorney", "type": "boolean", "primaryKey": false, "notNull": false}, "osb_number": {"name": "osb_number", "type": "text", "primaryKey": false, "notNull": false}, "wsb_number": {"name": "wsb_number", "type": "text", "primaryKey": false, "notNull": false}, "mfa_pin": {"name": "mfa_pin", "type": "text", "primaryKey": false, "notNull": false}, "file_and_serve_email": {"name": "file_and_serve_email", "type": "text", "primaryKey": false, "notNull": false}, "file_and_serve_password": {"name": "file_and_serve_password", "type": "text", "primaryKey": false, "notNull": false}, "is_disabled": {"name": "is_disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_admin": {"name": "is_admin", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_non_staff": {"name": "is_non_staff", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"users_created_at_index": {"name": "users_created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_updated_at_index": {"name": "users_updated_at_index", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_uid_unique": {"name": "users_uid_unique", "nullsNotDistinct": false, "columns": ["uid"]}}, "checkConstraints": {}}}, "enums": {"public.status": {"name": "status", "schema": "public", "values": ["pending", "failed", "complete"]}}, "schemas": {}, "sequences": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}