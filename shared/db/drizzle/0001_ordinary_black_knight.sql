ALTER TABLE "efile_operations" ALTER COLUMN "uuid" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "is_disabled" SET DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "is_archived" SET DEFAULT false;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "is_admin" SET DEFAULT false;--> statement-breakpoint
ALTER TABLE "cases" ADD COLUMN "courtroom_number" text;--> statement-breakpoint
ALTER TABLE "templates" ADD COLUMN "county" text;--> statement-breakpoint
ALTER TABLE "templates" ADD COLUMN "court_type" text;--> statement-breakpoint
ALTER TABLE "templates" ADD COLUMN "category" text;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "cases_created_at_index" ON "cases" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "cases_updated_at_index" ON "cases" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "clients_created_at_index" ON "clients" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "clients_updated_at_index" ON "clients" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "contacts_created_at_index" ON "contacts" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "contacts_updated_at_index" ON "contacts" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "counties_created_at_index" ON "counties" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "counties_updated_at_index" ON "counties" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "defendants_created_at_index" ON "defendants" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "defendants_updated_at_index" ON "defendants" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "documents_created_at_index" ON "documents" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "documents_updated_at_index" ON "documents" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "efile_operations_created_at_index" ON "efile_operations" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "efile_operations_updated_at_index" ON "efile_operations" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "intake_form_responses_created_at_index" ON "intake_form_responses" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "intake_form_responses_updated_at_index" ON "intake_form_responses" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "properties_created_at_index" ON "properties" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "properties_updated_at_index" ON "properties" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_created_at_index" ON "settings" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_updated_at_index" ON "settings" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "states_created_at_index" ON "states" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "states_updated_at_index" ON "states" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "templates_created_at_index" ON "templates" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "templates_updated_at_index" ON "templates" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_created_at_index" ON "users" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_updated_at_index" ON "users" USING btree ("updated_at");