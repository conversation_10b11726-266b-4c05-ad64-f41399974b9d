ALTER TABLE "efile_operations" ADD COLUMN "client_id" integer;--> statement-breakpoint
ALTER TABLE "efile_operations" ADD COLUMN "property_id" integer;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "file_and_serve_email" text;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "efile_operations" ADD CONSTRAINT "efile_operations_client_id_clients_id_fk" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "efile_operations" ADD CONSTRAINT "efile_operations_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
