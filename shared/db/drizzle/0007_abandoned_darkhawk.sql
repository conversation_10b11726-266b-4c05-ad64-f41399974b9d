CREATE TABLE IF NOT EXISTS "secure_data" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "secure_data_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"iv" text NOT NULL,
	"encrypted_data" text NOT NULL,
	"auth_tag" text NOT NULL
);
--> statement-breakpoint
ALTER TABLE "cases" ALTER COLUMN "is_original_create_date" SET DEFAULT true;--> statement-breakpoint
ALTER TABLE "defendants" ADD COLUMN "dob" text;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "is_non_staff" boolean DEFAULT false;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "secure_data_created_at_index" ON "secure_data" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "secure_data_updated_at_index" ON "secure_data" USING btree ("updated_at");