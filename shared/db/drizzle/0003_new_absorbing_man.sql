ALTER TABLE "intake_form_responses" ALTER COLUMN "submitted_date" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "defendants" ADD COLUMN "property_id" integer;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "do_not_use" boolean;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "is_home_forward" boolean;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "defendants" ADD CONSTRAINT "defendants_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
