CREATE TABLE IF NOT EXISTS "email_templates" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "email_templates_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"deleted_at" timestamp with time zone,
	"name" text,
	"purpose" text,
	"cc" jsonb,
	"bcc" jsonb,
	"subject" text,
	"body" text
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "email_templates_created_at_index" ON "email_templates" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "email_templates_updated_at_index" ON "email_templates" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "email_templates_deleted_at_index" ON "email_templates" USING btree ("deleted_at");