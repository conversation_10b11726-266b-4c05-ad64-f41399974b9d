CREATE TYPE "public"."status" AS ENUM('pending', 'failed', 'complete');--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "cases" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "cases_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"status" text,
	"stage" text,
	"case_number" text,
	"address1" text,
	"address2" text,
	"city" text,
	"state" text,
	"zip" text,
	"county" text,
	"attorney" text,
	"staff" text,
	"plaintiffs" text,
	"next_activity_expected_date" timestamp,
	"is_pickup_case" boolean,
	"is_original_create_date" boolean,
	"uuid" uuid,
	"client_uuid" uuid,
	"property_uuid" uuid,
	"defendant_uuid" uuid,
	"property_id" integer,
	"client_id" integer,
	"defendant_id" integer,
	"trial" boolean,
	"client_contact" text,
	"has_reciprocal_service_wording" boolean,
	"first_appearance_terms" text,
	"limited_or_general_judgment" text,
	"fee_paid" boolean,
	"general_notes" text,
	"issue_summary_sheet" text,
	"order_or_judgment" text,
	"residential_commercial" text,
	"lease_type" text,
	"property_contact" text,
	"military_affidavit_notes" text,
	"noncompliance_desc" text,
	"wa_notice_type" text,
	"additional_defendants" jsonb,
	"res_notices_or" jsonb,
	"comm_notices_or" jsonb,
	"case_rent_and_fees" jsonb,
	"case_dates" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "clients" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "clients_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" uuid,
	"name" text,
	"is_new" boolean,
	"company_type" text,
	"client_notes" text,
	"address1" text,
	"address2" text,
	"city" text,
	"state" text,
	"zip" text,
	CONSTRAINT "clients_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contact_clients" (
	"contact_id" integer NOT NULL,
	"client_id" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contacts" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "contacts_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" uuid,
	"client_uuid" uuid,
	"property_uuid" uuid,
	"is_new" boolean,
	"name" text,
	"title" text,
	"email" text,
	"fax" text,
	"phone" text,
	"cell" text,
	"other" text,
	"notes" text,
	CONSTRAINT "contacts_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "counties" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "counties_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" text NOT NULL,
	"name" text,
	"court_address" text,
	"state" text,
	"court_name" text,
	"fa_time" text,
	"service_fee" numeric(10, 2),
	"fa_fee" numeric(10, 2),
	"filing_fee" numeric(10, 2),
	"s_and_c_fee" numeric(10, 2),
	"circuit_fee" numeric(10, 2),
	"court_fee" numeric(10, 2),
	"justice_fee" numeric(10, 2),
	"justice_court" jsonb,
	"alternate_court" jsonb,
	CONSTRAINT "counties_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "defendants" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "defendants_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" uuid,
	"ssn" text,
	"status" text,
	"first_name" text,
	"middle_name" text,
	"last_name" text,
	"suffix" text,
	"business_name" text,
	"is_business" boolean,
	"phone" text,
	"address1" text,
	"address2" text,
	"city" text,
	"state" text,
	"zip" text,
	"use_mailing_address" boolean,
	"mailing" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "documents" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "documents_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"case_id" integer,
	"file_name" text,
	"file_path" text,
	"status" "status"
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "efile_operations" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "efile_operations_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" text NOT NULL,
	"case_uuid" uuid,
	"defendant_uuid" uuid,
	"user_uid" text,
	"case_id" integer,
	"defendant_id" integer,
	"user_id" integer,
	"case_number" text,
	"defendant_name" text,
	"timestamp" timestamp with time zone,
	"bot_id" text,
	"e_file_url" text,
	"status_message" text,
	"status" text,
	CONSTRAINT "efile_operations_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "intake_form_responses" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "intake_form_responses_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"tenant_name" text,
	"property_name" text,
	"form_name" text,
	"client" text,
	"unit_number" text,
	"clio_folder_id" numeric(25, 0),
	"form_entry_id" text,
	"form_data" jsonb,
	"submitted_date" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contact_properties" (
	"contact_id" integer NOT NULL,
	"property_id" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "properties" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "properties_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" uuid,
	"name" text,
	"client_name" text,
	"client_uuid" uuid,
	"client_id" integer,
	"action_services" text,
	"property_notes" text,
	"address1" text,
	"address2" text,
	"city" text,
	"state" text,
	"zip" text,
	"county" text,
	"is_new" boolean,
	CONSTRAINT "properties_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "sessions" (
	"sid" varchar PRIMARY KEY NOT NULL,
	"sess" json,
	"expire" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "settings" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "settings_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"key" text NOT NULL,
	"value" jsonb,
	CONSTRAINT "settings_key_unique" UNIQUE("key")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "states" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "states_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"name" text,
	"full_name" text
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "templates" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "templates_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uuid" text NOT NULL,
	"name" text,
	"state" text,
	"download_format" text,
	"custom_fields" jsonb,
	"usage" jsonb,
	CONSTRAINT "templates_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "users_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"uid" text,
	"name" text,
	"initials" text,
	"email" text,
	"is_attorney" boolean,
	"osb_number" text,
	"wsb_number" text,
	"mfa_pin" text,
	"file_and_serve_password" text,
	"is_disabled" boolean,
	"is_archived" boolean,
	"is_admin" boolean,
	CONSTRAINT "users_uid_unique" UNIQUE("uid")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_client_id_clients_id_fk" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_defendant_id_defendants_id_fk" FOREIGN KEY ("defendant_id") REFERENCES "public"."defendants"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "contact_clients" ADD CONSTRAINT "contact_clients_contact_id_contacts_id_fk" FOREIGN KEY ("contact_id") REFERENCES "public"."contacts"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "contact_clients" ADD CONSTRAINT "contact_clients_client_id_clients_id_fk" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "efile_operations" ADD CONSTRAINT "efile_operations_case_id_cases_id_fk" FOREIGN KEY ("case_id") REFERENCES "public"."cases"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "efile_operations" ADD CONSTRAINT "efile_operations_defendant_id_defendants_id_fk" FOREIGN KEY ("defendant_id") REFERENCES "public"."defendants"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "efile_operations" ADD CONSTRAINT "efile_operations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "contact_properties" ADD CONSTRAINT "contact_properties_contact_id_contacts_id_fk" FOREIGN KEY ("contact_id") REFERENCES "public"."contacts"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "contact_properties" ADD CONSTRAINT "contact_properties_property_id_properties_id_fk" FOREIGN KEY ("property_id") REFERENCES "public"."properties"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "sessions" USING btree ("expire");