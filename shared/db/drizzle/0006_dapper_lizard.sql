ALTER TABLE "cases" ADD COLUMN "opposing_counsel_id" integer;--> statement-breakpoint
ALTER TABLE "contacts" ADD COLUMN "type" text;--> statement-breakpoint
ALTER TABLE "contacts" ADD COLUMN "bar_number" text;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_opposing_counsel_id_contacts_id_fk" FOREIGN KEY ("opposing_counsel_id") REFERENCES "public"."contacts"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
