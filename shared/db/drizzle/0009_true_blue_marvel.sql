ALTER TABLE "cases" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "clients" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "contacts" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "counties" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "defendants" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "documents" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "efile_operations" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "intake_form_responses" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "properties" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "secure_data" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "settings" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "states" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "templates" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "cases_deleted_at_index" ON "cases" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "clients_deleted_at_index" ON "clients" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "contacts_deleted_at_index" ON "contacts" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "counties_deleted_at_index" ON "counties" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "defendants_deleted_at_index" ON "defendants" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "documents_deleted_at_index" ON "documents" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "efile_operations_deleted_at_index" ON "efile_operations" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "intake_form_responses_deleted_at_index" ON "intake_form_responses" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "properties_deleted_at_index" ON "properties" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "secure_data_deleted_at_index" ON "secure_data" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "settings_deleted_at_index" ON "settings" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "states_deleted_at_index" ON "states" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "templates_deleted_at_index" ON "templates" USING btree ("deleted_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "users_deleted_at_index" ON "users" USING btree ("deleted_at");