import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
dayjs.extend(customParseFormat);

import DOMPurify from "dompurify";
import { map } from "lodash";
import tinycolor from "tinycolor2";

export const stopPropagation = (method?: Function) => (event) => {
  event.stopPropagation();
  if (method) return method(event);
};

export const preventDefault = (method?: Function) => (event) => {
  event.preventDefault();
  if (method) return method(event);
};

export const fireOnEnter = (method) => (event) => {
  if (event.keyCode == 13 && event.shiftKey == false) {
    event.preventDefault();
    method();
  }
};

export function getRelativeText(
  background,
  { light, dark } = { dark: "#ffffff", light: "#212529" }
) {
  const isLight = tinycolor(background).getBrightness() > 150;

  return isLight ? light : dark;
}

export function delay(timeout) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(null);
    }, timeout); // 5000 ms = 5 seconds
  });
}

export function constToOptions(constants) {
  return map(constants, (value) => ({
    value: value,
    label: value,
  }));
}

export function objectToOptions(constants) {
  return map(constants, (value, key) => ({
    value: key,
    label: value,
  }));
}

export function stringToHtml(
  string: string = "",
  options: { doubleSpace: boolean } = { doubleSpace: false }
) {
  const lineBreak = options.doubleSpace ? "<br /><br />" : "<br />";
  const htmlString = string.trim().replace(/\n/g, lineBreak);
  const sanitizedString = DOMPurify.sanitize(htmlString);

  return sanitizedString;
}

export function urlEncodeAddress({ address1, address2, city, state, zip }) {
  return `${address1} ${address2} ${city} ${state} ${zip}`.replace(/ /g, "+");
}

// Expects timeStr to be in the form of "h:mm A"
export function convertTimeStringToDate(
  timeStr: string | null | undefined
): Date | null {
  if (!timeStr) return null;

  const today = dayjs().format("YYYY-MM-DD");

  const dateTime = dayjs(`${today} ${timeStr}`, "YYYY-MM-DD h:mm A");

  return dateTime.toDate();
}

export function convertDateToTimeString(date: Date): string | null {
  if (!date) return null;

  return dayjs(date).format("h:mm A");
}
