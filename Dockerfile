# BUILDER
FROM node:20 AS builder

# Build app
WORKDIR /app
ENV NODE_ENV=development
COPY . .
RUN npm install
ENV NODE_ENV=production
RUN npm run build-api
RUN npm run build-web


# PRODUCTION
FROM node:20

# Install chromium
RUN apt-get update -qq && apt-get install -qq --no-install-recommends chromium
RUN rm -rf /var/lib/apt/lists/*
RUN ln -s /usr/bin/chromium /usr/bin/google-chrome

# Copy our files
WORKDIR /app
COPY . .

# Setup app
ENV NODE_ENV=production
RUN npm install
COPY --from=builder /app/dist /app/dist