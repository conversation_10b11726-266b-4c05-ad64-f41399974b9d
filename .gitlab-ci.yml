image: node:20

stages:
  - tag
  - deploy

before_script:
  - "which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )"
  - eval $(ssh-agent -s)
  - ssh-add <(echo "$SSH_PRIVATE_KEY")
  - mkdir -p ~/.ssh
  - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  - git fetch --unshallow origin

tag_for_deployment:
  stage: tag
  only:
    - main
  script:
    - git tag `date +%Y.%m.%d`.$CI_JOB_ID
    - <NAME_EMAIL>:$CI_PROJECT_PATH --tags

deploy_to_test:
  stage: deploy
  when: manual
  environment:
    name: test
    url: https://test.app.andor-law.com
  script: git push -f <EMAIL>:andor-case-manager-test HEAD:refs/heads/master

deploy_to_test_2:
  stage: deploy
  when: manual
  environment:
    name: test-2
    url: https://test-2.app.andor-law.com
  script: git push -f <EMAIL>:andor-case-manager-test-2 HEAD:refs/heads/master

deploy_to_beta:
  stage: deploy
  when: manual
  environment:
    name: beta
    url: https://beta.app.andor-law.com
  script: git push -f <EMAIL>:andor-case-manager-beta HEAD:refs/heads/master

deploy_to_live:
  stage: deploy
  when: manual
  only:
    - tags
  environment:
    name: live
    url: https://app.andor-law.com
  script: git push -f <EMAIL>:andor-case-manager-live HEAD:refs/heads/master
