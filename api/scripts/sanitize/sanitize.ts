import dotenv from "dotenv";
dotenv.config();

import { db } from "@api/src/services/db";
import { faker } from "@faker-js/faker";
import { Cases } from "@shared/db/schema/cases";
import { Clients } from "@shared/db/schema/clients";
import { Contacts } from "@shared/db/schema/contacts";
import { Defendants } from "@shared/db/schema/defendants";
import { Properties } from "@shared/db/schema/properties";
import { Settings } from "@shared/db/schema/settings";
import { Users } from "@shared/db/schema/users";
import { asc, eq, sql } from "drizzle-orm";
import { map, upperCase } from "lodash";
import fakeCompanyNames from "./fake-company-names";
import fakePropertyNames from "./fake-property-names";

async function updateFakeData() {
  console.log(`Start sanitize: ${new Date().toISOString()}`);

  // DEFENDANTS
  const defendants = await db
    .select({
      id: Defendants.id,
    })
    .from(Defendants)
    .orderBy(asc(Defendants.id));

  for (const defendant of defendants) {
    const fakeFirstName = faker.person.firstName();
    const fakeLastName = faker.person.lastName();

    const variable = Math.random();
    let fakeMiddleNameOrInitial = "";

    if (variable < 0.1) {
      fakeMiddleNameOrInitial = faker.person.middleName();
    } else if (variable < 0.5) {
      fakeMiddleNameOrInitial = faker.person.middleName().charAt(0) + ".";
    }

    let fakeSSN: string | null = null;

    if (variable < 0.5) fakeSSN = faker.string.numeric(9);

    await db
      .update(Defendants)
      .set({
        firstName: fakeFirstName,
        lastName: fakeLastName,
        middleName: fakeMiddleNameOrInitial,
        ssn: fakeSSN,
      })
      .where(eq(Defendants.id, defendant.id));
  }

  // CASES
  const cases = await db
    .select({
      id: Cases.id,
      additionalDefendants: Cases.additionalDefendants,
    })
    .from(Cases)
    .orderBy(asc(Cases.id));

  for (const caseData of cases) {
    const fakedAdditionalDefendants = map(
      caseData.additionalDefendants,
      (defendant) => {
        const fakeFirstName = faker.person.firstName();
        const fakeLastName = faker.person.lastName();
        const variable = Math.random();
        let fakeMiddleNameOrInitial = "";

        if (variable < 0.1) {
          fakeMiddleNameOrInitial = faker.person.middleName();
        } else if (variable < 0.5) {
          fakeMiddleNameOrInitial = faker.person.middleName().charAt(0) + ".";
        }

        let fakeSSN = "";

        if (variable < 0.5) fakeSSN = faker.string.numeric(9);

        return {
          ...defendant,
          firstName: fakeFirstName,
          lastName: fakeLastName,
          middleName: fakeMiddleNameOrInitial,
          ssn: fakeSSN,
          suffix: "",
        };
      }
    );

    const fakePrefix = faker.string.numeric({ length: 2 });
    const fakeText = faker.string.alpha({ length: 2, casing: "upper" });
    const fakeSuffix = faker.string.numeric({ length: 5 });
    const fakeCaseNumber = `${fakePrefix}${fakeText}${fakeSuffix}`;

    await db
      .update(Cases)
      .set({
        caseNumber: fakeCaseNumber,
        additionalDefendants: fakedAdditionalDefendants,
      })
      .where(eq(Cases.id, caseData.id));
  }

  // USERS
  const users = await db
    .select({
      id: Users.id,
    })
    .from(Users)
    .orderBy(asc(Users.id));

  for (const user of users) {
    const fakeFileAndServePassword = faker.string.alphanumeric(15);
    const fakeMfaPin = faker.string.alphanumeric({
      length: 16,
      casing: "upper",
    });

    await db
      .update(Users)
      .set({
        fileAndServePassword: fakeFileAndServePassword,
        mfaPin: fakeMfaPin,
      })
      .where(eq(Users.id, user.id));
  }

  // CLIENTS
  const clients = await db
    .select({
      id: Clients.id,
    })
    .from(Clients)
    .orderBy(asc(Clients.id));

  for (const [index, client] of clients.entries()) {
    const fakeName = fakeCompanyNames[index];

    await db
      .update(Clients)
      .set({
        name: fakeName,
      })
      .where(eq(Clients.id, client.id));
  }

  // PROPERTIES
  const properties = await db
    .select({
      id: Properties.id,
      clientId: Properties.clientId,
    })
    .from(Properties)
    .orderBy(asc(Properties.id));

  for (const [index, property] of properties.entries()) {
    const fakeName = fakePropertyNames[index];
    const client = await db
      .select({ name: Clients.name })
      .from(Clients)
      .where(eq(Clients.id, property.clientId!));

    await db
      .update(Properties)
      .set({
        name: fakeName,
        clientName: upperCase(client[0].name as string),
      })
      .where(eq(Properties.id, property.id));
  }

  // Contacts
  const contacts = await db
    .select({
      id: Contacts.id,
    })
    .from(Contacts)
    .orderBy(asc(Contacts.id));

  for (const [index, contact] of contacts.entries()) {
    await db
      .update(Contacts)
      .set({
        name: faker.person.fullName(),
        email: faker.internet.email(),
        other: Math.random() < 0.02 ? faker.internet.email() : null,
        cell:
          Math.random() < 0.1
            ? faker.phone.number({ style: "national" })
            : null,
        fax:
          Math.random() < 0.02
            ? faker.phone.number({ style: "national" })
            : null,
        phone:
          Math.random() < 0.6
            ? faker.phone.number({ style: "national" })
            : null,
      })
      .where(eq(Contacts.id, contact.id));
  }

  // Efile
  await db.execute(
    sql`delete from efile_operations where defendant_name not ilike '%John Doe%'`
  );

  // Intake
  await db.execute(
    sql`TRUNCATE TABLE intake_form_responses RESTART IDENTITY CASCADE`
  );

  // Settings
  await db
    .update(Settings)
    .set({ value: { tokens: {}, folders: {} } })
    .where(eq(Settings.key, "clio"));

  console.log(`End sanitize: ${new Date().toISOString()}`);
}

updateFakeData();
