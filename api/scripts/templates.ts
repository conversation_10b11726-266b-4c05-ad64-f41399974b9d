import dotenv from "dotenv";
dotenv.config();

import {
  // createDocRecord,
  getTemplate,
  getTemplates,
} from "@api/src/services/templates";

async function testGetTemplates() {
  const templates = await getTemplates();
  console.log(
    `
    ===================================================================================================================
                                            TEST TEMPLATES
    ===================================================================================================================\n`,
    templates
  );
}
testGetTemplates();

async function testGetTemplate() {
  const template = await getTemplate(1);
  console.log(
    `
    ===================================================================================================================
                                            TEST GET TEMPLATE
    ===================================================================================================================\n`,
    template
  );
}

// async function testRenderTemplate() {
//   const caseData = await collectData(42275);
//   if (!caseData) {
//     console.log({ error: "Case not found" });
//     return;
//   }
//   const template = await getTemplate(1);
//   if (!template) {
//     console.log({ error: "Template not found" });
//     return;
//   }
//   const directoryPath = `./documents/${caseData.case.caseNumber}`;
//   const fileName = `${template.name}.${template.downloadFormat}`;
//   const filePath = path.join(directoryPath, fileName);

//   console.log(
//     `
//     ===================================================================================================================
//                                             TEST RENDERING TEMPLATES
//     ===================================================================================================================\n`,
//     template
//   );

//   await createDocRecord(caseData?.case.id, fileName, filePath);
//   await renderDocument({
//     caseData,
//     template,
//     documentsDirectory: directoryPath,
//     documentName: fileName,
//     storagePath: filePath,
//   });
// }
// testRenderTemplate();
