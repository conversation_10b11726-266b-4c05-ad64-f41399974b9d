import dotenv from "dotenv";
dotenv.config();

import { MigrationConfig, readMigrationFiles } from "drizzle-orm/migrator";
import pg from "pg";
import original_config from "../../drizzle.config";

const { Client } = pg;
const client = new Client({ connectionString: process.env.DATABASE_URL! });

const config = {
  ...original_config,
  migrationsFolder: original_config.out,
  migrationsTable: original_config.migrations?.table ?? "__drizzle_migrations",
  migrationsSchema: original_config.migrations?.schema ?? "drizzle",
} as MigrationConfig;

const migrations = readMigrationFiles(config);

const table_name = `${config.migrationsSchema}.${config.migrationsTable}`;

async function main() {
  await client.connect();
  const migrationRecords = await client.query(
    `SELECT id, hash, created_at FROM ${table_name}`
  );
  const db_migrations_hashs = migrationRecords.rows.map((m) => {
    return m.hash as string;
  });

  for (const migration of migrations) {
    if (!db_migrations_hashs.includes(migration.hash)) {
      const new_db_migration = {
        hash: migration.hash,
        created_at: migration.folderMillis,
      };

      console.log(`-------------migration--------------`, new_db_migration);

      await client.query(
        `insert into ${table_name} (hash, created_at) values ($1, $2)`,
        [migration.hash, migration.folderMillis]
      );
    }
  }
}

main().finally(() => process.exit(0));
