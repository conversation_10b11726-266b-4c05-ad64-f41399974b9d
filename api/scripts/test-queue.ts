import dotenv from "dotenv";
dotenv.config();

import { boss } from "@api/src/services/boss";

async function runStuff() {
  // await initializeBoss();
  await boss.start();

  // Create Schedules.
  // await boss.schedule("refresh_token", "0 1 * * *", null!, {
  //   tz: "America/Los_Angeles",
  // }); // At 1 AM every day
  // await boss.schedule("flush_logs", "*/10 * * * *", null!, {
  //   tz: "America/Los_Angeles",
  // }); // Every 10 minutes

  await boss.send("efiling", {});
}

runStuff();
