import { db } from "@api/src/services/db";
import { Settings } from "@shared/db/schema/settings";
import { eq } from "drizzle-orm";
import express, { Request, Response } from "express";
import { isPlainObject } from "lodash";

export const settingsRoutes = express.Router();

settingsRoutes.get("/:key", async (req: Request, res: Response) => {
  const key = req.params.key;

  if (!key) {
    res.status(404).json({ error: "Setting not found" });
    return;
  }

  const setting = await db.query.settings.findFirst({
    where: eq(Settings.key, key),
  });

  res.json(setting);
});

settingsRoutes.put("/:key", async (req: Request, res: Response) => {
  const key = req.params.key;
  const value = req.body;

  if (!key) {
    res.status(404).json({ error: "Setting not found" });
    return;
  }

  const settings = await db.query.settings.findFirst({
    where: eq(Settings.key, key),
  });

  const oldValue = settings?.value;
  let newValue = value;

  if (isPlainObject(oldValue)) {
    newValue = {
      ...(oldValue as object),
      ...value,
    };
  }

  await db
    .update(Settings)
    .set({ value: newValue })
    .where(eq(Settings.key, key));

  res.json({ success: true });
});
