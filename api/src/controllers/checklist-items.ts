import { db } from "@api/src/services/db";
import {
  ChecklistItems,
  ChecklistItemUpdateSchema,
} from "@shared/db/schema/checklist-items";
import { Comments } from "@shared/db/schema/comments";
import { asc, eq } from "drizzle-orm";
import express, { Request, Response } from "express";

export const checklistItemsRoutes = express.Router();

checklistItemsRoutes.post("", async (req: Request, res: Response) => {
  const newChecklistItemData = req.body || {};
  const result = await db
    .insert(ChecklistItems)
    .values(newChecklistItemData)
    .returning({
      id: ChecklistItems.id,
    });
  const newChecklistItemId = result[0]?.id;

  const checklistItem = await db.query.comments.findFirst({
    where: eq(ChecklistItems.id, newChecklistItemId),
  });

  res.json(checklistItem);
});

checklistItemsRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};
  const parsedChanges = ChecklistItemUpdateSchema.parse(changes);

  const checklistItemResult = await db
    .update(ChecklistItems)
    .set(parsedChanges)
    .where(eq(ChecklistItems.id, id))
    .returning({
      id: ChecklistItems.id,
      createdAt: ChecklistItems.createdAt,
      updatedAt: ChecklistItems.updatedAt,
      deletedAt: ChecklistItems.deletedAt,
      caseId: ChecklistItems.caseId,
      completedById: ChecklistItems.completedById,
      description: ChecklistItems.description,
      status: ChecklistItems.status,
      stage: ChecklistItems.stage,
      type: ChecklistItems.type,
      order: ChecklistItems.order,
      complete: ChecklistItems.complete,
      dateComplete: ChecklistItems.completedDate,
    });

  const checklistItemId = checklistItemResult[0]?.id;

  const checklistItem = await db.query.checklistItems.findFirst({
    where: eq(ChecklistItems.id, checklistItemId),
  });

  await db.insert(Comments).values({
    type: "auto",
    description: `${checklistItem?.description} ${
      checklistItem?.complete ? "marked complete" : "marked incomplete"
    }`,
    caseId: checklistItem?.caseId,
    staffId: req.session?.user?.id,
  });

  res.json(checklistItem);
});

checklistItemsRoutes.get("", async (req: Request, res: Response) => {
  const { caseId } = req.query || {};
  const checklistItems = await db.query.checklistItems.findMany({
    where: eq(ChecklistItems.caseId, Number(caseId)),
    orderBy: [asc(ChecklistItems.order)],
    with: {
      completedBy: true,
    },
  });

  res.json({ checklistItems });
});
