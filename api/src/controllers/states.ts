import { State, States } from "@shared/db/schema/states";
import { eq } from "drizzle-orm";
import express, { Request, Response } from "express";
import { db } from "../services/db";

export const stateRoutes = express.Router();

stateRoutes.post("/", async (req: Request, res: Response) => {
  const { fullName, name } = req.body;
  const insertedRecords = await db
    .insert(States)
    .values({
      fullName,
      name,
    })
    .returning();

  if (!insertedRecords[0]) {
    res.status(404).json({ error: "Could not create state record" });
    return;
  }

  res.json(new State(insertedRecords[0]));
});

stateRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};
  await db.update(States).set(changes).where(eq(States.id, id));

  if (!id) {
    res.status(404).json({ error: "State record not found" });
    return;
  }

  res.json({ success: true });
});
