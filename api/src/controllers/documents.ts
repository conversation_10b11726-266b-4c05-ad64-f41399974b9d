import { db } from "@api/src/services/db";
import { Documents } from "@shared/db/schema/documents";
import {
  and,
  DBQueryConfig,
  desc,
  eq,
  ilike,
  inArray,
  or,
  SQL,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";

export const documentRoutes = express.Router();

documentRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const ids = req.query.ids as string[];
  const caseId = req.query.caseId ? Number(req.query.caseId) : null;

  if (ids) where.push(inArray(Documents.id, (ids as string[]).map(Number)));
  if (caseId) where.push(eq(Documents.caseId, caseId));

  const search = req.query.search as string;
  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(or(ilike(Documents.fileName, `%${term}%`)));
    }
  }

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: desc(Documents.createdAt),
    where: and(...where),
  };

  const count = await db.$count(Documents, and(...where));
  const documents = await db.query.documents.findMany(queryConfig);

  res.json({ documents, count });
});
