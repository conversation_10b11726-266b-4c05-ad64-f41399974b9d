import { db } from "@api/src/services/db";
import { decrypt, encrypt } from "@api/src/services/encryption";
import { Defendants } from "@shared/db/schema/defendants";
import {
  and,
  asc,
  DBQueryConfig,
  eq,
  ilike,
  inArray,
  or,
  SQL,
  sql,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";
import search, { SearchQuery } from "../services/search";

export const defendantRoutes = express.Router();

defendantRoutes.get("", async (req: Request, res: Response) => {
  const where: SQL<unknown>[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const propertyId = Number(req.query.propertyId);
  const ids = req.query.ids as string[];

  if (ids) where.push(inArray(Defendants.id, (ids as string[]).map(Number)));
  if (propertyId) where.push(eq(Defendants.propertyId, propertyId));

  const search = req.query.search as string;
  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(
        or(
          ilike(Defendants.firstName, `%${term}%`),
          ilike(Defendants.middleName, `%${term}%`),
          ilike(Defendants.lastName, `%${term}%`),
          ilike(Defendants.businessName, `%${term}%`),
          ilike(Defendants.address1, `%${term}%`),
          ilike(Defendants.address2, `%${term}%`),
          ilike(Defendants.city, `%${term}%`),
          ilike(Defendants.zip, `%${term}%`)
        )!
      );
    }
  }

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    extras: {
      orderName:
        sql<string>`concat(coalesce(trim(${Defendants.lastName}), ''), coalesce(trim(${Defendants.firstName}), ''), coalesce(trim(${Defendants.businessName})), '')`.as(
          "order_name"
        ),
    },
    orderBy: [asc(sql`order_name`)],
    where: and(...where),
  };

  const count = await db.$count(Defendants, and(...where));
  const defendants = await db.query.defendants.findMany(queryConfig);

  res.json({ defendants, count });
});

defendantRoutes.get("/search", async (req: Request, res: Response) => {
  try {
    const queryConfig: DBQueryConfig = {
      extras: {
        orderName:
          sql<string>`concat(coalesce(trim(${Defendants.lastName}), ''), coalesce(trim(${Defendants.firstName}), ''), coalesce(trim(${Defendants.businessName})), '')`.as(
            "order_name"
          ),
      },
      orderBy: [asc(sql`order_name`)],
    };

    const options = await search(
      req.query as unknown as SearchQuery,
      queryConfig
    );

    res.json(options);
  } catch (error) {
    console.log(`-------------error--------------`, error);
    res.status(400).json({ error: "Invalid input" });
  }
});

defendantRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const decryptValues = req.query.decrypt === "true";

  if (!id) {
    res.status(404).json({ error: "Defendant not found" });
    return;
  }

  const defendant = await db.query.defendants.findFirst({
    where: eq(Defendants.id, id),
    with: { property: true },
  });

  if (!defendant) {
    res.status(404).json({ error: "Defendant not found" });
    return;
  }

  if (decryptValues) {
    if (defendant.ssn) defendant.ssn = await decrypt(defendant.ssn);
    if (defendant.dob) defendant.dob = await decrypt(defendant.dob);
  }

  res.json(defendant);
});

defendantRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};

  if (!id) {
    res.status(404).json({ error: "Defendant not found" });
    return;
  }

  const defendant = await db.query.defendants.findFirst({
    where: eq(Defendants.id, id),
  });

  // If the ssn or dob change, we encrypt the new value into the existing secure_data row.
  if (changes.ssn) {
    const encryptedData = await encrypt(changes.ssn, defendant?.ssn);
    changes.ssn = encryptedData;
  }

  if (changes.dob) {
    const encryptedData = await encrypt(changes.dob, defendant?.dob);
    changes.dob = encryptedData;
  }

  await db.update(Defendants).set(changes).where(eq(Defendants.id, id));

  res.json({ success: true });
});

defendantRoutes.post("", async (req: Request, res: Response) => {
  const newDefendantData = req.body || {};

  if (newDefendantData.ssn) {
    const encryptedData = await encrypt(newDefendantData.ssn);
    newDefendantData.ssn = encryptedData;
  }
  if (newDefendantData.dob) {
    const encryptedData = await encrypt(newDefendantData.dob);
    newDefendantData.dob = encryptedData;
  }

  const result = await db
    .insert(Defendants)
    .values(newDefendantData)
    .returning({
      id: Defendants.id,
    });
  const newDefendantId = result[0]?.id;

  const defendant = await db.query.defendants.findFirst({
    where: eq(Defendants.id, newDefendantId),
  });

  res.json(defendant);
});
