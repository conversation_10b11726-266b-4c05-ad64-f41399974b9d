import { db } from "@api/src/services/db";
import msal from "@azure/msal-node";
import { User, Users } from "@shared/db/schema/users";
import { eq, ilike } from "drizzle-orm";
import express, { Request, Response } from "express";
import { authenticatedMixin } from "../services/middleware";

export const authRoutes = express.Router();
const authDomain = process.env.SSO_AUTH_DOMAIN;
const redirectDomain = process.env.SSO_REDIRECT_DOMAIN;

const config = {
  auth: {
    clientId: process.env.SSO_CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.SSO_TENANT_ID}`,
    clientSecret: process.env.SSO_CLIENT_SECRET!,
  },
  system: {
    loggerOptions: {
      loggerCallback(logLevel: msal.LogLevel, message: string) {
        console.log(
          `-------------Auth Error Log Level: ${logLevel}--------------`,
          message
        );
      },
      piiLoggingEnabled: false,
      logLevel: msal.LogLevel.Error,
    },
  },
};

const authClient = new msal.ConfidentialClientApplication(config);

authRoutes.get("/login", async (req: Request, res: Response) => {
  const next = req.query.next as string;

  const authUrlParams = {
    scopes: ["User.Read"],
    redirectUri: `https://${authDomain}/__/auth/handler`,
    state: next,
  };

  const authUrl = await authClient.getAuthCodeUrl(authUrlParams);

  res.redirect(authUrl);
});

authRoutes.get("/handler", async (req: Request, res: Response) => {
  const next = req.query.state as string;

  const tokenRequest = {
    code: req.query.code as string,
    scopes: ["User.Read"],
    redirectUri: `https://${authDomain}/__/auth/handler`,
  };

  const response = await authClient.acquireTokenByCode(tokenRequest);

  const user = await db.query.users.findFirst({
    where: ilike(Users.email, response.account!.username),
  });

  if (!user) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  req.session.user = new User(user);
  res.redirect(`https://${redirectDomain}${next}`);
});

authRoutes.get(
  "/logout",
  authenticatedMixin,
  async (req: Request, res: Response) => {
    req.session.destroy(() => {});

    res.json({ message: "Logged out" });
  }
);

authRoutes.get(
  "/data",
  authenticatedMixin,
  async (req: Request, res: Response) => {
    if (!req.session?.user) {
      res.status(401).json({ message: "Unauthorized" });
      return;
    }

    const user = await db.query.users.findFirst({
      where: eq(Users.id, req.session.user.id!),
    });

    res.json({ user });
  }
);
