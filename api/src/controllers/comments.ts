import { db } from "@api/src/services/db";
import { Comments } from "@shared/db/schema/comments";
import { and, desc, eq, inArray, SQL } from "drizzle-orm";
import express, { Request, Response } from "express";

export const commentRoutes = express.Router();

commentRoutes.get("", async (req: Request, res: Response) => {
  const { caseId, type, workflowCheck } = req.query || {};
  const where: (SQL<unknown> | undefined)[] = [];
  if (caseId) where.push(eq(Comments.caseId, Number(caseId)));
  if (type) where.push(eq(Comments.type, String(type)));
  if (workflowCheck)
    where.push(eq(Comments.workflowCheck, String(workflowCheck)));

  const searchQuery = db
    .select({ caseId: Comments.id })
    .from(Comments)
    .where(and(...where));

  const comments = await db.query.comments.findMany({
    where: inArray(Comments.id, searchQuery),
    with: {
      user: true,
    },
    orderBy: [desc(Comments.updatedAt)],
  });

  res.json(comments);
});

commentRoutes.post("", async (req: Request, res: Response) => {
  const newCommentData = req.body || {};
  const staffId = req.session.user?.id;
  const result = await db
    .insert(Comments)
    .values({
      ...newCommentData,
      staffId,
    })
    .returning({
      id: Comments.id,
    });
  const newCommentId = result[0]?.id;

  const comment = await db.query.comments.findFirst({
    where: eq(Comments.id, newCommentId),
  });

  res.json(comment);
});

commentRoutes.put("", async (req: Request, res: Response) => {
  const commentUpdates = req.body || {};
  const { id, ...changes } = commentUpdates;
  const result = await db
    .update(Comments)
    .set(changes)
    .where(eq(Comments.id, commentUpdates.id))
    .returning({
      id: Comments.id,
    });
  const commentId = result[0]?.id;

  const comment = await db.query.comments.findFirst({
    where: eq(Comments.id, commentId),
  });

  res.json(comment);
});
