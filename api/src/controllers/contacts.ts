import { db } from "@api/src/services/db";
import { ContactToClients } from "@shared/db/schema/clients";
import { Contacts } from "@shared/db/schema/contacts";
import { ContactToProperties } from "@shared/db/schema/properties";
import {
  and,
  asc,
  DBQueryConfig,
  eq,
  ilike,
  inArray,
  isNull,
  SQL,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";

export const contactRoutes = express.Router();

contactRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const ids = req.query.ids as string[];

  if (ids) where.push(inArray(Contacts.id, (ids as string[]).map(Number)));

  const search = req.query.search as string;

  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(ilike(Contacts.name, `%${term}%`));
    }
  }

  // Filter out deleted contacts by default
  where.push(isNull(Contacts.deletedAt));

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: asc(Contacts.name),
    where: and(...where),
  };

  const count = await db.$count(Contacts, and(...where));
  const contacts = await db.query.contacts.findMany(queryConfig);

  res.json({ contacts, count });
});

contactRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);

  if (!id) {
    res.status(404).json({ error: "Contact not found" });
    return;
  }

  const contact = await db.query.contacts.findFirst({
    where: eq(Contacts.id, id),
  });

  res.json(contact);
});

contactRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};

  if (!id) {
    res.status(404).json({ error: "Contact not found" });
    return;
  }

  await db.update(Contacts).set(changes).where(eq(Contacts.id, id));

  res.json({ success: true });
});

contactRoutes.post("", async (req: Request, res: Response) => {
  const { clientId, propertyId, ...newContactData } = req.body || {};

  const result = await db.insert(Contacts).values(newContactData).returning({
    id: Contacts.id,
  });
  const newContactId = result[0]?.id;

  if (propertyId) {
    await db
      .insert(ContactToProperties)
      .values({ contactId: newContactId, propertyId });
  }

  if (clientId) {
    await db
      .insert(ContactToClients)
      .values({ contactId: newContactId, clientId });
  }

  const contact = await db.query.contacts.findFirst({
    where: eq(Contacts.id, newContactId),
  });

  res.json(contact);
});

contactRoutes.delete("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);

  if (!id) {
    res.status(404).json({ error: "Contact not found" });
    return;
  }

  await db
    .update(Contacts)
    .set({ deletedAt: new Date() })
    .where(eq(Contacts.id, id));

  res.json({ success: true });
});
