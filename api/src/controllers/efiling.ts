import { db } from "@api/src/services/db";
import { Cases } from "@shared/db/schema/cases";
import { EfileOperations } from "@shared/db/schema/efile-operations";
import { and, DBQueryConfig, desc, eq, SQL } from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";
import { boss } from "../services/boss";
export const efileRoutes = express.Router();

efileRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const caseId = Number(req.query.caseId);
  const userId = Number(req.query.userId);

  if (caseId) where.push(eq(EfileOperations.caseId, caseId));
  if (userId) where.push(eq(EfileOperations.userId, userId));

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: desc(EfileOperations.createdAt),
    where: and(...where),
    with: {
      case: true,
      client: true,
      defendant: true,
      property: true,
      user: true,
    },
  };
  const efileOperations = await db.query.efileOperations.findMany(queryConfig);
  const count = await db.$count(EfileOperations, and(...where));
  res.json({ efileOperations, count });
});

efileRoutes.post("/:caseId", async (req: Request, res: Response) => {
  const caseId = Number(req.params.caseId);

  if (!req.session.user) {
    res.status(404).json({ error: "Invalid user" });
    return;
  }

  if (Number.isNaN(caseId)) {
    res.status(404).json({ error: "Invalid case id" });
    return;
  }

  const caseData = await db.query.cases.findFirst({
    where: eq(Cases.id, caseId),
  });

  const efileData: any = {
    caseId: caseId,
    userId: req.session.user.id,
    clientId: caseData?.clientId,
    defendantId: caseData?.defendantId,
    propertyId: caseData?.propertyId,
    status: "processing",
    statusMessage: `Starting EFile for case # ${caseData?.caseNumber}`,
  };

  const result = await db.insert(EfileOperations).values(efileData).returning();
  const efileOp = result[0];

  res.status(200).json({ message: "Efile succeeded", efileOpId: efileOp.id });

  const payload = {
    efileOpId: efileOp.id,
    userId: req.session.user.id,
    caseId: caseId,
  };

  try {
    await boss.send("efiling", payload);
  } catch (error) {
    console.trace(`${error}`);
  }
});
