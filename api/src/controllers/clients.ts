import { db } from "@api/src/services/db";
import { Clients } from "@shared/db/schema/clients";
import { Properties } from "@shared/db/schema/properties";
import {
  and,
  asc,
  DBQueryConfig,
  eq,
  ilike,
  inArray,
  or,
  SQL,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";

export const clientRoutes = express.Router();

clientRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const ids = req.query.ids as string[];

  if (ids) where.push(inArray(Clients.id, (ids as string[]).map(Number)));

  const search = req.query.search as string;

  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(
        or(
          ilike(Clients.name, `%${term}%`),
          ilike(Clients.address1, `%${term}%`),
          ilike(Clients.address2, `%${term}%`),
          ilike(Clients.city, `%${term}%`),
          ilike(Clients.zip, `%${term}%`)
        )
      );
    }
  }

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: asc(Clients.name),
    where: and(...where),
  };

  const count = await db.$count(Clients, and(...where));
  const clients = await db.query.clients.findMany(queryConfig);

  res.json({ clients, count });
});

clientRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);

  if (!id) {
    res.status(404).json({ error: "Client not found" });
    return;
  }

  const client = await db.query.clients.findFirst({
    where: eq(Clients.id, id),
    with: {
      properties: {
        orderBy: asc(Properties.name),
        with: {
          contacts: {
            with: {
              contact: true,
            },
          },
        },
      },
      contacts: {
        with: {
          contact: true,
        },
      },
    },
  });

  res.json(client);
});

clientRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};

  if (!id) {
    res.status(404).json({ error: "Client not found" });
    return;
  }

  await db.update(Clients).set(changes).where(eq(Clients.id, id));

  res.json({ success: true });
});

clientRoutes.post("", async (req: Request, res: Response) => {
  const newClientData = req.body || {};

  const result = await db.insert(Clients).values(newClientData).returning({
    id: Clients.id,
  });
  const newClientId = result[0]?.id;

  const client = await db.query.clients.findFirst({
    where: eq(Clients.id, newClientId),
  });

  res.json(client);
});
