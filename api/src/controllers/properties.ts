import { db } from "@api/src/services/db";
import { Properties } from "@shared/db/schema/properties";
import {
  and,
  asc,
  DBQueryConfig,
  eq,
  ilike,
  inArray,
  or,
  SQL,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";

export const propertyRoutes = express.Router();

propertyRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const ids = req.query.ids as string[];
  const clientId = Number(req.query.clientId);

  if (ids) where.push(inArray(Properties.id, (ids as string[]).map(Number)));
  if (clientId) where.push(eq(Properties.clientId, clientId));

  const search = req.query.search as string;
  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(
        or(
          ilike(Properties.name, `%${term}%`),
          ilike(Properties.address1, `%${term}%`),
          ilike(Properties.address2, `%${term}%`),
          ilike(Properties.city, `%${term}%`),
          ilike(Properties.zip, `%${term}%`),
          ilike(Properties.propertyNotes, `%${term}%`)
        )
      );
    }
  }

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: asc(Properties.name),
    where: and(...where),
  };

  const count = await db.$count(Properties, and(...where));
  const properties = await db.query.properties.findMany(queryConfig);

  res.json({ properties, count });
});

propertyRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);

  if (!id) {
    res.status(404).json({ error: "Property not found" });
    return;
  }

  const property = await db.query.properties.findFirst({
    where: eq(Properties.id, id),
    with: {
      contacts: {
        with: {
          contact: true,
        },
      },
      client: true,
    },
  });

  res.json(property);
});

propertyRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};

  if (!id) {
    res.status(404).json({ error: "Property not found" });
    return;
  }

  await db.update(Properties).set(changes).where(eq(Properties.id, id));

  res.json({ success: true });
});

propertyRoutes.post("", async (req: Request, res: Response) => {
  const newPropertyData = req.body || {};

  const result = await db.insert(Properties).values(newPropertyData).returning({
    id: Properties.id,
  });
  const newPropertyId = result[0]?.id;

  const property = await db.query.properties.findFirst({
    where: eq(Properties.id, newPropertyId),
  });

  res.json(property);
});
