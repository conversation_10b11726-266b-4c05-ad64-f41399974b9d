import { decrypt } from "@api/src/services/encryption";
import express, { Request, Response } from "express";

export const secureRoutes = express.Router();

secureRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = req.params.id;

  try {
    const decryptedData = await decrypt(id);
    res.json(decryptedData || "");
  } catch (error) {
    throw new Error(`Decryption error for id: ${id}, ${error}`);
  }
});
