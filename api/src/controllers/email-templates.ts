import {
  EmailTemplate,
  EmailTemplates,
} from "@shared/db/schema/email-templates";
import { eq } from "drizzle-orm";
import express, { Request, Response } from "express";
import { db } from "../services/db";

export const emailTemplateRoutes = express.Router();

emailTemplateRoutes.post("/", async (req: Request, res: Response) => {
  const data = req.body;
  const insertedRecords = await db
    .insert(EmailTemplates)
    .values(data)
    .returning();

  if (!insertedRecords[0]) {
    res.status(404).json({ error: "Could not create email template record" });
    return;
  }

  res.json(new EmailTemplate(insertedRecords[0]));
});

emailTemplateRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const changes = req.body || {};
  await db.update(EmailTemplates).set(changes).where(eq(EmailTemplates.id, id));

  if (!id) {
    res.status(404).json({ error: "Email template record not found" });
    return;
  }

  res.json({ success: true });
});
