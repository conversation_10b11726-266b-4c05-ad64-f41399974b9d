import { Counties } from "@shared/db/schema/counties";
import { eq } from "drizzle-orm";
import express, { Request, Response } from "express";
import { db } from "../services/db";

export const countyRoutes = express.Router();

countyRoutes.post("", async (req: Request, res: Response) => {
  const newCountyData = req.body || {};
  const result = await db.insert(Counties).values(newCountyData).returning();
  res.json(result[0]);
});

countyRoutes.put("", async (req: Request, res: Response) => {
  const countyData = req.body || {};
  const { id, ...data } = countyData;
  const result = await db
    .update(Counties)
    .set(data)
    .where(eq(Counties.id, id))
    .returning();
  res.json(result[0]);
});

countyRoutes.get("/:id", async (req: Request, res: Response) => {
  const countyId = req.params.id;
  if (!countyId) res.json("County not found");

  const county = await db.query.counties.findFirst({
    where: eq(Counties.id, Number(countyId)),
  });
  res.json(county);
});
