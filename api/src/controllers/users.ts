import { db } from "@api/src/services/db";
import { decrypt, encrypt } from "@api/src/services/encryption";
import { Users } from "@shared/db/schema/users";
import {
  DBQueryConfig,
  SQL,
  and,
  asc,
  eq,
  ilike,
  inArray,
  or,
} from "drizzle-orm";
import express, { Request, Response } from "express";
import { max } from "lodash";

export const userRoutes = express.Router();

userRoutes.get("", async (req: Request, res: Response) => {
  const where: (SQL<unknown> | undefined)[] = [];
  const limit = Number(req.query.limit || 30);
  const page = Number(req.query.page || 1);
  const offset = max([page - 1, 0])! * limit;
  const status = req.query.status as string;
  const ids = req.query.ids as string[];

  if (ids) where.push(inArray(Users.id, (ids as string[]).map(Number)));

  if (status) {
    if (status === "Active")
      where.push(and(eq(Users.isDisabled, false), eq(Users.isArchived, false)));

    if (status === "Disabled")
      where.push(and(eq(Users.isDisabled, true), eq(Users.isArchived, false)));

    if (status === "Archived") where.push(eq(Users.isArchived, true));
  }

  const search = req.query.search as string;
  if (search) {
    for (const term of search.split(" ")) {
      if (!term) continue;

      where.push(
        or(ilike(Users.name, `%${term}%`), ilike(Users.email, `%${term}%`))
      );
    }
  }

  const queryConfig: DBQueryConfig = {
    limit,
    offset,
    orderBy: asc(Users.name),
    where: and(...where),
  };

  const count = await db.$count(Users, and(...where));
  const users = await db.query.users.findMany(queryConfig);

  res.json({ users, count });
});

userRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const decryptValues = req.query.decrypt === "true";

  if (!id) {
    res.status(404).json({ error: "User not found" });
    return;
  }

  const user = await db.query.users.findFirst({
    where: eq(Users.id, id),
  });

  if (decryptValues && user?.fileAndServePassword) {
    user.fileAndServePassword = await decrypt(user.fileAndServePassword);
  }

  res.json(user);
});

userRoutes.put("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const userChanges = req.body || {};

  if (!id) {
    res.status(404).json({ error: "User not found" });
    return;
  }

  const user = await db.query.users.findFirst({
    where: eq(Users.id, id),
  });

  if (userChanges.fileAndServePassword) {
    const encryptedData = await encrypt(
      userChanges.fileAndServePassword,
      user?.fileAndServePassword
    );
    userChanges.fileAndServePassword = encryptedData;
  }

  await db.update(Users).set(userChanges).where(eq(Users.id, id));

  res.json({ success: true });
});

userRoutes.post("", async (req: Request, res: Response) => {
  const newUserData = req.body || {};

  if (newUserData.fileAndServePassword) {
    const encryptedData = await encrypt(newUserData.fileAndServePassword);
    newUserData.fileAndServePassword = encryptedData;
  }

  const result = await db.insert(Users).values(newUserData).returning({
    id: Users.id,
  });
  const newUserId = result[0]?.id;

  const user = await db.query.users.findFirst({
    where: eq(Users.id, newUserId),
  });

  res.json(user);
});
