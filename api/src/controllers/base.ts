import { db } from "@api/src/services/db";
import { Counties } from "@shared/db/schema/counties";
import { EmailTemplates } from "@shared/db/schema/email-templates";
import { States } from "@shared/db/schema/states";
import { Templates } from "@shared/db/schema/templates";
import { Users } from "@shared/db/schema/users";
import { and, asc, eq, isNotNull } from "drizzle-orm";
import express, { Request, Response } from "express";

export const baseRoutes = express.Router();

baseRoutes.get("/data", async (req: Request, res: Response) => {
  const templates = await db.query.templates.findMany({
    orderBy: asc(Templates.name),
  });

  const templatesByState = templates.reduce((acc, template) => {
    if (!acc[template.state!]) {
      acc[template.state!] = [];
    }
    acc[template.state!].push(template);

    return acc;
  }, {});

  const settings = await db.query.settings.findMany();

  const settingsData: any = settings.reduce((acc, setting) => {
    acc[setting.key] = setting.value;
    return acc;
  }, {});

  delete settingsData.clio?.tokens;

  const counties = await db.query.counties.findMany({
    orderBy: [asc(Counties.state), asc(Counties.name)],
  });
  const states = await db.query.states.findMany({ orderBy: asc(States.name) });
  const emailTemplates = await db.query.emailTemplates.findMany({
    orderBy: asc(EmailTemplates.subject),
  });
  const users = await db.query.users.findMany({
    columns: {
      id: true,
      name: true,
      email: true,
      isAttorney: true,
      isNonStaff: true,
      badgeColor: true,
      badgeTextColor: true,
      initials: true,
    },
    where: and(eq(Users.isDisabled, false), isNotNull(Users.name)),
    orderBy: asc(Users.name),
  });

  const isDebugMode = process.env.ENVIRONMENT
    ? process.env.ENVIRONMENT === "development"
    : false;

  res.json({
    templatesByState,
    counties,
    states,
    emailTemplates,
    users,
    isDebugMode,
    ...settingsData,
  });
});
