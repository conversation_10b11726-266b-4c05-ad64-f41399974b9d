import { Document } from "@shared/db/schema/documents";
import express, { Request, Response } from "express";
import collectData from "../services/case-data";
import {
  createDocRecord,
  saveDocument,
  updateDocStatus,
} from "../services/documents";
import { sanitizeFileName } from "../services/files";
import { logger } from "../services/logger";
import {
  createTemplate,
  getTemplate,
  getTemplates,
  renderDocument,
  updateTemplate,
} from "../services/templates";

export const templateRoutes = express.Router();

templateRoutes.get("/", async (req: Request, res: Response) => {
  const templates = await getTemplates();
  res.json(templates);
});

templateRoutes.get("/:id", async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const template = await getTemplate(id);
  res.json(template);
});

templateRoutes.put("/", async (req: Request, res: Response) => {
  const {
    id,
    name,
    state,
    downloadFormat,
    county,
    courtType,
    category,
    customFields,
    usage,
  } = req.body;
  const template = await updateTemplate({
    id,
    name,
    state,
    downloadFormat,
    county,
    courtType,
    category,
    customFields,
    usage,
  });
  res.json({ message: "Template updated" });
});

templateRoutes.post("/", async (req: Request, res: Response) => {
  const {
    name,
    state,
    downloadFormat,
    county,
    courtType,
    category,
    customFields,
    usage,
  } = req.body;

  const template = await createTemplate({
    name,
    state,
    downloadFormat,
    county,
    courtType,
    category,
    customFields,
    usage,
  });
  res.json({ template });
});

templateRoutes.post(
  "/:templateId/render/:caseId",
  async (req: Request, res: Response) => {
    const caseId = Number(req.params.caseId);
    const user = req.session.user;
    const templateId = Number(req.params.templateId);
    const { customFields } = req.body;
    let docRecord: Document | null = null;

    try {
      const caseData = await collectData(Number(caseId), user);
      if (!caseData) {
        res.status(404).json({ error: "Case not found" });
        return;
      }

      const template = await getTemplate(templateId);
      if (!template) {
        res.status(404).json({ error: "Template not found" });
        return;
      }

      const documentName = `${template.name} - ${caseData.client.name} - ${
        caseData.defendant.firstName
      } ${caseData.defendant.lastName}.${template.getFileExtension(
        customFields
      )}`;

      docRecord = await createDocRecord({
        caseId: Number(caseId),
        documentName: sanitizeFileName(documentName),
      });

      res.json(docRecord);

      const documentData = await renderDocument({
        caseData,
        template,
        documentName: docRecord.fileName!,
        outputFormat: docRecord.getExtension(),
        customFields,
      });

      await saveDocument(docRecord, documentData);
    } catch (error: Error | any) {
      logger.error(error.stack || error);
      if (docRecord?.id) await updateDocStatus("failed", docRecord.id);
    }
  }
);
