import { db } from "@api/src/services/db";
import { basePath } from "@api/src/services/files";
import { Documents } from "@shared/db/schema/documents";
import { eq } from "drizzle-orm";
import express, { Request, Response } from "express";
import fs from "fs/promises";
import path from "path";

export const fileRoutes = express.Router();

fileRoutes.get("*path", async (req: Request, res: Response) => {
  const reqFilePath = decodeURIComponent(path.join(...req.params.path));
  const localFilePath = path.resolve(path.join(basePath, reqFilePath));
  const sendFileAs = req.query.action === "download" ? "attachment" : "inline";
  const doc = await db.query.documents.findFirst({
    where: eq(Documents.filePath, reqFilePath),
  });

  try {
    await fs.access(localFilePath, fs.constants.F_OK);

    const options = {
      headers: {
        "Content-Disposition": `${sendFileAs}; filename="${doc?.fileName!}"`,
      },
    };

    res.sendFile(localFilePath, options, (err) => {
      if (err) {
        console.error("Error during download:", err);
        res.status(500).send("Error during file download");
      }
    });
  } catch (error: any) {
    if (error.code === "ENOENT") {
      res.status(404).send("File not found");
      return;
    }

    throw error;
  }
});
