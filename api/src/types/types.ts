export interface CaseData {
  defendant: any;
  case: any;
  property: any;
  client: any;
  defaults: any;
  customFields?: any;
  singularPluralWording?: any;
}

export interface SingularPluralWording {
  defendantSP: string;
  defendantSP2: string;
  isSP: string;
  wasSP: string;
  hasSP: string;
  owesSP: string;
  remainsSP: string;
  vacatesSP: string;
}

export type Template = {
  state: string | null;
  name: string | null;
  id: number;
  createdAt: Date;
  updatedAt: Date;
  downloadFormat: string | null;
};

export type DocumentRecord = {
  status: "pending" | "failed" | "complete" | null;
  id: number;
  createdAt: Date;
  updatedAt: Date;
  caseId: number | null;
  fileName: string | null;
  filePath: string | null;
};

export type StatusAndStage = {
  status: string;
  stage: string;
  next: {
    STATUS: string;
    STAGE: string;
  };
};
