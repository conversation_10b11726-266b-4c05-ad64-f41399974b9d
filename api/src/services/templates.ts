import { db } from "@api/src/services/db";
import { Template, Templates } from "@shared/db/schema/templates";
import axios from "axios";
import { eq } from "drizzle-orm";
import { logger } from "../services/logger";
import { CaseData } from "../types/types";

const DOCMOSIS_KEY = process.env.DOCMOSIS_KEY;
const DOCMOSIS_URL = process.env.DOCMOSIS_URL;
const docmosisClient = axios.create({
  baseURL: DOCMOSIS_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export async function createTemplate(
  templateData
): Promise<Template | undefined> {
  const templates = await db.insert(Templates).values(templateData).returning();
  return new Template(templates[0]);
}

export async function updateTemplate(data): Promise<Template | undefined> {
  const { id, ...changes } = data;
  const templates = await db
    .update(Templates)
    .set(changes)
    .where(eq(Templates.id, id))
    .returning();
  return new Template(templates[0]);
}

export async function getTemplates(): Promise<{
  templates: Template[] | undefined;
}> {
  const rawTemplatesData = await db.query.templates.findMany();

  const templates = rawTemplatesData.map((template) => new Template(template));
  return { templates };
}

export async function getTemplate(id: number): Promise<Template | undefined> {
  const template = await db.query.templates.findFirst({
    where: eq(Templates.id, id),
  });
  return new Template(template!);
}

export async function renderDocument({
  caseData,
  template,
  documentName,
  outputFormat,
  customFields,
}: {
  caseData: CaseData;
  template: Template;
  documentName: string;
  outputFormat: string;
  customFields: { [key: string]: any };
}): Promise<Buffer> {
  const endpoint = "/render";
  const dataToRender = {
    accessKey: DOCMOSIS_KEY,
    templateName: `/${template.state}/${template.name}.docx`,
    outputName: documentName,
    outputFormat,
    storeTo: "stream",
    streamResultInResponse: true,
    data: { ...caseData, customFields },
  };

  try {
    const response = await docmosisClient.post(endpoint, dataToRender);
    return Buffer.from(response.data.resultFile, "base64");
  } catch (error: any) {
    const errorMessage = error?.response?.data?.shortMsg || error;
    logger.error(`Error rendering document: ${errorMessage}`, error.stack);
    throw error;
  }
}
