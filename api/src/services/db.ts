import { Cases, CasesRelations } from "@shared/db/schema/cases";
import {
  ChecklistItemRelations,
  ChecklistItems,
} from "@shared/db/schema/checklist-items";
import {
  Clients,
  ClientsRelations,
  ContactClientsRelations,
  ContactToClients,
} from "@shared/db/schema/clients";
import {
  ClioContacts,
  ClioMatterRelations,
  ClioMatters,
} from "@shared/db/schema/clio";
import { Comments, CommentsRelations } from "@shared/db/schema/comments";
import { Contacts } from "@shared/db/schema/contacts";
import { Counties } from "@shared/db/schema/counties";
import { DefendantRelations, Defendants } from "@shared/db/schema/defendants";
import { Documents } from "@shared/db/schema/documents";
import {
  EfileOperations,
  EfileOpertionRelations,
} from "@shared/db/schema/efile-operations";
import { EmailTemplates } from "@shared/db/schema/email-templates";
import {
  IntakeFormResponseRelations,
  IntakeFormResponses,
} from "@shared/db/schema/intake-form-responses";
import {
  ContactPropertiesRelations,
  ContactToProperties,
  Properties,
  PropertiesRelations,
} from "@shared/db/schema/properties";
import { SecureData } from "@shared/db/schema/secure-data";
import { Settings } from "@shared/db/schema/settings";
import { States } from "@shared/db/schema/states";
import { Templates } from "@shared/db/schema/templates";
import { Users } from "@shared/db/schema/users";
import { drizzle } from "drizzle-orm/node-postgres";

export const schema = {
  // Cases
  cases: Cases,
  caseRelations: CasesRelations,

  // Checklist Items
  checklistItems: ChecklistItems,
  checklistItemRelations: ChecklistItemRelations,

  // Clients
  clients: Clients,
  clientsRelations: ClientsRelations,
  clientContactRelations: ContactClientsRelations,

  // Comments
  comments: Comments,
  commentsRelations: CommentsRelations,

  // Clio Matters
  clioMatters: ClioMatters,
  clioMattersRelations: ClioMatterRelations,

  // Clio Contacts
  clioContacts: ClioContacts,

  // Contacts
  contacts: Contacts,
  contactProperties: ContactToProperties,
  contactClients: ContactToClients,

  // Counties
  counties: Counties,

  // Defendants
  defendants: Defendants,
  defendantRelations: DefendantRelations,

  // Documents
  documents: Documents,

  // Efile Operations
  efileOperations: EfileOperations,
  efileOperationsRelations: EfileOpertionRelations,

  // Email Template Operations
  emailTemplates: EmailTemplates,

  // Intake Form Responses
  intakeFormResponses: IntakeFormResponses,
  intakeFormResponseRelations: IntakeFormResponseRelations,

  // Properties
  properties: Properties,
  propertiesRelations: PropertiesRelations,
  propertiesContactRelations: ContactPropertiesRelations,

  // Secure Data
  secureData: SecureData,

  // Settings
  settings: Settings,

  // States
  states: States,

  // Templates & Document Generation
  templates: Templates,

  // Users
  users: Users,
};

export const db = drizzle(process.env.DATABASE_URL!, {
  casing: "snake_case",
  schema,
});
