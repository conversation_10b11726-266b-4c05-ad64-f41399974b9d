import { db } from "@api/src/services/db";
import { SecureData } from "@shared/db/schema/secure-data";
import crypto from "crypto";
import { eq } from "drizzle-orm";
import { logger } from "./logger";

const algorithm = "aes-256-gcm";
const key = Buffer.from(process.env.ENCRYPTION_KEY!, "hex");

function generateUniqueIV(): Buffer {
  let counter = BigInt(Date.now());
  const counterBuffer = Buffer.alloc(12); // 96 bits/12 bytes is optimal for GCM.

  // Convert counter to 96-bit (12-byte) buffer.
  counterBuffer.writeBigUInt64BE(counter, 4); // Last 8 bytes for counter.
  // First 4 bytes can be random or derived from other unique values.
  crypto.randomFillSync(counterBuffer, 0, 4);

  return counterBuffer;
}

export async function encrypt(text: string, existingKey?) {
  // takes between 1-5 ms to complete an encryption.
  try {
    const iv = generateUniqueIV();
    const cipher = crypto.createCipheriv(algorithm, key, iv);

    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");

    const authTag = cipher.getAuthTag();

    const secureData = {
      iv: iv.toString("hex"),
      encryptedData: encrypted,
      authTag: authTag.toString("hex"),
    };

    if (existingKey) {
      const oldSecureDataId = existingKey.split("secure_data_")[1];

      if (oldSecureDataId) {
        const currentValue = await decrypt(existingKey);

        if (currentValue === text) return existingKey;

        const queryOutput = await db
          .update(SecureData)
          .set(secureData)
          .where(eq(SecureData.id, oldSecureDataId))
          .returning({ id: SecureData.id });

        const secureDataId = queryOutput[0]?.id;

        if (secureDataId) return `secure_data_${secureDataId}`;
      }
    }

    const queryOutput = await db
      .insert(SecureData)
      .values(secureData)
      .returning({ id: SecureData.id });
    const secureDataId = queryOutput[0].id;

    return `secure_data_${secureDataId}`;
  } catch (error) {
    logger.error(`-------------Error encrypting data--------------`, error);
    throw new Error("Error encrypting data");
  }
}

export async function decrypt(id: string) {
  try {
    const secureDataId = id.split("secure_data_")[1];

    const securedData = await db.query.secureData.findFirst({
      where: eq(SecureData.id, Number(secureDataId)),
    });

    if (!securedData) return null;

    const decipher = crypto.createDecipheriv(
      algorithm,
      key,
      Buffer.from(securedData.iv, "hex")
    );

    decipher.setAuthTag(Buffer.from(securedData.authTag, "hex"));

    let decrypted = decipher.update(securedData.encryptedData, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  } catch (error) {
    logger.error(
      `-------------Error decrypting data ${id}--------------`,
      error
    );
    return id;
  }
}
