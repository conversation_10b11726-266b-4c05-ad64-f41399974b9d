import { db } from "@api/src/services/db";
import { EfileOperations } from "@shared/db/schema/efile-operations";
import { Users } from "@shared/db/schema/users";
import { eq } from "drizzle-orm";
import * as puppeteer from "puppeteer-core";
import { setTimeout } from "timers/promises";
import { CaseData } from "../types/types";
import collectData from "./case-data";
import { decrypt } from "./encryption";
import { logger } from "./logger";

export async function startEfile(jobData) {
  const { userId, caseId, efileOpId } = jobData;
  const filingStaff = await db.query.users.findFirst({
    where: eq(Users.id, userId),
  });

  if (filingStaff?.fileAndServePassword) {
    filingStaff.fileAndServePassword = await decrypt(
      filingStaff.fileAndServePassword
    );
  } else {
    logger.error("No file and serve password found");
    throw new Error("No file and serve password found");
  }

  if (filingStaff?.fileAndServeEmail)
    filingStaff.email = filingStaff?.fileAndServeEmail;

  if (Number.isNaN(caseId)) {
    logger.error("Invalid case id");
    throw new Error("Invalid case id");
  }

  const efileOp = await db.query.efileOperations.findFirst({
    where: eq(EfileOperations.id, efileOpId),
  });

  const caseData = await collectData(caseId, filingStaff);
  if (!caseData) {
    logger.error("Case not found");
    throw new Error("Case not found");
  }

  const payload = {
    efileOp,
    caseData,
  };

  try {
    runEfile(payload);
  } catch (error) {
    logger.error(`Failed to run efile: ${error}`);
  }
}

export async function runEfile(jobData: any) {
  const { efileOp, caseData } = jobData;

  const PUPPETEER_OPTIONS = {
    args: ["--no-sandbox", "--disable-gpu"],
    headless: process.env.NODE_ENV === "production",
    executablePath: process.env.LOCAL_CHROME,
    defaultViewport: { width: 1400, height: 800 },
    protocolTimeout: 240_000,
    timeout: 60_000,
  };
  const browser = await puppeteer.launch(PUPPETEER_OPTIONS);
  const [page] = await browser.pages();
  const efile = new Efile(browser, page, updateEfileStatus);
  let draftURL = "https://oregon.tylertech.cloud/OfsWeb/Envelope/AddOrEdit?Id=";
  let draftId = "";

  try {
    draftId = await efile.run(efileOp.id, caseData);
    await updateEfileStatus(
      efileOp.id,
      "complete",
      `EFile successful. \nDraft ID: ${draftId}.\nNo errors reported`,
      draftURL + draftId
    );
  } catch (error) {
    draftId === "New" || draftId === ""
      ? (draftURL =
          "The case information section was not able to complete; a draft was not created.")
      : (draftURL += draftId);
    await updateEfileStatus(
      efileOp.id,
      "failed",
      `EFile failed or incomplete. \nDraft ID: ${draftId}. \n${error}`,
      draftURL
    );
    throw new Error(`\nFailed to complete efile: ${error}`);
  } finally {
    await browser.close();
  }
}

export class Efile {
  browser: puppeteer.Browser;
  page: puppeteer.Page;
  statusUpdater: EfileStatusUpdater;

  constructor(
    browser: puppeteer.Browser,
    page: puppeteer.Page,
    statusUpdater: EfileStatusUpdater
  ) {
    this.browser = browser;
    this.page = page;
    this.statusUpdater = statusUpdater;
  }

  baseSelector = `Host.Areas.FileAndServeModule.Views.Envelope.ViewModels.`;

  bools = {
    isAdditionalPlaintiff: true,
    isSlowField: true,
  };

  async run(efileOpId: number, caseData: CaseData): Promise<string> {
    /* BEGIN A NEW CASE EFILE */
    try {
      await this.statusUpdater(
        efileOpId,
        "processing",
        "Launching Odyssey File & Serve Portal"
      );
      await this.beginEFile(caseData);

      /* CASE INFORMATION SECTION */
      await this.statusUpdater(efileOpId, "processing", "Entering Case Info");
      await this.enterCaseInfo(caseData);

      /* PARTY INFORMATION SECTION (Plaintiff (Client) Information) */
      await this.statusUpdater(
        efileOpId,
        "processing",
        "Entering Client Plaintiff Info"
      );

      if (
        caseData.case.plaintiffs === "both" ||
        caseData.case.plaintiffs === "client"
      ) {
        if (await this.isReady())
          await this.enterPlaintiffInfo(
            caseData.client,
            caseData.case.attorney
          );
      } else {
        if (await this.isReady())
          await this.enterPlaintiffInfo(
            caseData.property,
            caseData.case.attorney
          );
      }

      /* PARTY INFORMATION SECTION (Defendant Information) */
      await this.statusUpdater(
        efileOpId,
        "processing",
        "Entering Defendant Info"
      );
      if (await this.isReady())
        await this.enterDefendantInfo(caseData.defendant);

      /* PARTY INFORMATION SECTION (Second Plaintiff (Property) Information) */
      await this.statusUpdater(
        efileOpId,
        "processing",
        "Entering Property Plaintiff Info"
      );

      if (caseData.case.fileBothPlaintiffs) {
        if (await this.isReady())
          await this.enterPlaintiffInfo(
            caseData.property,
            caseData.case.attorney,
            this.bools.isAdditionalPlaintiff
          );
      }

      /* PARTY INFORMATION SECTION (Additional Defendant Information) */
      if (caseData.case.additionalDefendants.length > 0) {
        await this.statusUpdater(
          efileOpId,
          "processing",
          "Entering Additional Defendants"
        );
        await this.enterAdditionalDefendants(caseData);
      }

      if (
        caseData.property.county.name !== "Clackamas" &&
        caseData.property.county.name !== "Multnomah" &&
        caseData.property.county.name !== "Washington"
      ) {
        await this.statusUpdater(
          efileOpId,
          "processing",
          "Entering Additional Defendants"
        );
        const outlyingDefendant: any = {};
        outlyingDefendant["firstName"] = "And all";
        outlyingDefendant["middleName"] = "";
        outlyingDefendant["lastName"] = "other occupants";
        outlyingDefendant["suffix"] = "";
        outlyingDefendant["address1"] = caseData.defendant.address1;
        outlyingDefendant["address2"] = caseData.defendant.address2;
        outlyingDefendant["city"] = caseData.defendant.city;
        outlyingDefendant["state"] = caseData.defendant.state;
        outlyingDefendant["zip"] = caseData.defendant.zip;
        await this.enterDefendantInfo(outlyingDefendant, true);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
    return await this.getDraftId();
  }

  async beginEFile(caseData: CaseData) {
    await this.goToURL(
      "https://oregon.tylertech.cloud/OfsWeb/FileAndServeModule"
    );
    await this.logIn(
      caseData.case.filingStaff.email,
      caseData.case.filingStaff.fileAndServePassword
    );
    await this.startCase();
  }

  async goToURL(URL: string) {
    try {
      await this.page.goto(URL, {
        timeout: 10000,
        waitUntil: ["domcontentloaded", "networkidle0"],
      });
    } catch (error) {
      throw new Error(`\nError navigating to URL ->\n${error}`);
    }
  }

  async logIn(USERNAME: string, PASSWORD: string) {
    try {
      await this.page.type("#UserName", USERNAME);
      await this.page.type("#Password", PASSWORD);
      await Promise.all([
        this.page.waitForNavigation({
          timeout: 10000,
          waitUntil: "domcontentloaded",
        }),
        this.page.click(".btn"),
      ]);
    } catch (error) {
      throw new Error(`\nLogin failed ->\n${error}`);
    }
  }

  async startCase() {
    try {
      const startCaseButton = await this.page.waitForSelector(
        `a[title="Start a New Case"]`,
        { visible: true }
      );
      await startCaseButton!.click({ delay: 5 });
      await this.page.waitForNavigation({ waitUntil: "domcontentloaded" });
      if (await this.isReady()) {
        return;
      }
    } catch (error) {
      throw new Error(`\nStarting a new case failed ->\n${error}`);
    }
  }

  // TODO: Monitor for changes to Odyssey app. The following modal seems to have been removed.
  async closeModal(cssSelector: string) {
    try {
      const closeModalButton = await this.page.waitForSelector(cssSelector, {
        visible: true,
      });
      await closeModalButton!.click({ delay: 5 });
    } catch (error) {
      throw new Error(`\nClosing modal failed ->\n${error}`);
    }
  }

  async enterCaseInfo(caseData: CaseData) {
    const caseType = "Landlord/Tenant - General ";
    try {
      /* This is a required field */
      /* Case Information Input: Location */
      const county = this.getFullCountyName(caseData.property.county.name);
      await this.selectFromDropdownExactMatch("Case", "Location_Id", county);

      /* This is a required field */
      /* Select Case Information Input: Category */
      await this.selectFromDropdownExactMatch("Case", "Category_Id", "Civil");

      /* This is a required field */
      /* Select Case Information Input: CaseType */
      /* DEFAULTING TO GENERAL EVICTION UNTIL WE CAN FIGURE OUT WHY THE CASE TYPE FOR RESIDENTIAL EVICTION IS NOT AVAILABLE */
      await this.selectFromDropdownExactMatch("Case", "CaseType_Id", caseType);

      /* Save Case Information */
      await this.clickElementWithId("btn16");
    } catch (error) {
      throw new Error(
        `\nA DRAFT CASE WAS NOT CREATED\nEntering case info failed ->\n${error}`
      );
    }
  }

  async enterPlaintiffInfo(
    plaintiff: any,
    attorney: any,
    isAdditionalPlaintiff: Boolean = false
  ) {
    try {
      if (isAdditionalPlaintiff) {
        if (await this.isReady())
          await this.clickElementWithId("btnAddAnotherParty");

        if (await this.isReady())
          await this.selectFromDropdownExactMatch(
            "Party",
            "PartyType_Id",
            "Plaintiff"
          );
      }

      await this.clickIsBusinessCheckbox();

      /* This is a required field */
      await this.enterText("Party", "BusinessName", plaintiff.name);
      await this.enterAddress(plaintiff);

      if (attorney.name !== null && attorney.name !== "") {
        await this.selectFromDropdownExactMatch(
          "Party",
          "Attorney_Id",
          attorney.name
        );
      }

      /* Save Plaintiff Party Information */
      await this.clickElementWithId("btn37");
    } catch (error) {
      throw new Error(`\nEntering a plaintiff failed ->\n${error}`);
    }
  }

  async enterAddress(party: any) {
    try {
      if (party.address1 !== null && party.address1 !== "") {
        await this.enterText("Party", "AddressLine1", party.address1);
      }

      if (party.address2 !== null && party.address2 !== "") {
        await this.enterText("Party", "AddressLine2", party.address2);
      }

      if (party.city !== null && party.city !== "") {
        await this.enterText("Party", "City", party.city);
      }

      if (party.state !== null && party.state !== "") {
        const partyState = this.getFullStateName(party.state);

        await this.selectFromDropdownExactMatch(
          "Party",
          "State_Id",
          partyState
        );
      }

      if (party.zip !== null && party.zip !== "") {
        await this.enterText(
          "Party",
          "ZipCode",
          party.zip,
          this.bools.isSlowField
        );
      }
    } catch (error) {
      throw new Error(`Entering a Party address failed: ${error}`);
    }
  }

  async enterDefendantInfo(
    defendant: any,
    isAdditionalDefendant: boolean = false
  ) {
    try {
      if (isAdditionalDefendant) {
        if (await this.isReady())
          await this.clickElementWithId("btnAddAnotherParty");

        if (await this.isReady())
          await this.selectFromDropdownExactMatch(
            "Party",
            "PartyType_Id",
            "Defendant"
          );
      }

      defendant.isBusiness
        ? await this.enterBusiness(defendant)
        : await this.enterPerson(defendant);

      await this.enterAddress(defendant);

      if (!!defendant.phone) {
        await this.enterText("Party", "PhoneNumber", defendant.phone);
      }

      /* Save Defendant Party Information */
      await this.clickElementWithId("btn37");
    } catch (error) {
      throw new Error(`\nEntering defendant failed ->\n${error}`);
    }
  }

  private async enterPerson(defendant: any) {
    await this.enterText("Party", "FirstName", defendant.firstName);

    if (defendant.middleName !== null && defendant.middleName !== "") {
      await this.enterText(
        "Party",
        "MiddleName",
        defendant.middleName,
        this.bools.isSlowField
      );
    }

    await this.enterText("Party", "LastName", defendant.lastName);

    if (defendant.suffix !== null && defendant.suffix !== "") {
      await this.selectFromDropdownExactMatch(
        "Party",
        "Suffix",
        defendant.suffix
      );
    }
  }

  private async enterBusiness(defendant: any) {
    await this.clickIsBusinessCheckbox();
    await this.enterText("Party", "BusinessName", defendant.businessName);
  }

  async enterAdditionalDefendants(caseData: CaseData) {
    let defendant;
    for (const additionalDefendant of caseData.case.additionalDefendants) {
      defendant = additionalDefendant;
      defendant["address1"] = caseData.defendant.address1;
      defendant["address2"] = caseData.defendant.address2;
      defendant["city"] = caseData.defendant.city;
      defendant["state"] = caseData.defendant.state;
      defendant["zip"] = caseData.defendant.zip;
      if (await this.isReady()) await this.enterDefendantInfo(defendant, true);
    }
  }

  async getDraftId(): Promise<string> {
    try {
      const draftHeader: any = await this.page.$(
        'h2[class="tyler-display-inline-block header-ellipsis"]'
      );
      const draftHeaderText: any = await draftHeader.getProperty("innerText");
      const headerText: any = await draftHeaderText.jsonValue();
      return headerText.split(" ")[2];
    } catch (error) {
      throw new Error(`\nGetting the draft ID failied ->\n${error}`);
    }
  }

  buildSelectors(sectionName: string, dataName: string) {
    const modelSelector = `${sectionName}ViewModel.${dataName}`;
    const selector = `${this.baseSelector}${modelSelector}`;
    const listboxSelector = `span[aria-owns="${selector}_listbox"]`;
    const optionSelector = `li[id="${selector}_option_selected"]`;
    const inputSelector = `input[aria-owns="${selector}_listbox"]`;
    return [listboxSelector, optionSelector, inputSelector];
  }

  async selectFromDropdownExactMatch(
    sectionName: string,
    dataName: string,
    data: string
  ) {
    const [listboxSelector, optionSelector, inputSelector] =
      this.buildSelectors(sectionName, dataName);
    try {
      await this.page.waitForSelector(listboxSelector);
      await setTimeout(1200);
      await this.page.focus(listboxSelector);
      await this.page.click(listboxSelector);
      await this.page.waitForSelector(inputSelector);
      await this.page.focus(inputSelector);
      await this.page.click(inputSelector);
      await this.page.waitForSelector(optionSelector, { visible: true });
      let selectedOptionText = await this.page.$eval(
        optionSelector,
        (li) => li.textContent
      );

      while (selectedOptionText!.toLowerCase() !== data.toLowerCase()) {
        await this.page.keyboard.press("ArrowDown", { delay: 5 });
        selectedOptionText = await this.page.$eval(
          optionSelector,
          (li) => li.textContent
        );
      }

      await this.page.keyboard.press("Tab");
    } catch (error) {
      throw new Error(
        `\nSelecting ${dataName} in ${sectionName} section failed ->\n${error}`
      );
    }
  }

  async selectFromDropdownOptionIncludes(
    sectionName: string,
    dataName: string,
    data: string
  ) {
    const [listboxSelector, optionSelector, inputSelector] =
      this.buildSelectors(sectionName, dataName);
    try {
      const dropdownHandle = await this.page.waitForSelector(listboxSelector);
      await dropdownHandle!.focus();
      await dropdownHandle!.click({ delay: 10 });
      const inputHandle = await this.page.waitForSelector(inputSelector);
      await inputHandle!.focus();
      await inputHandle!.click({ delay: 10 });
      await this.page.waitForSelector(optionSelector, { visible: true });
      let selectedOptionText = await this.page.$eval(
        optionSelector,
        (li) => li.textContent
      );
      while (!selectedOptionText!.toLowerCase().includes(data.toLowerCase())) {
        await this.page.keyboard.press("ArrowDown");
        selectedOptionText = await this.page.$eval(
          optionSelector,
          (li) => li.textContent
        );
      }
      await this.page.keyboard.press("Tab");
      await dropdownHandle!.dispose();
      await inputHandle!.dispose();
    } catch (error) {
      throw new Error(
        `\nSelecting ${dataName} in ${sectionName} section failed ->\n${error}`
      );
    }
  }

  async selectFromDropdown(
    sectionName: string,
    dataName: string,
    listItem: string
  ) {
    const modelSelector = `${sectionName}ViewModel.${dataName}`;
    const selector = `${this.baseSelector}${modelSelector}`;
    try {
      const dropdownHandle = await this.page.waitForSelector(
        `span[aria-owns="${selector}_listbox"]`,
        { visible: true, timeout: 60000 }
      );
      await dropdownHandle!.click({ delay: 5 });

      const selectedOption: string = `li[id="${selector}_option_selected"]`;

      await this.page.waitForSelector(selectedOption, { visible: true });
      let listItemText = await this.page.$eval(
        selectedOption,
        (li) => li.innerHTML
      );

      while (listItemText.toLowerCase() !== listItem.toLowerCase()) {
        await this.page.keyboard.press("ArrowDown");
        listItemText = await this.page.$eval(
          selectedOption,
          (li) => li.innerHTML
        );
      }
      await this.page.keyboard.press("Tab");
    } catch (error) {
      throw new Error(
        `\nSelecting ${dataName} in ${sectionName} section failed ->\n${error}`
      );
    }
  }

  async enterText(
    sectionName: string,
    dataName: string,
    text: string,
    isSlowField: boolean = false
  ) {
    try {
      await this.page.focus(
        `input[id="${this.baseSelector}${sectionName}ViewModel.${dataName}"]`
      );

      const textInputElement = await this.page.waitForSelector(
        `input[id="${this.baseSelector}${sectionName}ViewModel.${dataName}"]`,
        { visible: true }
      );

      isSlowField
        ? await textInputElement!.type(text, { delay: 100 })
        : await textInputElement!.type(text);
    } catch (error) {
      throw new Error(
        `\nEntering ${dataName} in ${sectionName} section ->\n${error}`
      );
    }
  }

  async clickElementWithId(id: string) {
    try {
      const elementHandle = await this.page.waitForSelector(`#${id}`, {
        visible: true,
      });
      if (elementHandle) await elementHandle.click({ delay: 5 });
      if (await this.isReady()) return;
    } catch (error) {
      throw new Error(`\nCould not find element ID ${id} ->\n${error}`);
    }
  }

  async addNewParty(id: string) {
    try {
      const elementHandle = await this.page.waitForSelector(
        `button#btnAddAnotherParty.btn.btn-default.btn-sm.pull-right`,
        { visible: true }
      );
      if (elementHandle) await elementHandle.click({ delay: 5 });
      if (await this.isReady()) return;
    } catch (error) {
      throw new Error(`\nFailed to use Add New Party button ->\n${error}`);
    }
  }

  async clickIsBusinessCheckbox() {
    const checkbox = await this.page.waitForSelector(
      `input[id="${this.baseSelector}PartyViewModel.IsBusiness"]`,
      { visible: true }
    );

    if (checkbox)
      await checkbox
        .click({ delay: 5 })
        .catch((error) => `Clicking checkbox failed ->\n${error}`);
  }

  getFullStateName(stateAbbr: string) {
    return this.stateMap[stateAbbr.toLowerCase()];
  }

  stateMap: any = {
    ca: "California",
    co: "Colorado",
    id: "Idaho",
    or: "Oregon",
    nv: "Nevada",
    ut: "Utah",
    wa: "Washington",
  };

  getFullCountyName(county: string) {
    if (county.toLowerCase() === "multnomah") {
      return "Multnomah Landlord Tenant";
    }
    return county;
  }

  async isReady(): Promise<boolean> {
    let isReady = false;
    await setTimeout(2000);
    while (!isReady) {
      isReady = await this.page.evaluate(() => {
        const elems = document.querySelectorAll(`div.busyIndicatorLayer`);
        for (const elem of elems) {
          if (elem.getAttribute("style") === "display: block;") {
            return false;
          }
        }
        return true;
      });
    }
    return isReady;
  }
}

export interface EfileStatusUpdater {
  (
    botId: number,
    status: "processing" | "failed" | "complete",
    statusMessage: string,
    eFileUrl?: string
  ): Promise<any>;
}

async function updateEfileStatus(
  efileId: number,
  status: "processing" | "failed" | "complete",
  statusMessage: string,
  eFileUrl?: string
) {
  if (!efileId) {
    return;
  }
  const updates: any = {
    status: status,
    statusMessage: statusMessage,
  };
  if (eFileUrl) {
    updates.eFileUrl = eFileUrl;
  }
  await db
    .update(EfileOperations)
    .set(updates)
    .where(eq(EfileOperations.id, efileId));
}
