import { Logtail } from "@logtail/node";

const logtailSourceToken = process.env.BETTERSTACK_SOURCE_TOKEN!;
const isDevelopment = process.env.NODE_ENV !== "production";

class CustomLogger extends Logtail {
  constructor(sourceToken: string) {
    super(sourceToken);
    // Log environment status on initialization
    if (isDevelopment) {
      console.log(
        "[LOGGER] Running in development mode - logs will only be printed to console"
      );
    }
  }

  private logToConsole(level: string, message: any, ...args: any[]) {
    const consoleMethod =
      {
        error: console.error,
        warn: console.warn,
        info: console.log,
        debug: console.debug,
      }[level] || console.log;

    // Handle console logging separately
    consoleMethod(
      `[${level.toUpperCase()}]`,
      message,
      ...(args.length ? args : [])
    );
  }

  error(message: any, ...args: any[]) {
    this.logToConsole("error", message, ...args);
    if (isDevelopment) return Promise.resolve();
    const cleanMessage = this.sanitizeMessage(message);
    const cleanArgs = args.map((arg) => this.sanitizeMessage(arg));
    return super.error(cleanMessage, ...cleanArgs);
  }

  warn(message: any, ...args: any[]) {
    this.logToConsole("warn", message, ...args);
    if (isDevelopment) return Promise.resolve();
    const cleanMessage = this.sanitizeMessage(message);
    const cleanArgs = args.map((arg) => this.sanitizeMessage(arg));
    return super.warn(cleanMessage, ...cleanArgs);
  }

  info(message: any, ...args: any[]) {
    this.logToConsole("info", message, ...args);
    if (isDevelopment) return Promise.resolve();
    const cleanMessage = this.sanitizeMessage(message);
    const cleanArgs = args.map((arg) => this.sanitizeMessage(arg));
    return super.info(cleanMessage, ...cleanArgs);
  }

  debug(message: any, ...args: any[]) {
    this.logToConsole("debug", message, ...args);
    if (isDevelopment) return Promise.resolve();
    const cleanMessage = this.sanitizeMessage(message);
    const cleanArgs = args.map((arg) => this.sanitizeMessage(arg));
    return super.debug(cleanMessage, ...cleanArgs);
  }

  private sanitizeMessage(message: any): any {
    if (message instanceof Error) {
      return {
        message: message.message,
        stack: message.stack,
        name: message.name,
      };
    }

    if (typeof message === "object" && message !== null) {
      try {
        // Test if object can be safely serialized
        JSON.parse(JSON.stringify(message));
        return message;
      } catch (e) {
        // If serialization fails, return a simplified version
        return {
          message: "Object contained circular references - simplified version:",
          data: Object.keys(message).reduce((acc: any, key) => {
            try {
              const value = message[key];
              if (typeof value !== "object" || value === null) {
                acc[key] = value;
              } else {
                acc[key] = "[Complex Object]";
              }
            } catch (e) {
              acc[key] = "[Unserializable]";
            }
            return acc;
          }, {}),
        };
      }
    }

    return message;
  }
}

export const logger = new CustomLogger(logtailSourceToken);
