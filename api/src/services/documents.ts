import { db } from "@api/src/services/db";
import { Document, Documents } from "@shared/db/schema/documents";
import { eq } from "drizzle-orm";
import path from "path";
import { saveFile } from "./files";

export async function createDocRecord({
  caseId,
  documentName,
}: {
  caseId: number;
  documentName: string;
}): Promise<Document> {
  const documentPath = path.join(caseId.toString(), documentName);
  const insertedRecords = await db
    .insert(Documents)
    .values({
      caseId,
      fileName: documentName,
      filePath: documentPath,
      status: "pending",
    })
    .returning();
  return new Document(insertedRecords[0]);
}

export async function updateDocStatus(
  status: "pending" | "failed" | "complete",
  docId: number
) {
  await db
    .update(Documents)
    .set({ status: status })
    .where(eq(Documents.id, docId));
}

export async function saveDocument(
  document: Document,
  data: Buffer
): Promise<void> {
  const filePath = path.join(
    "casedocs",
    String(document?.caseId),
    document.fileName!
  );

  try {
    const savedPath = await saveFile(filePath, data);
    await db
      .update(Documents)
      .set({
        filePath: savedPath,
        status: "complete",
      })
      .where(eq(Documents.id, document.id!));
  } catch (error) {
    console.log(`============= error =============`, error);
  }
}
