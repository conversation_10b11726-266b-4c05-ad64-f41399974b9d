import { db } from "@api/src/services/db";
import { User, Users } from "@shared/db/schema/users";
import { eq } from "drizzle-orm";
import { NextFunction, Request, Response } from "express";

async function isPublic(req: Request, res: Response, next: NextFunction) {
  next();
}

async function isAuthenticated(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (!req.session?.user) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  const user = await db.query.users.findFirst({
    where: eq(Users.id, req.session.user.id!),
  });

  if (!user) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  res.locals.user = new User(user);

  if (!res.locals.user.isActive()) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  next();
}

async function isAdmin(req: Request, res: Response, next: NextFunction) {
  const user = res.locals.user;

  if (!user.isAdmin) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  next();
}

export const publicMixin = [isPublic];
export const authenticatedMixin = [isAuthenticated];
export const adminMixin = [isAuthenticated, isAdmin];
