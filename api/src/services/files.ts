import { createWriteStream } from "fs";
import fs from "fs/promises";
import path from "path";
import uniqueFilename from "unique-filename";

export const basePath = "documents";

export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9 .,-]/g, "") // Whitelist alphanumeric characters, spaces, periods, and commas
    .replace(/\s+/g, " ") // Replace multiple spaces with a single space
    .trim();
}

/**
 * Prepares a file path by ensuring the directory exists and the filename is sanitized and unique.
 * @param filePath - The full path including directory and filename
 * @returns The sanitized and unique file path
 */
export async function prepareFile(filePath: string): Promise<string> {
  // Split the path into directory and filename
  const directory = path.dirname(filePath);
  const storageDirectory = path.resolve(basePath, directory);
  const filename = path.basename(filePath);

  // Ensure the directory exists
  await fs.mkdir(storageDirectory, { recursive: true });

  // Sanitize the filename
  const sanitizedFilename = sanitizeFileName(filename);

  try {
    // Check if the file already exists
    await fs.access(path.join(directory, sanitizedFilename));

    // If we get here, the file exists, so create a unique filename
    const parts = path.parse(sanitizedFilename);
    const uniqueBasename = uniqueFilename("", parts.name);
    const uniquePath = path.join(directory, `${uniqueBasename}${parts.ext}`);

    return uniquePath;
  } catch (error) {
    // File doesn't exist, return the sanitized path
    return path.join(directory, sanitizedFilename);
  }
}

export async function saveFile(
  filePath: string,
  data: Buffer
): Promise<string> {
  const cleanedPath = await prepareFile(filePath);
  const storagePath = path.join(basePath, cleanedPath);

  await fs.writeFile(storagePath, data);

  return cleanedPath;
}

export async function writeStream(
  filePath: string,
  stream: NodeJS.ReadableStream
): Promise<string> {
  const cleanedPath = await prepareFile(filePath);
  const storagePath = path.join(basePath, cleanedPath);

  const writer = createWriteStream(storagePath);
  stream.pipe(writer);
  await new Promise((resolve, reject) => {
    writer.on("finish", resolve);
    writer.on("error", reject);
  });

  return cleanedPath;
}
