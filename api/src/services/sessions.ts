import pgSession from "connect-pg-simple";
import session from "express-session";
import pg from "pg";

const PgSession = pgSession(session);
const cookieDomain = process.env.SSO_COOKIE_DOMAIN;
const cookiePrefix = process.env.SSO_COOKIE_PREFIX;
const cookieName = cookiePrefix
  ? `${cookiePrefix}.andor.connect.sid`
  : "andor.connect.sid";

const pgPool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
});

export default session({
  store: new PgSession({
    pool: pgPool,
    tableName: "sessions",
  }),
  secret: process.env.SESSION_SECRET!,
  resave: false,
  saveUninitialized: true,
  rolling: true,
  name: cookieName,
  cookie: {
    domain: cookieDomain,
    secure: true,
    httpOnly: true,
    path: "/",
    maxAge: 1 * 24 * 60 * 60 * 1000,
  },
});
