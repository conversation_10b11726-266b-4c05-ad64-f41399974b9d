import "@api/src/env";

import { boss, initializeBoss, queues } from "@api/src/services/boss";
import { differenceBy, filter, isEqual, pick } from "lodash";

async function initializeQueues() {
  const fields = ["name", "policy"];
  const bossQueues = await boss.getQueues();

  await Promise.all(
    queues.map(async (queue) => {
      const currentConfig = bossQueues.find((q) => q.name === queue.name);

      if (!currentConfig) {
        return boss.createQueue(queue.name, {
          policy: queue.policy,
          name: queue.name,
        });
      }

      if (!isEqual(pick(currentConfig, fields), pick(queue, fields))) {
        return boss.updateQueue(queue.name, {
          policy: queue.policy,
          name: queue.name,
        });
      }
    })
  );
}

export async function initializeSchedules() {
  const fields = ["name", "cron", "timezone"];
  const bossSchedules = await boss.getSchedules();
  const schedules = filter(queues, "cron");
  const removedSchedules = differenceBy(bossSchedules, schedules, "name");

  await Promise.all(
    schedules.map(async (queue) => {
      const currentConfig = bossSchedules.find((q) => q.name === queue.name);

      if (isEqual(pick(currentConfig, fields), pick(queue, fields))) return;

      await boss.schedule(queue.name, queue.cron, null!, {
        tz: queue.timezone,
      });
    })
  );

  await Promise.all(
    removedSchedules.map(async (queue: any) => {
      await boss.unschedule(queue.name);
    })
  );
}

async function configureQueue() {
  await initializeBoss({ schedule: false });
  await initializeQueues();
  await initializeSchedules();
  await boss.stop();
}

configureQueue();
