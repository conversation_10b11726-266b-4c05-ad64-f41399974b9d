# Byte-compiled / optimized / DLL files

**pycache**/
_.py[cod]
_$py.class

# C extensions

\*.so

# Distribution / packaging

.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
_.egg-info/
.installed.cfg
_.egg

# PyInstaller

# Usually these files are written by a python script from a template

# before PyInstaller builds the exe, so as to inject date/other infos into it.

_.manifest
_.spec

# Installer logs

pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports

htmlcov/
.tox/
.coverage
.coverage._
.cache
nosetests.xml
coverage.xml
_.cover
.hypothesis/

# Translations

_.mo
_.pot

# Django stuff:

_.log
local_settings.py
media/_
sla/\*
sla

# Flask stuff:

instance/
.webassets-cache

# Scrapy stuff:

.scrapy

# Sphinx documentation

docs/\_build/

# PyBuilder

target/

# Jupyter Notebook

.ipynb_checkpoints

# pyenv

.python-version

# celery beat schedule file

celerybeat-schedule

# SageMath parsed files

\*.sage.py

# Temp files

.tmp

# Environments

.env
.venv
env/
venv/
ENV/
docker.env

# Spyder project settings

.spyderproject
.spyproject

# Rope project settings

.ropeproject

# mkdocs documentation

/site

# mypy

.mypy_cache/

# OSX

\*.DS_Store

# VS or whatever editor Kevin is using

.editorconfig
.vscode/settings.json

# media folder

media/\*

# Node modules

node_modules
.dumps

# Collected static

griffin_app/static
