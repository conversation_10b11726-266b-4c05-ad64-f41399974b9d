# Andor Law Case Manager App

## Install Dependencies
* Run `npm install` in the root folder

## Set up PostgreSQL
* Install with [Postgres App](https://postgresapp.com/)
* Create a database named `andor`
* Add your username and password to the `.env` database_url:
`DATABASE_URL="postgress://[username]:[password]@localhost:5432/andor`

## Migrate & Populate Data
* Import database dump

## Create New Tables/Entities
* Create a schema in `/shared/db/schema/your_new_entity.ts`
* Add the new schema to `/api/src/db.ts`
* Run `npx drizzle-kit generate`
* Run `npx drizzle-kit migrate`

## Run
* Run `npm run dev-api`
* Open a second terminal window or tab and run `npm run dev-react`

## Notes
* [React Query Garbage Collection Time](https://tanstack.com/query/latest/docs/framework/react/guides/caching): GC Time is used to force a fresh data load for the form so we aren't editing stale data from the react query cache