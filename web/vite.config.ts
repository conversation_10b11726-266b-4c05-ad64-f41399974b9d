import react from '@vitejs/plugin-react';
import { fileURLToPath, URL } from "url";
import { defineConfig } from 'vite';
import webfontDownload from 'vite-plugin-webfont-dl';

// https://vite.dev/config/
export default defineConfig({
  root: `${process.cwd()}/web`,
  plugins: [
    react(),
    webfontDownload([
      'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap',
      'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Lusitana:wght@400;700&display=swap',
    ]),
  ],
  resolve: {
    alias: {
      "@api": fileURLToPath(new URL("../api", import.meta.url)),
      "@shared": fileURLToPath(new URL("../shared", import.meta.url)),
      "@web": fileURLToPath(new URL(".", import.meta.url)),
    },
  },
  envDir: `${process.cwd()}`,
  server: {
    port: 3000,
    host: '0.0.0.0',
    allowedHosts: true,
    proxy: {
      '/api': {
        target: 'http://localhost:1337'
      },
      '/__': {
        target: 'http://localhost:1337'
      },
    },
  },
  build: {
    outDir: `${process.cwd()}/dist/web`,
    emptyOutDir: true,
  }
})