import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { QueryClient } from "@tanstack/react-query";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 1000 * 60 * 60 * 24, // 24 hours
    },
  },
});

export const persister = createSyncStoragePersister({
  storage: window.localStorage,
});

const dehydratedQueryKeys = ["auth", "base-data"];

const persistOptions = {
  persister: persister,
  maxAge: 1000 * 60 * 60 * 24, // Persist for 24 hours
  dehydrateOptions: {
    shouldDehydrateQuery: (query) =>
      dehydratedQueryKeys.includes(query.queryKey[0]), // Replace with your query key
  },
};

export const clearCache = async () => {
  await persister.removeClient();
};

export function AndorQueryClientProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={persistOptions}
    >
      {children}
    </PersistQueryClientProvider>
  );
}
