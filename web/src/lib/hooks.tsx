import { County } from "@shared/db/schema/counties";
import { User } from "@shared/db/schema/users";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { clearCache } from "@web/src/lib/query";
import axios from "axios";
import { identity, isEqual, pickBy, some } from "lodash";
import queryString from "query-string";
import { useEffect, useMemo, useState } from "react";
import { useMatches, useSearchParams } from "react-router-dom";
import { useWindowSize } from "usehooks-ts";

export const useBasePath = (id) => {
  const matches = useMatches();

  return matches.find((match) => match.id === id)?.pathname;
};

export const useUrlQuery = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const query = searchParams.toString();
  const params = queryString.parse(query);

  const updateParams = (updates) => {
    const newParams = pickBy({ ...params, ...updates }, identity);

    if (!isEqual(newParams, params)) setSearchParams(newParams);
  };

  return { params, updateParams, query };
};

export const useSSO = () => {
  const queryClient = useQueryClient();
  const [user, setUser] = useState<User | null>(null);

  const { data, isError, refetch, ...passedProps } = useQuery({
    queryKey: ["auth"],
    queryFn: async () => {
      const response = await axios.get("/__/auth/data");
      return response.data;
    },
    retry: false,
    staleTime: 1000 * 30,
  });

  const revalidate = async () => {
    await clearCache();
    queryClient.setQueryData(["auth"], null);
    queryClient.invalidateQueries({ queryKey: ["auth"] });
  };

  const signIn = async () => {
    await revalidate();
    const currentPath = window.location.pathname;
    const query = queryString.stringify({ next: currentPath });
    window.location.assign(`/__/auth/login?${query}`);
  };
  const signOut = async () => {
    try {
      await axios.get("/__/auth/logout");
    } catch (error) {}

    await revalidate();
    queryClient.clear();
  };

  useEffect(() => {
    if (!data?.user) {
      setUser(null);
    } else if (isError) {
      revalidate();
    } else {
      try {
        setUser(new User(data.user));
      } catch (error) {
        revalidate();
      }
    }
  }, [isError, data?.user]);

  return {
    user,
    signIn,
    signOut,
    ...passedProps,
  };
};

export const useResponsive = () => {
  const { width } = useWindowSize();

  return {
    isPhone: width < 576,
    isTablet: width >= 576 && width <= 768,
    isDesktop: width > 768,
  };
};

export const useBaseData = () => {
  const { data, ...queryProps } = useQuery({
    queryKey: ["base-data"],
    queryFn: async () => {
      const response = await axios.get("/api/base/data");
      return response.data;
    },
    staleTime: 1000 * 30,
  });
  const counties = useMemo(() => {
    if (!data?.counties) return null;
    return data.counties.map((county) => new County(county));
  }, [data?.counties]);

  return { data: { ...data, counties }, ...queryProps };
};

export const useDocuments = (params: any = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["documents", params],
    queryFn: async (): Promise<{ documents: any; count: number }> => {
      const response = await axios.get("/api/documents", { params });

      return {
        documents: response.data.documents,
        count: response.data.documents.length,
      };
    },
    refetchInterval: (query) => {
      const currentDocuments = [...(query.state.data?.documents || [])];
      const hasPendingDocuments = some(currentDocuments, { status: "pending" });

      return hasPendingDocuments ? 1000 * 1 : 1000 * 120;
    },
  });

  return {
    documents: data?.documents || [],
    count: data?.count,
    ...passedProps,
  };
};

export const useRenderTemplate = (mutationOptions: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: ({
      templateId,
      caseId,
      ...options
    }: {
      templateId: string;
      caseId: string;
      [key: string]: any;
    }) => axios.post(`/api/templates/${templateId}/render/${caseId}`, options),
    mutationKey: ["render-document"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["documents"] }),
    ...mutationOptions,
  });

  return { renderTemplate: mutateAsync, ...mutationProps };
};

export const useRunEfile = (caseId) => {
  const queryClient = useQueryClient();

  const { mutateAsync } = useMutation({
    mutationFn: () => axios.post(`/api/efile/${caseId}`),
    mutationKey: ["run-efile", caseId],
    onSettled: () =>
      queryClient.invalidateQueries({ queryKey: ["efileOperations"] }),
  });

  return { runEfile: mutateAsync };
};

export const useEfile = (params: object = {}) => {
  const { data, ...passedParams } = useQuery({
    queryKey: ["efileOperations", params],
    queryFn: async (): Promise<{ efileOperations: any; count: number }> => {
      const response = await axios.get("/api/efile", { params });

      return response.data;
    },
    refetchInterval: (query) => {
      const currentEfilings = [...(query.state.data?.efileOperations || [])];
      const isProcessing = some(currentEfilings, { status: "processing" });

      return isProcessing ? 1000 * 1 : 1000 * 60;
    },
  });

  return {
    efilings: data?.efileOperations || [],
    count: data?.count || 0,
    ...passedParams,
  };
};

export const useCountdown = (onComplete: (() => void) | null = null) => {
  const [countdown, setCountdown] = useState(0);

  const triggerCountdown = async (milliseconds: number = 5000) => {
    setCountdown(milliseconds);
  };

  useEffect(() => {
    let timeout;

    if (countdown > 0)
      timeout = setTimeout(() => setCountdown(countdown - 10), 10);
    if (countdown <= 0) {
      setCountdown(0);
      if (onComplete) onComplete();
    }

    return () => clearTimeout(timeout);
  }, [countdown, onComplete]);

  return { countdown, triggerCountdown };
};
