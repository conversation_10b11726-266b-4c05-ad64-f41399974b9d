import "@web/src/styles/styles.css";
import ReactD<PERSON> from "react-dom/client";
import { RouterProvider } from "react-router-dom";
import { AndorQueryClientProvider } from "./lib/query";
import LoginWrapper from "./pages/login";
import { router } from "./pages/routes";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <AndorQueryClientProvider>
    <LoginWrapper>
      <RouterProvider router={router} />
    </LoginWrapper>
  </AndorQueryClientProvider>
);
