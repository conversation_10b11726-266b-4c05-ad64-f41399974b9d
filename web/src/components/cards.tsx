export default function EfileCard({ efile }) {
  const statusColor =
    efile.status === "complete"
      ? "text-success"
      : efile.status === "failed"
      ? "text-error"
      : "";
  return (
    <div className={`card card-bordered bg-slate-100 card-compact mb-4`}>
      <div className="card-body flex flex-row justify-between">
        <div>
          <h2 className={`card-title ${statusColor}`}>
            {efile.status}{" "}
            {efile.status === "processing" && (
              <span className="loading loading-infinity"></span>
            )}
          </h2>
          <p>{efile.statusMessage}</p>
        </div>
        {efile.status === "complete" ? (
          <div className="card-actions items-center">
            <a
              href={efile.eFileUrl}
              target="_blank"
              className="btn btn-primary btn-sm"
            >
              View Draft
            </a>
          </div>
        ) : null}
      </div>
    </div>
  );
}
