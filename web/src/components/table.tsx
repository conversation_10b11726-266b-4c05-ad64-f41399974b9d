import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline";
import { preventDefault, stopPropagation } from "@shared/utils";
import { Button } from "@web/src/components/buttons";
import { useUrlQuery } from "@web/src/lib/hooks";
import classNames from "classnames";
import {
  ceil,
  clamp,
  first,
  includes,
  max,
  min,
  omit,
  pickBy,
  size,
  times,
  union,
  values,
  without,
} from "lodash";
import { useEffect, useMemo, useState } from "react";
function SelectableTDComponent({ value, index, ...props }) {
  const {
    selected,
    setSelected,
    lastSelected,
    setLastSelected,
    selectable,
    setSelectable,
    singleSelect,
  } = SelectableTDComponent.api;
  const checked = includes(selected, value);

  const addSelectable = (value, index) => {
    setSelectable((current) => ({ ...current, [index]: value }));
  };

  const removeSelectable = (index) => {
    setSelectable((current) => omit(current, index));
  };

  const handleSelect = (event) => {
    const checking = !checked;

    if (singleSelect) {
      setSelected([value]);
      return;
    }

    if (!checking) {
      setSelected(without(selected, value));
      setLastSelected(null);
      return;
    }

    if (event.nativeEvent.shiftKey && lastSelected !== null) {
      const startPoint = lastSelected < index ? lastSelected : index;
      const endPoint = lastSelected < index ? index : lastSelected;
      const selections = values(
        pickBy(selectable, (value, key) => startPoint <= key && key <= endPoint)
      );

      setSelected(union(selected, selections));
    } else {
      setSelected(union(selected, [value]));
    }

    setLastSelected(index);
  };

  useEffect(() => {
    addSelectable(value, index);

    return () => removeSelectable(index);
  }, []);

  const type = singleSelect ? "radio" : "checkbox";

  return (
    <td onClick={preventDefault(handleSelect)} className="align-middle">
      <div className="d-flex align-items-center justify-content-center w-full h-full">
        <input
          {...props}
          type={type}
          onChange={handleSelect}
          onClick={stopPropagation()}
          checked={checked}
        />
      </div>
    </td>
  );
}

function SelectableTHComponent({ className, ...props }) {
  const { selected, setSelected, selectable } = SelectableTHComponent.api;
  const anyItemsChecked = size(selected) > 0;

  const handleSelectHeader = () => {
    if (anyItemsChecked) {
      setSelected([]);
    } else {
      setSelected(values(selectable));
    }
  };

  return (
    <th onClick={handleSelectHeader} className={classNames("align-middle shrink-table-cell", className)}>
      <div className="d-flex align-items-center justify-content-center w-full h-full">
        <input
          {...props}
          type="checkbox"
          onChange={handleSelectHeader}
          checked={anyItemsChecked}
        />
      </div>
    </th>
  );
}

function SortableTHComponent({ children, sortKey, className = "", ...props }) {
  const { sort, setSort } = SortableTHComponent.api;
  const descendingKey = `-${sortKey}`;
  const isAscending = sort === sortKey;
  const isDescending = sort === descendingKey;

  const handleClick = (event) => {
    if (isAscending) return setSort(descendingKey);
    if (isDescending) return setSort(null);

    setSort(sortKey);
  };

  return (
    <th
      {...props}
      className={classNames(className, "shrink-table-cell")}
      onClick={preventDefault(handleClick)}
    >
      <div className="flex items-center">
        <a href="#">{children}</a>

        {isAscending && (
          <ChevronUpIcon height={16} width={16} className="ml-1" />
        )}
        {isDescending && (
          <ChevronDownIcon height={16} width={16} className="ml-1" />
        )}
      </div>
    </th>
  );
}

function TableComponent({ children, ...props }) {
  return <table {...props}>{children}</table>;
}

function PagerComponent({
  className = "",
  showPages = true,
  showFirstLast = true,
  showCount = false,
}) {
  const { lastPage, currentPage, pageSize, totalRecordCount, setCurrentPage } =
    PagerComponent.api;
  const startPage = clamp(currentPage - 5, 1, max([lastPage - 10, 1]));
  const startPageOffset = (currentPage - 1) * pageSize + 1;
  const endPageOffset = min([currentPage * pageSize, totalRecordCount]);

  return (
    <div className={classNames("flex justify-between mx-4", className)}>
      <div className="flex">
        {showFirstLast && (
          <Button
            className="mr-3 flex items-center"
            disabled={currentPage < 2}
            onClick={() => setCurrentPage(1)}
          >
            <ChevronDoubleLeftIcon height={20} width={20} />
          </Button>
        )}
        <Button
          className="mr-3 flex items-center"
          disabled={currentPage < 2}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          <ChevronLeftIcon height={20} width={20} />
        </Button>
      </div>
      <div className="flex items-center">
        {showPages &&
          times(min([lastPage, 11]), (offset) => {
            const pageNumber = startPage + offset;

            return (
              <Button
                key={pageNumber}
                className={classNames("mx-1", {
                  "btn-active": pageNumber === currentPage,
                })}
                onClick={() => setCurrentPage(pageNumber)}
              >
                <span>{pageNumber}</span>
              </Button>
            );
          })}
        {showCount && (
          <div className="mx-3">
            {startPageOffset} - {endPageOffset} of {totalRecordCount}
          </div>
        )}
      </div>
      <div className="flex">
        <Button
          className="ml-3 flex items-center"
          disabled={currentPage === lastPage}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          <ChevronRightIcon height={20} width={20} />
        </Button>
        {showFirstLast && (
          <Button
            className="ml-3 flex items-center"
            disabled={currentPage === lastPage}
            onClick={() => setCurrentPage(lastPage)}
          >
            <ChevronDoubleRightIcon height={20} width={20} />
          </Button>
        )}
      </div>
    </div>
  );
}

interface UseTableArguments {
  defaultSort?: string;
  onSort?: Function;
  onPageChange?: Function;
  defaultPage?: number;
  pageSize?: number;
  totalRecordCount?: number;
  singleSelect?: Boolean;
  data?: Array<any>;
}

export const useTable = ({
  defaultSort,
  onSort,
  onPageChange,
  defaultPage = 1,
  pageSize = 30,
  totalRecordCount = 0,
  singleSelect = false,
}: UseTableArguments) => {
  const [selected, setSelected] = useState([]);
  const [lastSelected, setLastSelected] = useState(null);
  const [selectable, setSelectable] = useState({});
  const [sort, setSort] = useState(defaultSort);
  const [currentPage, setCurrentPage] = useState(defaultPage);
  const lastPage = ceil(totalRecordCount / pageSize);

  const handleSort = (newSort) => {
    setSort(newSort);
    if (onSort) onSort(newSort);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    if (onPageChange) onPageChange(newPage);
  };

  useEffect(() => {
    if (lastPage && currentPage > lastPage) handlePageChange(1);
  }, [currentPage > lastPage]);

  const api = {
    selected,
    setSelected,
    lastSelected,
    setLastSelected,
    selectable,
    setSelectable,
    sort,
    setSort: handleSort,
    currentPage,
    lastPage,
    singleSelect,
    totalRecordCount,
    pageSize,
    setCurrentPage: handlePageChange,
  };

  const SelectableTD = useMemo(() => SelectableTDComponent, []);
  SelectableTD.api = api;

  const SelectableTH = useMemo(() => SelectableTHComponent, []);
  SelectableTH.api = api;

  const SortableTH = useMemo(() => SortableTHComponent, []);
  SortableTH.api = api;

  const Pager = useMemo(() => PagerComponent, []);
  PagerComponent.api = api;

  const Table = useMemo(() => TableComponent, []);
  TableComponent.api = api;

  return {
    Table,
    SelectableTD,
    SelectableTH,
    SortableTH,
    Pager,
    selected: singleSelect ? first(selected) : selected,
    setSelected,
    sort,
  };
};

export const useTableWithQueryParams = ({
  defaultSort,
  defaultPage,
  data = [],
  ...props
}: UseTableArguments) => {
  const { params, updateParams } = useUrlQuery();

  return useTable({
    defaultSort: (params.orderBy as string) || defaultSort,
    onSort: (orderBy) => updateParams({ orderBy }),
    defaultPage: Number(params.page) || defaultPage,
    onPageChange: (page) => updateParams({ page }),
    ...props,
  });
};
