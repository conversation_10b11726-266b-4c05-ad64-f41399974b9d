import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { EmailTemplate } from "@shared/db/schema/email-templates";
import dayjs from "dayjs";
import { useBaseData } from "../lib/hooks";
import { useCase } from "../pages/cases/hooks";
import { encodeMailto } from "../pages/cases/utils";
import { useIntakeRequest } from "../pages/intake-requests/hooks";

export const EmailButton = ({
  templateType,
  caseId,
}: {
  templateType: string;
  caseId?: number;
}) => {
  const { data } = useBaseData();
  const { caseData } = useCase(String(caseId));
  const { intakeRequest } = useIntakeRequest(
    String(caseData?.intakeFormResponseId!)
  );
  // TODO: For now define variables here as we need them.
  // Later think about more dynamic ways of dealing with this.
  const variables = {
    county: caseData?.property?.county,
    dateFA: dayjs(caseData?.caseDates?.dateFA).format("MMM D, YYYY"),
    faTime: dayjs(caseData?.caseDates?.dateFA).format("h:mm A"),
  };
  const templates = data.emailTemplates.filter(
    (template) => template.type === templateType
  );
  if (templates.length === 1) {
    const template = new EmailTemplate(templates[0]);
    const mailTo = encodeMailto(
      template,
      intakeRequest,
      caseData?.property?.contacts!,
      variables
    );

    return (
      <a
        key={template.id}
        href={mailTo}
        target="_blank"
        className="btn btn-primary"
      >
        {template.name}
      </a>
    );
  }

  return (
    <div>
      <details className="dropdown dropdown-end">
        <summary className="btn btn-primary rounded-md">
          Email Templates
          <span>
            <ChevronDownIcon height={24} width={24} className="inline ml-10" />
          </span>
        </summary>
        <ul className="menu dropdown-content z-10 bg-base-100 rounded-md p-2 shadow-md">
          {templates.map((template) => {
            const newTemplate = new EmailTemplate(template);
            const mailTo = encodeMailto(
              newTemplate,
              intakeRequest,
              caseData?.property?.contacts!,
              variables
            );
            return (
              <li key={template.id}>
                <a
                  className="rounded-md"
                  key={template.id}
                  href={mailTo}
                  target="_blank"
                >
                  {template.name}
                </a>
              </li>
            );
          })}
        </ul>
      </details>
    </div>
  );
};
