import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DebouncedTextInput } from './utils';

// Mock the useDebounce hook
jest.mock('../lib/hooks', () => ({
  useDebounce: jest.fn((value, delay) => {
    // For testing, we'll return the value immediately
    // In a real test, you might want to test the actual debouncing behavior
    return value;
  }),
}));

describe('DebouncedTextInput', () => {
  it('should render with initial value', () => {
    const mockOnChange = jest.fn();
    
    render(
      <DebouncedTextInput
        label="Test Input"
        name="test"
        value="initial value"
        placeholder="Enter text..."
        onChange={mockOnChange}
      />
    );
    
    const input = screen.getByDisplayValue('initial value');
    expect(input).toBeInTheDocument();
  });

  it('should call onChange when user types', async () => {
    const mockOnChange = jest.fn();
    
    render(
      <DebouncedTextInput
        label="Test Input"
        name="test"
        value=""
        placeholder="Enter text..."
        onChange={mockOnChange}
      />
    );
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new text' } });
    
    // Since we mocked useDebounce to return immediately, onChange should be called
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('new text');
    });
  });

  it('should update internal state when external value changes', () => {
    const mockOnChange = jest.fn();
    
    const { rerender } = render(
      <DebouncedTextInput
        label="Test Input"
        name="test"
        value="initial"
        placeholder="Enter text..."
        onChange={mockOnChange}
      />
    );
    
    // Update the external value
    rerender(
      <DebouncedTextInput
        label="Test Input"
        name="test"
        value="updated"
        placeholder="Enter text..."
        onChange={mockOnChange}
      />
    );
    
    const input = screen.getByDisplayValue('updated');
    expect(input).toBeInTheDocument();
  });
});
