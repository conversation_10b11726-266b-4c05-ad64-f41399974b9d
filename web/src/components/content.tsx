import { LinkIcon } from "@heroicons/react/24/outline";
import {
  getRelativeText,
  stopPropagation,
  urlEncodeAddress,
} from "@shared/utils";
import classNames from "classnames";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import { map, split, startsWith, take, truncate, uniqueId } from "lodash";
import { useState } from "react";
import { Link } from "react-router-dom";
import { Tooltip } from "./utils";

dayjs.extend(advancedFormat);

export function TextWithLinks({
  text,
  truncateLength = 35,
  style = undefined,
}) {
  return (
    <>
      {map(split(text, "\n"), (line, lineIndex) => (
        <span key={lineIndex}>
          {map(split(line, " "), (word, wordIndex) => {
            if (!startsWith(word, "http")) return word + " ";

            return (
              <span key={wordIndex}>
                <a
                  onClick={stopPropagation()}
                  href={word}
                  target="blank"
                  style={style}
                >
                  {truncate(word, { length: truncateLength })}
                </a>
              </span>
            );
          })}
        </span>
      ))}
    </>
  );
}

export function ShortDate({
  date,
  shortFormat = "M/D/YY",
  longFormat = "MMMM Do, YYYY",
}) {
  if (!date) return null;

  const hasNoTime =
    dayjs(date).get("hour") === 0 && dayjs(date).get("minute") === 0;

  const finalShortFormat = hasNoTime ? shortFormat : `${shortFormat} h:mm a`;
  const finalLongFormat = hasNoTime ? longFormat : `${longFormat} h:mm a`;

  return (
    <Tooltip Element="span" title={dayjs(date).format(finalLongFormat)}>
      {dayjs(date).format(finalShortFormat)}
    </Tooltip>
  );
}

export function TagDot({ tag, style, ...props }) {
  const [id] = useState(uniqueId("tooltip-"));
  const styles = {
    fontSize: "1rem",
    backgroundColor: tag.color,
    color: getRelativeText(tag.color),
    ...style,
  };

  return (
    <>
      <UncontrolledTooltip target={`#${id}`}>{tag.label}</UncontrolledTooltip>
      <div
        id={id}
        style={styles}
        className={`cursor-pointer font-weight-light badge-pill badge my-1`}
        {...props}
      >
        {take(tag.label, 1)}
      </div>
    </>
  );
}

export function Tag({
  tag,
  style = {},
  children = null,
  className = "",
  ...props
}: {
  tag: { color: string; label: string };
  style?: React.CSSProperties;
  children?: React.ReactNode;
  className?: string;
} & React.ComponentProps<"div">) {
  const styles = {
    fontSize: "1rem",
    backgroundColor: tag.color,
    color: getRelativeText(tag.color),
    ...style,
  };

  return (
    <div
      style={styles}
      className={classNames(
        "cursor-pointer font-weight-light badge-pill badge my-1",
        className
      )}
      {...props}
    >
      {children || tag.label}
    </div>
  );
}

export function AddressLink({
  ...props
}: {
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  county?: string | null;
  court?: string | null;
} & React.ComponentProps<"a">) {
  return (
    <a
      href={`https://maps.google.com/?q=${urlEncodeAddress(props)}`}
      className="text-sm inline-block hover:underline"
      target="_blank"
      {...props}
    >
      {props.address1}
      {props.address2 && <>, {props.address2}</>}
      <br />
      {props.city}, {props.state} {props.zip}
      {props.county && (
        <>
          <br />
          {props.county} County
        </>
      )}
      {props.court && (
        <>
          <br />
          {props.court}
        </>
      )}
    </a>
  );
}

export function ThemedLink({
  children,
  color,
  className,
  ...props
}: {
  children: React.ReactNode;
  color?: string;
  className?: string;
} & React.ComponentProps<typeof Link>) {
  const colors = {
    default: {
      decoration: "!decoration-gray-300",
      bg: "bg-gray-200",
      text: "text-gray-200",
    },
    primary: {
      decoration: "!decoration-primary",
      bg: "bg-primary/[.65] group-hover:bg-primary",
      text: "text-primary",
    },
    accent: {
      decoration: "!decoration-accent",
      bg: "bg-accent/[.65] group-hover:bg-accent",
      text: "text-accent",
    },
    secondary: {
      decoration: "!decoration-secondary",
      bg: "bg-secondary/[.65] group-hover:bg-secondary",
      text: "text-secondary",
    },
    info: {
      decoration: "!decoration-[#0e7490]",
      bg: "bg-[#0e7490]/[.65] group-hover:bg-[#0e7490]",
      text: "text-[#0e7490]",
    },
    counties: {
      decoration: "!decoration-[#f39c12]",
      bg: "bg-[#f39c12]/[.65] group-hover:bg-[#f39c12]",
      text: "text-[#f39c12]",
    },
  };

  const colorMap = color ? colors[color] : colors.default;

  return (
    <Link
      className={classNames("themed-link", colorMap.decoration, className)}
      {...props}
    >
      <div className="group flex items-center gap-[8px]">
        <div style={{ width: "1.5rem", height: "1.5rem" }}>
          <LinkIcon
            width="1.5rem"
            height="1.5rem"
            className={classNames(
              colorMap.bg,
              "text-white",
              "p-[5px]",
              "rounded-full",
              "transition-all"
            )}
          />
        </div>
        <div className="flex-grow min-w-0">{children}</div>
      </div>
    </Link>
  );
}

export function TruncateText({
  children,
  noTooltip = false,
  maxWidth,
  ...props
}: {
  children: React.ReactNode;
  noTooltip?: boolean;
  maxWidth?: string | number;
}) {
  const style: any = {};

  if (maxWidth) style.maxWidth = maxWidth;

  return (
    <Tooltip title={noTooltip ? null : children}>
      <div
        className="overflow-ellipsis overflow-x-hidden whitespace-nowrap max-w-full block"
        style={style}
        {...props}
      >
        {children}
      </div>
    </Tooltip>
  );
}

export function Placeholder({
  children,
  className,
  minHeight = 250,
}: {
  children?: any;
  className?: string;
  minHeight?: number;
}) {
  return (
    <div
      style={{ minHeight }}
      className={classNames(
        "w-full min-h-30 bg-gray-300 rounded flex items-center justify-center",
        className
      )}
    >
      <span className="text-primary text-xl">{children || "Placeholder"}</span>
    </div>
  );
}

export function CountBadge({
  count,
  className,
  showZero = true,
}: {
  count: number | undefined;
  className?: string;
  showZero?: boolean;
}) {
  // Don't render anything if count is undefined or null
  if (count === undefined || count === null) return null;

  // Don't render if count is zero and showZero is false
  if (count === 0 && !showZero) return null;

  // Use primary badge for counts > 0, muted badge for zero
  const badgeClass =
    count > 0
      ? "badge badge-sm badge-primary ml-2"
      : "badge badge-sm badge-muted ml-2";

  return <span className={classNames(badgeClass, className)}>{count}</span>;
}
