import { ChatBubbleLeftIcon, PencilIcon } from "@heroicons/react/24/outline";
import { COMMENT_TYPES } from "@shared/constants";
import { Comment } from "@shared/db/schema/comments";
import {
  HookExpandingTextarea,
  HookForm,
} from "@web/src/components/forms/forms";
import dayjs from "dayjs";
import { useState } from "react";
import { Button } from "react-daisyui";
import { useForm } from "react-hook-form";
import { useSSO } from "../lib/hooks";
import {
  useComments,
  useCreateComment,
  useUpdateComment,
} from "../pages/intake/hooks";

export function CommentView({
  comment,
  caseId,
  allowNewComment = false,
}: {
  comment?: Comment;
  caseId?: number;
  allowNewComment?: boolean;
}) {
  console.log(`============= CommentView =============`, allowNewComment);
  const { user } = useSSO();
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [updatingComment, setUpdatingComment] = useState(false);
  const { createComment } = useCreateComment();
  const { updateComment } = useUpdateComment();
  const onSubmit = (data) => {
    updatingComment ? updateComment(data) : createComment(data);
    setShowCommentForm(false);
    setUpdatingComment(false);
  };

  return (
    <>
      {!showCommentForm && (
        <div
          className={`flex items-center ${
            comment ? "justify-between" : "justify-end"
          }`}
        >
          {comment && <span>{comment!.description}</span>}

          <div className="flex items-center">
            {allowNewComment && (
              <ChatBubbleLeftIcon
                className="inline h-5 w-5 ml-3"
                onClick={() => {
                  setUpdatingComment(false);
                  setShowCommentForm(true);
                }}
              />
            )}

            {comment &&
              comment.type === "manual" &&
              user?.id == comment!.staffId && (
                <PencilIcon
                  className="inline h-5 w-5 ml-3"
                  onClick={() => {
                    setUpdatingComment(true);
                    setShowCommentForm(true);
                  }}
                />
              )}
          </div>
        </div>
      )}

      {showCommentForm && (
        <CommentForm
          comment={
            updatingComment
              ? comment
              : {
                  caseId: caseId,
                  staffId: user?.id,
                  description: "",
                }
          }
          workflowCheck={comment?.workflowCheck || undefined}
          onSubmit={onSubmit}
        >
          <Button className="btn-primary btn-xs" type="submit">
            {updatingComment ? "Update" : "Add"}
          </Button>
          <Button
            className="btn-ghost btn-xs"
            onClick={() => {
              setShowCommentForm(false);
            }}
          >
            Cancel
          </Button>
        </CommentForm>
      )}
    </>
  );
}

export function CommentListView({
  caseId,
  workflowCheck,
  allowNewComment,
}: {
  caseId: number;
  workflowCheck?: string;
  allowNewComment?: boolean;
}) {
  const { comments } = useComments({
    caseId: String(caseId),
    workflowCheck,
  });
  let comment: Comment | undefined;
  if (comments && comments.length > 0)
    comment = comments.find((c) => c.type === "manual");
  console.log(`============= allowNewComment =============`, allowNewComment);
  return (
    <>
      <CommentView
        comment={comment}
        caseId={caseId}
        allowNewComment={allowNewComment || true}
      />

      {comment && (
        <div className={`flex items-center mt-1 text-xs text-gray-500`}>
          <span className="font-medium">{comment?.user?.name}</span>
          <span className="mx-1">•</span>
          <span>{dayjs(comment?.updatedAt).format("M/D/YYYY")}</span>
          <span className="mx-1">•</span>
          <span>{dayjs(comment?.updatedAt).format("h:mm A")}</span>
        </div>
      )}
    </>
  );
}

export function CommentLogView({ comment }: { comment: Comment }) {
  return (
    <div
      key={comment.id}
      className={`flex flex-col mb-4 ${
        comment.type === "auto" ? "items-start" : "items-end"
      }`}
    >
      <div
        className={`px-3 py-2 rounded-lg text-sm max-w-[80%] ${
          comment.type === "auto"
            ? "bg-gray-200 text-black"
            : "bg-blue-500 text-white"
        }`}
      >
        <CommentView comment={comment} />
      </div>

      <div
        className={`flex items-center mt-1 text-xs text-gray-500 ${
          comment.type === "auto" ? "ml-2" : "mr-2"
        }`}
      >
        <span className="font-medium">{comment.user?.name}</span>
        <span className="mx-1">•</span>
        <span>{dayjs(comment?.updatedAt).format("M/D/YYYY")}</span>
        <span className="mx-1">•</span>
        <span>{dayjs(comment?.updatedAt).format("h:mm A")}</span>
      </div>
    </div>
  );
}

export function CommentForm({
  comment,
  onSubmit,
  workflowCheck,
  children: buttons,
}: {
  comment: any;
  workflowCheck?: string;
  onSubmit: any;
  children: any;
}) {
  const defaultValues = {
    description: comment?.description || "",
    caseId: comment?.caseId,
    staffId: comment?.staffId,
    id: comment.id,
    type: COMMENT_TYPES.MANUAL,
    workflowCheck: workflowCheck || null,
  };
  const formContext = useForm({ defaultValues });
  const handleSubmit = () => {
    formContext.handleSubmit((data) => {
      onSubmit(data);
      formContext.reset();
    })();
  };
  return (
    <HookForm context={formContext} onSubmit={handleSubmit}>
      <div className="flex gap-3 items-center">
        <HookExpandingTextarea
          name="description"
          placeholder="New comment..."
          minRows={1}
          rules={{ required: true }}
        />
        {buttons}
      </div>
    </HookForm>
  );
}
