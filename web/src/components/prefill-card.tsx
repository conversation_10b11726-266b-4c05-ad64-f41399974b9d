import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import classNames from "classnames";
import { ReactNode, useState } from "react";
import { Card } from "react-daisyui";

interface PrefillCardProps {
  title: string;
  children: ReactNode;
  complete?: boolean;
  match?: boolean | null;
}

export default function PrefillCard({
  title,
  children,
  complete = false,
  match = null,
}: PrefillCardProps) {
  // Internal state for skipped status
  const [skipped, setSkipped] = useState(false);

  // Only consider complete cards as de-emphasized for styling purposes
  const isDeemphasized = complete || skipped;

  // Handle skip action - toggle skipped state
  const handleSkip = () => {
    setSkipped(!skipped);
  };

  return (
    <Card
      className={classNames("rounded bg-slate-100", {
        "opacity-75": isDeemphasized,
      })}
    >
      <Card.Body className="px-3 py-2">
        <Card.Title
          className={classNames(
            "text-md font-light flex items-center justify-between",
            {
              "pb-1 border-b border-b-slate-300": !isDeemphasized,
              "text-slate-500": isDeemphasized,
            }
          )}
        >
          <div className="flex items-center gap-2">
            {match !== null &&
              (match || complete ? (
                <span className="text-green-600">
                  <CheckCircleIcon className="h-4 w-4" />
                </span>
              ) : (
                <span className="text-red-500">
                  <ExclamationCircleIcon className="h-4 w-4" />
                </span>
              ))}
            <span>{title}</span>
          </div>
          <div className="flex items-center gap-2">
            {complete && (
              <span className="text-green-600 flex items-center text-sm">
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                Complete
              </span>
            )}
            {skipped && (
              <span className="text-slate-500 flex items-center text-sm mr-2">
                <XCircleIcon className="h-4 w-4 mr-1" />
                Skipped
              </span>
            )}
            {!complete && (
              <button
                onClick={handleSkip}
                className="text-slate-500 hover:text-slate-700 text-xs"
                type="button"
              >
                {skipped ? "Undo Skip" : "Skip"}
              </button>
            )}
          </div>
        </Card.Title>
        {!isDeemphasized && <div className="py-2">{children}</div>}
      </Card.Body>
    </Card>
  );
}
