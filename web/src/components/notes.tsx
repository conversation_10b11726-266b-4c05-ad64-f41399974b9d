import { ChatBubbleLeftIcon } from "@heroicons/react/24/outline";
import { IntakeFormResponse } from "@shared/db/schema/intake-form-responses";
import {
  HookExpandingTextarea,
  HookForm,
} from "@web/src/components/forms/forms";
import { useState } from "react";
import { Button } from "react-daisyui";
import { useForm } from "react-hook-form";
import { useUpdateRequests } from "../pages/intake-requests/hooks";

export function NotesView({
  intakeRequest,
  allowEdit = false,
}: {
  intakeRequest: IntakeFormResponse;
  allowEdit?: boolean;
}) {
  const [showNotesForm, setShowNotesForm] = useState(false);
  const { update } = useUpdateRequests();

  const onSubmit = (data) => {
    update({
      ids: [intakeRequest.id],
      data: { notes: data.notes },
    });
    setShowNotesForm(false);
  };

  return (
    <>
      {!showNotesForm && (
        <div
          className={`flex items-center ${
            intakeRequest.notes ? "justify-between" : "justify-end"
          }`}
        >
          {intakeRequest.notes && <span>{intakeRequest.notes}</span>}

          <div className="flex items-center">
            {allowEdit && (
              <ChatBubbleLeftIcon
                className="inline h-5 w-5 ml-3"
                onClick={() => {
                  setShowNotesForm(true);
                }}
              />
            )}
          </div>
        </div>
      )}

      {showNotesForm && (
        <NotesForm notes={intakeRequest.notes || ""} onSubmit={onSubmit}>
          <Button className="btn-primary btn-xs" type="submit">
            Add
          </Button>
          <Button
            className="btn-ghost btn-xs"
            onClick={() => {
              setShowNotesForm(false);
            }}
          >
            Cancel
          </Button>
        </NotesForm>
      )}
    </>
  );
}

export function NotesListView({
  intakeRequest,
}: {
  intakeRequest: IntakeFormResponse;
}) {
  if (!intakeRequest) return null;

  return (
    <>
      <NotesView intakeRequest={intakeRequest} allowEdit={true} />

      {intakeRequest.notes && (
        <div className={`flex items-center mt-1 text-xs text-gray-500`}>
          <span className="font-medium">
            {intakeRequest.updatedAt?.toLocaleDateString()}
          </span>
          <span className="mx-1">•</span>
          <span>{intakeRequest.updatedAt?.toLocaleTimeString()}</span>
        </div>
      )}
    </>
  );
}

export function NotesForm({
  notes,
  onSubmit,
  children: buttons,
}: {
  notes: string;
  onSubmit: any;
  children: any;
}) {
  const defaultValues = {
    notes: notes || "",
  };
  const formContext = useForm({ defaultValues });
  const handleSubmit = () => {
    formContext.handleSubmit((data) => {
      onSubmit(data);
      formContext.reset();
    })();
  };
  return (
    <HookForm context={formContext} onSubmit={handleSubmit}>
      <div className="flex gap-3 items-center">
        <HookExpandingTextarea
          name="notes"
          placeholder="Add notes..."
          minRows={1}
          rules={{ required: true }}
        />
        {buttons}
      </div>
    </HookForm>
  );
}
