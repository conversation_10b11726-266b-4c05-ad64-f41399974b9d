import { Modal } from "react-daisyui";
import { create } from "zustand";
import { Button } from "./buttons";

interface ConfirmationOptions {
  headerText?: string;
  confirmationText?: string;
  confirmationColor?: string;
  secondaryCallback?: () => void;
  secondaryText?: string;
  secondaryConfirmationColor?: string;
  callback?: () => void;
  cancelText?: string;
  message?: string;
}

interface ConfirmationStoreState {
  isOpen: boolean;
  options: ConfirmationOptions;
  confirm: (options: ConfirmationOptions) => void;
  close: () => void;
}

export const useConfirmation = create<ConfirmationStoreState>((set) => ({
  isOpen: false,
  options: {},
  confirm: (options) => set(() => ({ isOpen: true, options })),
  close: () => {
    set((state) => ({
      isOpen: false,
      options: { ...state.options, callback: undefined },
    }));
  },
}));

export default function ConfirmationPopup() {
  const { isOpen, options, close } = useConfirmation();

  const {
    headerText = "Please confirm",
    confirmationText = "Confirm",
    confirmationColor = "success text-white",
    secondaryCallback = null,
    secondaryText = null,
    secondaryConfirmationColor = "secondary",
    callback = null,
    cancelText = "Cancel",
    message = "",
  } = options;

  const onConfirm = (isSecondary: boolean = false) => {
    if (isSecondary && secondaryCallback) secondaryCallback();
    else if (callback) callback();
    close();
  };

  return (
    <Modal.Legacy
      style={{ maxWidth: 600 }}
      open={isOpen}
      onClickBackdrop={close}
    >
      <Modal.Header>{headerText}</Modal.Header>
      <Modal.Body>{message}</Modal.Body>
      <Modal.Actions>
        <div className="flex justify-between w-full">
          <Button className="btn-ghost" onClick={close}>
            {cancelText}
          </Button>
          <div className="flex">
            {secondaryCallback && (
              <Button
                className={`btn-${secondaryConfirmationColor} mr-3`}
                onClick={() => onConfirm(true)}
              >
                {secondaryText}
              </Button>
            )}
            {callback && (
              <Button
                className={`btn-${confirmationColor}`}
                onClick={() => onConfirm()}
              >
                {confirmationText}
              </Button>
            )}
          </div>
        </div>
      </Modal.Actions>
    </Modal.Legacy>
  );
}
