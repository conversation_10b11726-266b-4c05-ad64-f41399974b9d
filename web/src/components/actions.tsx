import { REQUEST_STATUS_OPTIONS } from "@shared/constants";
import { useUpdateCases } from "../pages/cases/hooks";
import { useUpdateRequests } from "../pages/intake-requests/hooks";
import { But<PERSON> } from "./buttons";
import { SimpleSelect, StaffSelect } from "./forms/selects";
import { Popover } from "./utils";

export function BulkRequestActions({ selected }) {
  const { update: updateRequests } = useUpdateRequests();

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-slate-600">{selected.length} selected</span>
      <Popover trigger="click">
        <Popover.Trigger>
          <Button className="btn-primary btn-sm">Actions</Button>
        </Popover.Trigger>
        <Popover.Content>
          <div className="p-3 w-64">
            <StaffSelect
              placeholder="Opening Staff Member"
              onChange={(openingStaff) =>
                updateRequests({ ids: selected, data: { openingStaff } })
              }
              isClearable
            />
          </div>
          <div className="p-3 w-64">
            <StaffSelect
              placeholder="Intake Staff Member"
              onChange={(intakeStaff) =>
                updateRequests({ ids: selected, data: { intakeStaff } })
              }
              isClearable
            />
          </div>
          <div className="p-3 w-64">
            <SimpleSelect
              label="Status"
              options={REQUEST_STATUS_OPTIONS}
              name="status"
              onChange={(status) =>
                updateRequests({ ids: selected, data: { status } })
              }
              placeholder="Select Status"
              className="w-full"
              value={selected.status}
            />
          </div>
          <div className="p-3">
            <div className="flex items-center gap-2">
              <label className="cursor-pointer label gap-2">
                <input
                  type="checkbox"
                  className="checkbox checkbox-md"
                  onChange={(event) =>
                    updateRequests({
                      ids: selected,
                      data: { priority: event.target.checked },
                    })
                  }
                />
                <span>Make Priority</span>
              </label>
            </div>
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
}

export function BulkCaseActions({ selected }) {
  const { update: updateCases } = useUpdateCases();

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-slate-600">{selected.length} selected</span>
      <Popover trigger="click">
        <Popover.Trigger>
          <Button className="btn-primary btn-sm">Actions</Button>
        </Popover.Trigger>
        <Popover.Content>
          <div className="p-3 w-64">
            <StaffSelect
              placeholder="Select Staff Member"
              onChange={(staff) =>
                updateCases({ ids: selected, data: { staff } })
              }
              isClearable
            />
          </div>
          <div className="p-3">
            <div className="flex items-center gap-2">
              <label className="cursor-pointer label gap-2">
                <input
                  type="checkbox"
                  className="checkbox checkbox-md"
                  onChange={(event) =>
                    updateCases({
                      ids: selected,
                      data: { priority: event.target.checked },
                    })
                  }
                />
                <span>Make Priority</span>
              </label>
            </div>
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
}
