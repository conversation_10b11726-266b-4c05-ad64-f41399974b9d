import classNames from "classnames";
import { map } from "lodash";
import { createContext, useContext } from "react";
import { NavLink, Outlet, useParams } from "react-router-dom";
import { Tooltip } from "./utils";

export function MetadataTable({
  label,
  children,
}: {
  label?: any;
  children?: any;
}) {
  return (
    <div>
      {label && <h5 className="text-center">{label}</h5>}
      <table className="w-full">
        <tbody>{children}</tbody>
      </table>
    </div>
  );
}

export function MetadataSpacer() {
  return (
    <tr>
      <td colSpan={2}>
        <br />
      </td>
    </tr>
  );
}

export function MetadataRow({ children = null }) {
  return (
    <tr>
      <td colSpan={2}>{children}</td>
    </tr>
  );
}

export function MetadataHeader({ label }) {
  return (
    <tr>
      <td className="border-b">
        <h5 className="text-left font-weight-light pr-3 text-lg">{label}</h5>
      </td>
      <td className="border-b"></td>
    </tr>
  );
}

export function MetadataItem({
  label,
  title,
  compact = false,
  children = null,
}: {
  label: any;
  title?: any;
  compact?: boolean;
  children: any;
}) {
  return (
    <Tooltip
      title={title}
      positions={["left"]}
      align="center"
      className="group/metadata-item"
      Element="tr"
    >
      <td
        style={{ width: "1px" }}
        className={classNames(
          "align-top text-nowrap pl-1 pr-3 pt-1 border-r border-r-slate-300",
          { "pb-3 group-last:pb-2": !compact, "pb-1": compact }
        )}
      >
        <div className="flex items-center justify-end text-right text-slate-500 font-extralight">
          {label}
        </div>
      </td>
      <td
        className={classNames("text-left align-top pl-3 py-1", {
          "pb-3 group-last/metadata-item:pb-0": !compact,
          "pb-1 group-last/metadata-item:pb-0": compact,
        })}
      >
        {children}
      </td>
    </Tooltip>
  );
}

const ColumnContext = createContext({ columnCount: 3 });

export function Columns({ children, columnCount = 3, className = "" }) {
  return (
    <ColumnContext.Provider value={{ columnCount }}>
      <div
        className={classNames(
          className,
          "block lg:flex w-full h-full max-h-lvh"
        )}
      >
        {children}
      </div>
    </ColumnContext.Provider>
  );
}

export function Column({ children, className = "" }) {
  const { columnCount } = useContext(ColumnContext);

  return (
    <div
      className={classNames(
        className,
        `
        w-full lg:w-1/2 xl:w-1/${columnCount}
        lg:h-full
        px-4 pt-4
        lg:border-r
        border-t lg:border-t-0
        border-slate-300
        overflow-y-auto overscroll-y-contain`
      )}
    >
      {children}
    </div>
  );
}

const TabsContext = createContext({ height: "2.5rem" });

export function Tabs({
  children,
  hideStart = false,
  hideEnd = false,
  height = "2.5rem",
  ...props
}) {
  return (
    <TabsContext.Provider value={{ height }}>
      <>
        <div role="tablist" className="tabs tabs-lifted items-start">
          {!hideStart && (
            <a className="tab" style={{ height, gridColumnStart: 1 }} />
          )}
          {children}
          {!hideEnd && <a className="tab" style={{ height }} />}
        </div>
        <div {...props}>
          <Outlet />
        </div>
      </>
    </TabsContext.Provider>
  );
}

export function Tab({
  children = null,
  to = null,
  className = "",
  style = {},
  ...props
}: {
  children?: any;
  to?: string;
  className?: string;
  style?: any;
} & any) {
  const { height } = useContext(TabsContext);

  if (to === null)
    return (
      <span
        className={classNames(className, "tab cursor-default")}
        style={{ height, ...style }}
      >
        {children}
      </span>
    );

  return (
    <NavLink
      to={to}
      role="tab"
      end
      style={{ height, ...style }}
      className={({ isActive }: { isActive: boolean }) =>
        classNames(className, "tab", {
          ["tab-active"]: isActive,
        })
      }
      {...props}
    >
      {children}
    </NavLink>
  );
}

export function VerticalTabs({
  children,
  content,
}: {
  children: any;
  content?: any;
}) {
  const { tab } = useParams();

  return (
    <div className="flex w-full h-full items-stretch">
      <div className="h-full">{children}</div>
      <div className="p-6 bg-slate-100 grow rounded-md">
        {!content && <Outlet />}
        {content &&
          map(content, (Component, tabIndex) => {
            return (
              <div
                key={tabIndex}
                className={classNames("w-full h-full", {
                  hidden: tab !== tabIndex,
                })}
              >
                <Component />
              </div>
            );
          })}
      </div>
    </div>
  );
}

export function VerticalTab({ children, to, label, className = "", ...props }) {
  return (
    <NavLink
      to={to}
      style={{ width: 100, height: 100 }}
      className={({ isActive }) =>
        classNames(
          className,
          "rounded-l-md text-center flex items-center justify-center my-2",
          { "bg-slate-100": isActive, "hover:bg-slate-200": !isActive }
        )
      }
      {...props}
    >
      <div>
        <div className="mx-auto mb-1" style={{ width: 24, height: 24 }}>
          {children}
        </div>
        {label}
      </div>
    </NavLink>
  );
}
