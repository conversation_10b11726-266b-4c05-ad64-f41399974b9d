import classNames from "classnames";
import clsx from "clsx";
import React from "react";
import { useRunEfile } from "../lib/hooks";
import { useConfirmation } from "./confirmation";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

export function Button({ children, className, ...rest }: ButtonProps) {
  return (
    <button
      type="button"
      tabIndex={-1}
      {...rest}
      className={clsx(`btn flex items-center`, className)}
    >
      {children}
    </button>
  );
}

export function EfileButton({ caseId, className = "", children = null }) {
  const { confirm } = useConfirmation();
  const { runEfile } = useRunEfile(caseId);

  return (
    <Button
      onClick={() =>
        confirm({
          headerText: "Efile Case",
          message: "Are you sure you are ready to efile?",
          confirmationText: "Yes, run efile",
          callback: () => runEfile(),
        })
      }
      className={classNames(" btn-primary", className)}
    >
      {children || "Run Efile"}
    </Button>
  );
}
