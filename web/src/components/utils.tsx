import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ClipboardIcon,
} from "@heroicons/react/24/outline";
import axios from "axios";
import classNames from "classnames";
import { debounce } from "lodash";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Card } from "react-daisyui";
import { Link, useLocation } from "react-router-dom";
import { ArrowContainer, Popover as TinyPopover } from "react-tiny-popover";
import { useCountdown } from "../lib/hooks";
import { useCreateComment } from "../pages/intake/hooks";
import { Input } from "./forms/forms";

export const LinkWithQuery = ({ Component = Link, children, to, ...props }) => {
  const { search } = useLocation();

  return (
    <Component to={to + search} {...props}>
      {children}
    </Component>
  );
};

export function CenteredSpinner(props) {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <span className="loading loading-spinner loading-lg" {...props}></span>
    </div>
  );
}

export function ProgressBar({ height = 5 }) {
  return (
    <div
      className="progress rounded-0 position-absolute"
      style={{ height, top: 0, right: 0, left: 0 }}
    >
      <progress className="progress w-56"></progress>
    </div>
  );
}

const OverlayContext = createContext({
  setTriggerElement: (trigger: any) => {},
  setContent: (content: any) => {},
});

export function Overlay({
  children,
  arrowColor,
  open,
  style = {},
  trigger = "hover",
  Element = "div",
  className = "",
  ...props
}: {
  children: any;
  arrowColor?: string;
  open?: boolean;
  style?: object;
  trigger?: string;
  Element?: any;
  className?: string;
} & any) {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState(null);
  const [triggerElement, setTriggerElement] = useState(null);
  const context = useMemo(() => ({ setTriggerElement, setContent }), []);
  const isClick = trigger === "click";
  const isHover = trigger === "hover";

  if (!content)
    return (
      <OverlayContext.Provider value={context}>
        <Element className={className}>{children}</Element>
        {triggerElement}
      </OverlayContext.Provider>
    );

  return (
    <OverlayContext.Provider value={context}>
      {children}
      <TinyPopover
        isOpen={open !== undefined ? open : isOpen}
        positions={["top", "bottom"]} // preferred positions by priority
        reposition={true}
        onClickOutside={isClick ? () => setIsOpen(false) : () => {}}
        containerStyle={{ zIndex: "9999", ...style }}
        content={({ position, childRect, popoverRect }) => (
          <ArrowContainer
            position={position}
            childRect={childRect}
            popoverRect={popoverRect}
            arrowSize={8}
            arrowColor={arrowColor}
          >
            {content}
          </ArrowContainer>
        )}
        {...props}
      >
        <Element
          onMouseEnter={isHover ? () => setIsOpen(true) : () => {}}
          onMouseLeave={isHover ? () => setIsOpen(false) : () => {}}
          onClick={isClick ? () => setIsOpen(!isOpen) : () => {}}
          className={className}
        >
          {triggerElement}
        </Element>
      </TinyPopover>
    </OverlayContext.Provider>
  );
}

Overlay.Content = ({ children }: { children: any }) => {
  const { setContent } = useContext(OverlayContext);

  useEffect(() => {
    setContent(children);
  }, [children, setContent]);

  return null;
};

Overlay.Trigger = ({ children }: { children: any }) => {
  const { setTriggerElement } = useContext(OverlayContext);

  useEffect(() => {
    setTriggerElement(children);
  }, [children, setTriggerElement]);

  return null;
};

export const Popover = (props) => <Overlay arrowColor="#fff" {...props} />;
Popover.Trigger = Overlay.Trigger;
Popover.Content = ({
  children,
  style = {},
  className,
  ...props
}: {
  style?: object;
  className?: string;
  children: any;
}) => {
  return (
    <Overlay.Content {...props}>
      <div
        style={style}
        className={classNames(
          "bg-white rounded px-4 py-2 shadow-lg text-left transition-opacity",
          className
        )}
      >
        {children}
      </div>
    </Overlay.Content>
  );
};

const WizardContext = createContext({
  next: () => {},
  back: () => {},
  reset: () => {},
  currentStep: 0,
  enabled: false,
});

export const Wizard = ({ children }: { children: any }) => {
  const [currentStep, setCurrentStep] = useState(1);

  const context = useMemo(
    () => ({
      next: () => setCurrentStep(currentStep + 1),
      back: () => setCurrentStep(currentStep - 1),
      reset: () => setCurrentStep(1),
      enabled: currentStep > 0,
      currentStep,
    }),
    [currentStep]
  );

  return (
    <WizardContext.Provider value={context}>{children}</WizardContext.Provider>
  );
};

export const useWizard = () => useContext(WizardContext);

Wizard.Step = ({
  step,
  onOpen,
  ...props
}: {
  step: number;
  onOpen?: Function;
} & any) => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentStep } = useWizard();
  const shouldOpen = currentStep === step;

  useEffect(() => {
    if (shouldOpen && onOpen) onOpen();
    setIsOpen(shouldOpen);
  }, [shouldOpen]);

  return (
    <Overlay
      open={isOpen}
      positions={["right", "bottom", "top"]}
      arrowColor="#345b80"
      padding={5}
      {...props}
    />
  );
};

Wizard.Target = Overlay.Trigger;
Wizard.Content = ({
  children,
  style = {},
  className,
  final = false,
  ...props
}: {
  style?: object;
  className?: string;
  children: any;
  final?: boolean;
}) => {
  const { next, back, currentStep, enabled } = useWizard();

  if (!enabled) return null;

  return (
    <Overlay.Content {...props}>
      <div
        style={{ maxWidth: 420, ...style }}
        className={classNames(
          "bg-white rounded-lg px-4 py-3 shadow-lg text-left transition-opacity border-primary border-2",
          className
        )}
      >
        {children}
        <div className="flex justify-between mt-3">
          <div>
            {currentStep > 1 && (
              <button
                type="button"
                className="btn btn-sm btn-ghost"
                onClick={back}
              >
                <ChevronLeftIcon height={18} width={18} />
                Back
              </button>
            )}
          </div>
          <div>
            {!final && (
              <button
                type="button"
                className="btn btn-sm btn-ghost"
                onClick={next}
              >
                Next
                <ChevronRightIcon height={18} width={18} />
              </button>
            )}
          </div>
        </div>
      </div>
    </Overlay.Content>
  );
};

Wizard.Complete = ({ label = "All done!" }) => {
  const { reset } = useContext(WizardContext);

  return (
    <div className="flex justify-between gap-3">
      <div>{label}</div>
      <button type="button" className="btn btn-sm btn-primary" onClick={reset}>
        Restart
      </button>
    </div>
  );
};

export function Tooltip({ title, children, Element = "div", ...props }) {
  if (!title) return <Element className={props.className}>{children}</Element>;

  return (
    <Overlay arrowColor="rgb(51 65 85)" Element={Element} {...props}>
      <Overlay.Content>
        <div className="bg-slate-700 text-white rounded px-4 py-2 text-center shadow-lg max-w-72">
          {title}
        </div>
      </Overlay.Content>
      <Overlay.Trigger>{children}</Overlay.Trigger>
    </Overlay>
  );
}

export function TooltipCopy({
  title,
  children,
  hideContentInPopup = false,
  style = {},
  text = "",
  secureKey,
  formatter,
  ...props
}: {
  title: string;
  children: React.ReactNode;
  hideContentInPopup?: boolean;
  style?: React.CSSProperties;
  text?: string;
  secureKey?: string;
  formatter?: (value: any) => any;
}) {
  const [confirmCopy, setConfirmCopy] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleCopy = async () => {
    if (secureKey) {
      setLoading(true);
      const response = await axios.get(`/api/secure/${secureKey}`);
      setLoading(false);

      await navigator.clipboard.writeText(
        formatter ? formatter(response.data) : response.data
      );
    } else {
      await navigator.clipboard.writeText(text || title);
    }

    setConfirmCopy(true);
    setTimeout(() => setConfirmCopy(false), 1000);
  };

  return (
    <div
      {...props}
      style={{ cursor: "pointer", display: "inline-block", ...style }}
      onClick={handleCopy}
    >
      <Tooltip
        title={
          <>
            {!hideContentInPopup && (
              <>
                {title}
                <br />
              </>
            )}
            {loading ? (
              <span className="loading loading-infinity"></span>
            ) : (
              <span>{confirmCopy ? "COPIED!" : "(click to copy)"}</span>
            )}
          </>
        }
      >
        {children}
      </Tooltip>
    </div>
  );
}

export function ClickToReveal({
  Element = "div",
  title = "Click to reveal",
  secureKey,
  showTime = 5 * 1000,
  showCopy = true,
  formatter,
  copyFormatter,
  ...props
}: {
  element?: any;
  title?: string;
  secureKey: string;
  showTime?: number;
  showCopy?: boolean;
  formatter?: (value: any) => any;
  copyFormatter?: (value: any) => string;
}) {
  const { countdown, triggerCountdown } = useCountdown(() => setValue(""));
  const [value, setValue] = useState("");

  if (!secureKey) return null;

  const reveal = async () => {
    const response = await axios.get(`/api/secure/${secureKey}`);
    setValue(formatter ? formatter(response.data) : response.data);
    triggerCountdown(showTime);
  };

  return (
    <div className="inline-flex items-center gap-2">
      {showCopy && (
        <TooltipCopy
          hideContentInPopup
          title={value}
          secureKey={secureKey}
          formatter={copyFormatter}
        >
          <ClipboardIcon height={18} width={18} />
        </TooltipCopy>
      )}
      <Tooltip title={value ? null : title}>
        <Element
          className="cursor-pointer relative pb-3"
          onClick={reveal}
          {...props}
        >
          {countdown ? value : "**********"}
          <div className="absolute bottom-0 left-0 right-0">
            <progress
              className="progress progress-success"
              value={countdown}
              max={showTime}
            />
          </div>
        </Element>
      </Tooltip>
    </div>
  );
}

export const UnderConstruction = ({ label }: { label: string }) => {
  return (
    <Card className="bg-slate-100 mb-3">
      <Card.Body className="flex items-center px-4 py-2">
        <h1 className="text-xl m-8">{label} coming soon . . .</h1>
      </Card.Body>
    </Card>
  );
};

export const DebouncedTextInput = ({
  label,
  name,
  placeholder,
  value,
  fieldName,
  mutationFunction,
  comment,
  delay = 1000,
}: {
  label: string;
  name: string;
  placeholder: string;
  value: string;
  fieldName: string;
  mutationFunction: Function;
  comment?: any;
  delay?: number;
}) => {
  const { createComment } = useCreateComment();
  const [inputValue, setInputValue] = useState(value);
  const debouncedServerFunction = useCallback(
    debounce((serverInputValue) => {
      if (comment)
        createComment({
          ...comment,
          description: `${comment.description} ${serverInputValue}`,
        });
      mutationFunction({ [fieldName]: serverInputValue });
    }, delay),
    []
  );

  useEffect(() => {
    return () => {
      debouncedServerFunction.cancel();
    };
  }, [debouncedServerFunction]);

  return (
    <Input
      label={label}
      name={name}
      placeholder={placeholder}
      value={inputValue}
      size={15}
      onChange={(event) => {
        setInputValue(event.target.value);
        debouncedServerFunction(event.target.value);
      }}
    />
  );
};
