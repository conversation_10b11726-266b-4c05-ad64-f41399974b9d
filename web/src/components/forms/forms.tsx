import { FloatingPortal } from "@floating-ui/react";
import CheckCircleIcon from "@heroicons/react/24/outline/CheckCircleIcon";
import { ExclamationCircleIcon, EyeIcon } from "@heroicons/react/24/solid";
import CalendarIcon from "@heroicons/react/24/solid/CalendarIcon";
import { User } from "@shared/db/schema/users";
import { stopPropagation } from "@shared/utils";
import { useCountdown } from "@web/src/lib/hooks";
import classNames from "classnames";
import dayjs from "dayjs";
import { difference, get, includes, isEmpty, map, union } from "lodash";
import type { ComponentPropsWithoutRef, FC, RefCallback } from "react";
import { forwardRef, useEffect, useRef, useState } from "react";
import { HexColorPicker } from "react-colorful";
import { Card, Toggle } from "react-daisyui";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  Controller,
  FormProvider,
  useForm,
  useForm<PERSON>ontext,
  UseFormReturn,
} from "react-hook-form";
import TextareaAutosize from "react-textarea-autosize";
import tinycolor from "tinycolor2";
import { Button } from "../buttons";
import { Overlay, Popover } from "../utils";
import {
  AttorneySelect,
  CitySelect,
  ClientSelect,
  ClioContactSelect,
  ClioMatterSelect,
  ContactSelect,
  CountySelect,
  DefendantSelect,
  LimitedStateSelect,
  ModelSelect,
  OpposingCounselSelect,
  PropertySelect,
  SettingSelect,
  SimpleSelect,
  StaffSelect,
  StateSelect,
} from "./selects";

export function HookForm({
  onSubmit,
  context,
  defaultValues,
  children,
  ...props
}: {
  onSubmit: Function;
  context?: any;
  defaultValues?: any;
  children: any;
} & any) {
  let formContext = context;
  if (!formContext) formContext = useForm({ defaultValues });

  return (
    <FormProvider {...formContext}>
      <form
        autoComplete="off"
        onSubmit={formContext.handleSubmit(
          async (data) => await onSubmit(data)
        )}
        {...props}
      >
        {children}
      </form>
    </FormProvider>
  );
}

export function FormValidationButton({
  formContext,
  disabled = false,
  children = "Submit",
  ...props
}: { formContext: UseFormReturn; disabled?: Boolean; children?: any } & any) {
  const errors = formContext?.formState?.errors;

  if (isEmpty(errors))
    return (
      <Button
        className={classNames(classNames, "btn-success text-white")}
        disabled={disabled}
        type="submit"
        {...props}
      >
        {children}
        <CheckCircleIcon height={18} width={18} />
      </Button>
    );

  return (
    <Popover arrowColor="#fff">
      <Popover.Trigger>
        <div className="flex items-center gap-2">
          <ExclamationCircleIcon
            className="text-error"
            height={32}
            width={32}
          />
          <Button
            className={classNames(classNames, "btn-error")}
            disabled
            type="submit"
            {...props}
          >
            {children}
            <CheckCircleIcon height={18} width={18} />
          </Button>
        </div>
      </Popover.Trigger>
      <Popover.Content>
        <h3 className="mb-2 pb-2 border-b border-b-gray-200">Form Errors</h3>
        <ol className="list-decimal list-inside">
          {errors &&
            map(errors, (error, key) => <li key={key}>{error.message}</li>)}
        </ol>
      </Popover.Content>
    </Popover>
  );
}

export function FormWrapper({
  style = {},
  children,
  className = "",
  maxWidth = "550px",
  ...props
}) {
  return (
    <Card
      compact
      style={{ maxWidth, width: "100%", ...style }}
      className={classNames("bg-slate-100", className)}
      {...props}
    >
      <Card.Body className="px-4 py-2">{children}</Card.Body>
    </Card>
  );
}

export function FormHeader({
  children = null,
  title,
  ...props
}: {
  title?: string;
  children?: any;
}) {
  return (
    <div className=" border-gray-300 border-b mb-1 pb-3" {...props}>
      {!children && <h1 className="text-3xl font-light pr-8">{title}</h1>}
      {children}
    </div>
  );
}

export function ButtonGroup({
  options = [],
  value,
  onChange,
  buttonClassName = "btn-primary",
  className = "",
  ...props
}: ButtonGroupProps & any) {
  return (
    <div
      className={classNames("flex input-button-group", className)}
      role="group"
      {...props}
    >
      {map(options, (option: any, index) => {
        let buttonClasses = buttonClassName;

        if (value !== option.value) buttonClasses += " btn-outline bg-white";

        if (index === 0) buttonClasses += " rounded-r-none";
        else if (index === options.length - 1)
          buttonClasses += " rounded-l-none border-l-0";
        else buttonClasses += " rounded-none border-l-0";

        return (
          <Button
            onClick={() => onChange(option.value)}
            key={option.value}
            className={classNames(buttonClasses, "flex-grow basis-0")}
          >
            {option.label}
          </Button>
        );
      })}
    </div>
  );
}

export function FormButtons({ children, ...props }) {
  return (
    <div
      className="mt-4 pt-4 border-t border-t-gray-300 flex justify-between"
      {...props}
    >
      {children}
    </div>
  );
}

export function InputElement({
  Element = "input",
  type = "text",
  value,
  className,
  style,
  error,
  required,
  name,
  innerRef,
  ...props
}: {
  Element: string;
  type?: string;
  name: string;
  className?: string;
  style?: any;
  value?: any;
  error?: string;
  required?: boolean;
  innerRef?: RefCallback<T>;
}) {
  const valueProps = {};

  if (type === "checkbox") {
    valueProps["checked"] = value;
  } else {
    valueProps["value"] = value;
  }

  return (
    <div className="flex items-center gap-2">
      <Element
        ref={innerRef}
        type={type}
        name={name}
        id={name}
        required={required}
        style={{ borderRadius: 4, ...style }}
        className={classNames(className, {
          "is-invalid": !!error,
          "form-control input-bordered input px-3 w-full":
            Element === "input" && type !== "checkbox",
          checkbox: Element === "input" && type === "checkbox",
          "textarea textarea-bordered": Element === "textarea",
        })}
        {...valueProps}
        {...props}
      />
    </div>
  );
}

interface InputProps extends ComponentPropsWithoutRef<"input"> {
  name?: string;
  label?: string;
  groupClassName?: string;
  labelClassName?: string;
  className?: string;
  labelStyle?: any;
  type?: string;
  raw?: boolean;
  Element?: string | FC;
  LabelElement?: string | FC;
  error?: string;
  description?: string;
  required?: boolean;
  hidden?: boolean;
  readonly?: boolean;
  children?: any;
  labelAfter?: boolean;
  inline?: boolean;
}

export function Input({
  label,
  groupClassName,
  labelClassName,
  className,
  type = "text",
  Element = "input",
  LabelElement = "label",
  raw = false,
  labelStyle,
  error,
  description,
  required = false,
  labelAfter = false,
  inline = false,
  name,
  hidden,
  readOnly,
  children,
  ...props
}: InputProps) {
  const simpleInput = typeof Element === "string" && !children;
  const child = !simpleInput ? (
    children
  ) : (
    <InputElement
      error={error}
      type={type}
      name={name!}
      Element={Element}
      {...props}
    />
  );

  if (raw) return child;

  const LabelText = (
    <span className="label-text">
      {label}
      {required && <span className="ml-1 text-red-800">*</span>}
    </span>
  );

  return (
    <div className={classNames("w-full mb-2", groupClassName)}>
      <LabelElement className="form-control w-full">
        <>
          {label && (
            <div
              className={classNames(
                "label py-1",
                {
                  "items-center cursor-pointer gap-2": inline,
                  "justify-start": labelAfter && inline,
                },
                labelClassName
              )}
              style={labelStyle}
            >
              {!labelAfter && LabelText}
              {inline && child}
              {labelAfter && LabelText}
            </div>
          )}
          {(!inline || !label) && child}
        </>

        {error && (
          <div className="label pt-1">
            <span className="label-text-alt text-error">{error}</span>
          </div>
        )}

        {description && !error && (
          <span className="label-text-alt pl-1">{description}</span>
        )}
      </LabelElement>
    </div>
  );
}

export function HookInput({
  name,
  rules = {},
  Element = "input",
  inline,
  labelAfter,
  labelStyle,
  labelClassName,
  groupClassName,
  ...props
}: {
  name: string;
  rules?: any;
  Element?: any;
  inline?: boolean;
  labelAfter?: boolean;
  labelStyle?: any;
  labelClassName?: string;
  groupClassName?: string;
} & InputProps) {
  const { control, formState } = useFormContext();
  const error = get(formState, `errors.${name}.message`);

  const simpleInput = typeof Element === "string";

  return (
    <Input
      error={error as string}
      labelAfter={labelAfter}
      labelStyle={labelStyle}
      labelClassName={labelClassName}
      groupClassName={groupClassName}
      inline={inline}
      {...props}
    >
      <Controller
        control={control}
        name={name}
        rules={rules}
        render={({ field: { ref, ...fieldProps } }) => (
          <>
            {!simpleInput && <Element {...fieldProps} {...props} />}
            {simpleInput && (
              <InputElement
                error={error as string}
                Element={Element}
                innerRef={ref}
                {...fieldProps}
                {...props}
              />
            )}
          </>
        )}
      />
    </Input>
  );
}

export const HookToggle = ({ color, ...props }: { color: string } & any) => (
  <HookInput
    Element={({ value, ...passed }: { value: Boolean } & any) => (
      <Toggle {...passed} checked={value} color={color} />
    )}
    labelClassName="bg-white border border-gray-300 rounded px-2"
    labelStyle={{ paddingTop: 6, paddingBottom: 6 }}
    inline
    labelAfter
    {...props}
  />
);

export const HookCheckbox = (props) => (
  <HookInput
    Element="input"
    type="checkbox"
    labelClassName="bg-white border border-gray-300 rounded px-2"
    labelStyle={{ paddingTop: 6, paddingBottom: 6 }}
    inline
    labelAfter
    {...props}
  />
);

export const HookExpandingTextarea = (props) => (
  <HookInput
    Element={TextareaAutosize}
    minRows={3}
    className="textarea textarea-bordered text-black"
    {...props}
  />
);

export const HookSelect = (props) => (
  <HookInput Element={SimpleSelect} {...props} />
);

export const HookStateSelect = (props) => (
  <HookInput Element={StateSelect} {...props} />
);

export const HookLimitedStateSelect = (props) => (
  <HookInput Element={LimitedStateSelect} {...props} />
);

export const HookCountySelect = (props) => (
  <HookInput Element={CountySelect} {...props} />
);

export const HookCitySelect = (props) => (
  <HookInput Element={CitySelect} {...props} />
);

export const HookModelSelect = (props) => (
  <HookInput Element={ModelSelect} {...props} />
);

export const HookClientSelect = (props) => (
  <HookInput Element={ClientSelect} {...props} />
);

export const HookPropertySelect = (props) => (
  <HookInput Element={PropertySelect} {...props} />
);

export const HookDefendantSelect = (props) => (
  <HookInput Element={DefendantSelect} {...props} />
);

export const HookSettingSelect = (props) => (
  <HookInput Element={SettingSelect} {...props} />
);

export const HookAttorneySelect = (props) => (
  <HookInput Element={AttorneySelect} {...props} />
);

export const HookStaffSelect = (props) => (
  <HookInput Element={StaffSelect} {...props} />
);

export const HookContactSelect = (props) => (
  <HookInput Element={ContactSelect} {...props} />
);

export const HookOpposingCounselSelect = (props) => (
  <HookInput Element={OpposingCounselSelect} {...props} />
);

export const HookClioContactSelect = (props) => (
  <HookInput Element={ClioContactSelect} {...props} />
);

export const HookClioMatterSelect = (props) => (
  <HookInput Element={ClioMatterSelect} {...props} />
);

export const HookDatePicker = ({
  showTimeSelect = false,
  placeholder,
  name,
  rules = {},
  type = "text",
  returnFormattedDate = false,
  ...props
}: {
  showTimeSelect?: Boolean;
  placeholder?: string;
  name: string;
  type: string;
  rules?: any;
  returnFormattedDate?: boolean;
} & any) => {
  const { control, formState } = useFormContext();
  const error = get(formState, `errors.${name}.message`);
  const defaultPlaceholder = showTimeSelect ? "Date & Time..." : "Date...";

  const CustomInput = forwardRef(({ value, onClick }: any, ref) => {
    return (
      <InputElement
        value={value}
        onClick={onClick}
        type={type}
        onChange={() => {}}
        placeholder={placeholder || defaultPlaceholder}
        style={{ paddingLeft: 36 }}
        innerRef={ref}
      />
    );
  });

  return (
    <Input error={error as string} {...props}>
      <Controller
        control={control}
        name={name}
        rules={rules}
        render={({ field: { ref, value, onChange, ...fieldProps } }) => {
          // Convert string dates to proper Date objects to avoid timezone issues
          let dateValue = value;
          if (typeof value === "string" && value) {
            // For date-only strings, append time to ensure local timezone parsing
            const dateString = value.includes("T")
              ? value
              : `${value}T00:00:00`;
            dateValue = dayjs(dateString).toDate();
          }

          const hasNoTime =
            dayjs(dateValue).get("hour") === 0 &&
            dayjs(dateValue).get("minute") === 0;
          const format = hasNoTime ? "MMMM d, yyyy" : "MMMM d, yyyy h:mm aa";

          const handleChange = (date: Date | null) => {
            if (returnFormattedDate && date) {
              // If returnFormattedDate is true, format as YYYY-MM-DD string
              onChange(dayjs(date).format("YYYY-MM-DD"));
            } else {
              onChange(date);
            }
          };

          return (
            <DatePicker
              selected={dateValue}
              onChange={handleChange}
              customInput={<CustomInput {...props} />}
              dateFormat={format}
              showIcon
              isClearable
              icon={<CalendarIcon style={{ width: 21, height: 21 }} />}
              todayButton="Today"
              timeIntervals={15}
              showTimeSelect={showTimeSelect}
              shouldCloseOnSelect={!showTimeSelect}
              popperContainer={FloatingPortal}
              {...fieldProps}
              {...props}
            />
          );
        }}
      />
    </Input>
  );
};

export interface ButtonGroupProps {
  options: Array<{ value: any; label: string }>;
  value?: any;
  onChange?: (value: any) => void;
  buttonClassName?: string;
  className?: string;
}

export const HookButtonGroup = (props: any) => (
  <HookInput LabelElement="span" Element={ButtonGroup} {...props} />
);

export function HookSecureInput({
  revealTime = 5 * 1000,
  InputComponent = HookInput,
  className = "mb-2",
  ...props
}: { revealTime: number } & any) {
  const { countdown, triggerCountdown } = useCountdown();
  const formContext = useFormContext();
  const value = formContext.watch(props.name);

  return (
    <div className="w-full flex items-end gap-2">
      <InputComponent type={countdown ? "text" : "password"} {...props} />
      <div className={classNames("relative", className)} style={{ bottom: 6 }}>
        <EyeIcon
          className={classNames({
            "text-gray-400 cursor-not-allowed": !value,
            "cursor-pointer": value,
          })}
          onClick={() => (value ? triggerCountdown(revealTime) : null)}
          height={24}
          width={24}
        />
        <div
          className={classNames("absolute", {
            hidden: !countdown,
          })}
          style={{
            right: -4,
            left: -4,
            lineHeight: 0,
          }}
        >
          <progress
            className="progress progress-success"
            style={{ height: 4 }}
            value={countdown}
            max={revealTime}
          />
        </div>
      </div>
    </div>
  );
}

// UNTESTED
export function MultiPicker({
  onSave,
  options,
  current,
  label,
  children = null,
}) {
  const [show, setShow] = useState(false);
  const [selected, setSelected] = useState(current);
  const target = useRef(null);

  useEffect(() => {
    setSelected(current);
  }, [current]);

  const addTag = (id) => setSelected(union(selected, [id]));
  const removeTag = (id) => setSelected(difference(selected, [id]));

  function handleSave() {
    setShow(false);
    onSave(selected);
  }

  return (
    <div>
      <div
        ref={target}
        className="cursor-pointer"
        onClick={stopPropagation(() => setShow(!show))}
      >
        {children}
      </div>
      <Overlay
        target={target.current}
        show={show}
        placement="bottom"
        rootClose
        onHide={() => setShow(false)}
      >
        <Popover>
          <Popover.Header as="h3" className="text-center px-4">
            {label}
            <button
              type="button"
              className="close position-absolute"
              style={{ right: 5, top: 5 }}
              aria-label="Close"
              onClick={stopPropagation(() => setShow(false))}
            >
              <Icon size={18} icon="close" />
            </button>
          </Popover.Header>
          <Popover.Body
            className="text-center p-3"
            style={{ maxHeight: 250, overflowY: "auto" }}
          >
            {options.map((option) => {
              const isChecked = includes(selected, option.value);
              const onClick = isChecked ? removeTag : addTag;

              return (
                <div key={option.value}>
                  <Tag
                    className="w-full d-flex justify-content-between"
                    tag={option}
                    onClick={stopPropagation(() => onClick(option.value))}
                  >
                    <span
                      style={{ textOverflow: "ellipsis", overflow: "hidden" }}
                    >
                      {option.label}
                    </span>
                    <input
                      type="checkbox"
                      checked={isChecked}
                      className="ml-3"
                      readOnly
                    />
                  </Tag>
                </div>
              );
            })}
            <button
              onClick={stopPropagation(() => handleSave())}
              className="btn btn-kairos-blue mt-2"
            >
              Save
            </button>
          </Popover.Body>
        </Popover>
      </Overlay>
    </div>
  );
}

export function Picker({ onSelect, options, label, children = null }) {
  const [show, setShow] = useState(false);
  const target = useRef(null);

  function handleSelect(selected) {
    setShow(false);
    onSelect(selected);
  }

  return (
    <div>
      <div
        ref={target}
        className="cursor-pointer"
        onClick={stopPropagation(() => setShow(!show))}
      >
        {children}
      </div>
      <Overlay
        target={target.current}
        show={show}
        placement="bottom"
        rootClose
        onHide={() => setShow(false)}
      >
        <Popover>
          <Popover.Header as="h3" className="text-center px-4">
            {label}
            <button
              type="button"
              className="close position-absolute"
              style={{ right: 5, top: 5 }}
              aria-label="Close"
              onClick={stopPropagation(() => setShow(false))}
            >
              <Icon size={18} icon="close" />
            </button>
          </Popover.Header>
          <Popover.Body className="text-center">
            {options.map((option) => (
              <div key={option.value}>
                <div
                  style={{ fontSize: "1rem" }}
                  onClick={stopPropagation(() => handleSelect(option.value))}
                  className={`cursor-pointer font-weight-light badge-pill badge my-1 badge-${option.color}`}
                >
                  {option.value}
                </div>
              </div>
            ))}
          </Popover.Body>
        </Popover>
      </Overlay>
    </div>
  );
}

export const HookColorPicker = ({
  name,
  label,
  updateTextColor = false,
  ...props
}: {
  name: string;
  label?: string;
  updateTextColor?: boolean;
} & any) => {
  const { control, setValue, watch } = useFormContext();
  const textColorName = "badgeTextColor";

  return (
    <Input label={label} {...props}>
      <Controller
        name={name}
        control={control}
        render={({ field: { value, onChange } }) => {
          const handleColorChange = (newColor: string) => {
            onChange(newColor);
            const color = tinycolor(newColor);
            const textColor = color.getBrightness() > 150 ? "black" : "white";
            setValue(textColorName, textColor);
          };

          // Use the current selected color for preview, fallback to user's color or default
          const displayColor =
            value || (props.user && props.user.badgeColor) || "#345b80";

          // Get the text color from form or user defaults
          const textColor =
            watch(textColorName) ||
            (props.user && props.user.badgeTextColor) ||
            (tinycolor(displayColor).getBrightness() > 150 ? "black" : "white");

          return (
            <div className="flex flex-col">
              <HexColorPicker
                color={displayColor}
                onChange={handleColorChange}
                style={{ width: "100%", maxWidth: "800px" }}
              />
              <div className="flex items-center gap-2 mt-2">
                <span>Badge Preview:</span>
                <div
                  className="badge"
                  style={{
                    backgroundColor: displayColor,
                    color: textColor,
                  }}
                >
                  {props.user.name || "Username"}
                </div>
              </div>
            </div>
          );
        }}
      />
    </Input>
  );
};

export function StaffBadge({
  user,
  style = {},
  className = "",
  size = 30,
  ...props
}: {
  user: User;
  style?: React.CSSProperties;
  className?: string;
  size?: number;
} & React.HTMLAttributes<HTMLSpanElement>) {
  if (!user) return null;

  // Use user's badge colors or defaults
  const backgroundColor = user.badgeColor || "#345b80";
  const textColor = user.badgeTextColor || "white";

  const badgeStyle = {
    backgroundColor,
    color: textColor,
    width: `${size}px`,
    height: `${size}px`,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minWidth: `${size}px`,
    minHeight: `${size}px`,
    maxWidth: `${size}px`,
    maxHeight: `${size}px`,
    ...style,
  };

  return (
    <span
      className={`rounded-full text-xs aspect-square ${className}`}
      style={badgeStyle}
      {...props}
    >
      {user.initials}
    </span>
  );
}
