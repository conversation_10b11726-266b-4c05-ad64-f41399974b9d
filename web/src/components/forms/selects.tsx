import {
  REQUEST_STATUS_OPTIONS,
  STATE_OPTIONS,
  TEMPLATE_STATE_OPTIONS,
} from "@shared/constants";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useBaseData } from "@web/src/lib/hooks";
import { useCreateClioContact } from "@web/src/pages/clio/components";
import axios from "axios";
import classNames from "classnames";
import { concat, find, isEqual, map, size, union } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import Select, { components as ReactSelectComponents } from "react-select";
import CreatableSelect from "react-select/creatable";

export function SimpleSelect({
  onChange,
  value,
  className,
  create = false,
  valueKey = "value",
  returnObject = false,
  ...props
}) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);

  const handleChange = (selected) => {
    if (returnObject) return onChange(selected);
    if (props.isMulti) return onChange(map(selected, valueKey));
    if (selected === null) return onChange(null);

    return onChange(selected[valueKey]);
  };

  const formattedValue = find(props.options, { value });
  const selectedValue =
    formattedValue ||
    (create && value ? { label: value, value, __isNew__: true } : "");
  const Component = create ? CreatableSelect : Select;

  return (
    <Component
      onChange={handleChange}
      value={selectedValue}
      className={classNames(className, "w-full")}
      isClearable
      onMenuOpen={() => setMenuIsOpen(true)}
      onMenuClose={() => setMenuIsOpen(false)}
      onKeyDown={(event) => {
        // Only prevent Enter key from submitting the form when menu is closed
        // This allows normal Enter selection when dropdown is open
        if (event.key === "Enter" && !menuIsOpen) {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
      {...props}
    />
  );
}

export function MultiSelectValueContainer({ children, ...props }) {
  let [values, input] = children;
  const isMulti = Array.isArray(values);

  const styles = {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    maxWidth: "calc(100% - 10px)",
  };

  if (isMulti && values.length === 1) {
    values = (
      <span style={styles}>
        {values[0].props.data.label || values[0].props.data.name}
      </span>
    );
  } else if (isMulti) {
    values = <span style={styles}>{values.length} selected</span>;
  }

  return (
    <ReactSelectComponents.ValueContainer {...props}>
      {values}
      {input}
    </ReactSelectComponents.ValueContainer>
  );
}

export function ModelSelect({
  model,
  value,
  onChange,
  selectCallback,
  searchFields,
  labelField,
  fields,
  create = false,
  className = "",
  filters = {},
  components = {},
  valueField = "id",
  isMulti = false,
  searchPath = "/api/search",
  hideSelectedOptions = false,
  returnFullObject = false,
  styles = {},
  searchParams = { limit: 50 },
  ...props
}: {
  model: string;
  value: Array<any> | any | null;
  onChange: Function;
  selectCallback?: Function;
  searchFields: Array<string>;
  labelField: string;
  fields?: Array<string>;
  create?: boolean;
  className?: string;
  filters?: object;
  components?: any;
  valueField?: string;
  isMulti?: boolean;
  searchPath?: string;
  hideSelectedOptions?: boolean;
  returnFullObject?: boolean;
  styles?: object;
  searchParams?: object;
}) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [search, setSearch] = useState("");
  const fetchFields = fields || [labelField];
  const params: any = {
    ...searchParams,
    model,
    searchFields,
    fields: union(fetchFields, [valueField]),
  };
  if (search) params.query = search;
  if (filters && size(filters)) params.filters = filters;

  const { data: results, isLoading } = useQuery({
    queryKey: [model, params],
    queryFn: async () => {
      const response = await axios.get(searchPath, { params });

      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const emptyValue = isMulti ? [] : null;
  const [selectedValues, setSelectedValues] = useState<any>(emptyValue);

  const getFormattedValues = useCallback(
    (values) => {
      if (!values) return emptyValue;
      if (returnFullObject || values.__isNew__) return values;
      if (isMulti) return map(values, valueField);

      return values[valueField];
    },
    [returnFullObject, isMulti, valueField]
  );

  const formattedValues = useMemo(
    () => getFormattedValues(selectedValues),
    [selectedValues]
  );

  const onLocalChange = (selected: any) => {
    if (!selected) {
      setSelectedValues(emptyValue);
      onChange(emptyValue);
      if (selectCallback) selectCallback(emptyValue);
    } else {
      setSelectedValues(selected);
      onChange(getFormattedValues(selected));
      if (selectCallback) selectCallback(selected);
    }
  };

  useEffect(() => {
    const getInitialItems = async () => {
      if (!value) return onLocalChange(emptyValue);

      const response = await axios.get(searchPath, {
        params: { ids: concat(value), model },
      });

      const initialValues = response ? response.data : [];

      onLocalChange(isMulti ? initialValues : initialValues[0]);
    };

    const isNew = value?.__isNew__;

    if (!isEqual(formattedValues, value)) {
      if (isNew) {
        // Handle pre-filled __isNew__ values directly
        onLocalChange(value);
      } else {
        getInitialItems().catch(console.error);
      }
    }
  }, [value, formattedValues, model]);

  const Component = create ? CreatableSelect : Select;
  const isSelected = (option, selectValue) =>
    Boolean(find(concat(selectValue), { [valueField]: option[valueField] }));

  const defaultComponents: any = {};

  if (isMulti) defaultComponents.ValueContainer = MultiSelectValueContainer;

  return (
    <Component
      isClearable={true}
      isLoading={isLoading}
      inputValue={search}
      hideSelectedOptions={hideSelectedOptions}
      onInputChange={setSearch}
      options={results}
      className={classNames(className, "w-full")}
      value={selectedValues}
      getOptionValue={(option) => {
        if (option.__isNew__) return option.value;

        return option[valueField];
      }}
      getOptionLabel={(option) => {
        if (option.__isNew__) return option.label;

        return option[labelField];
      }}
      filterOption={() => true}
      isOptionSelected={isSelected}
      onChange={onLocalChange}
      onMenuOpen={() => setMenuIsOpen(true)}
      onMenuClose={() => setMenuIsOpen(false)}
      onKeyDown={(event) => {
        // Only prevent Enter key from submitting the form when menu is closed
        // This allows normal Enter selection when dropdown is open
        if (event.key === "Enter" && !menuIsOpen) {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
      styles={{
        option: (defaultStyles) => ({
          ...defaultStyles,
          fontSize: 12,
          ...styles,
        }),
      }}
      components={{ ...defaultComponents, ...components }}
      isMulti={isMulti}
      {...props}
    />
  );
}

export function ClientSelect(props) {
  return (
    <ModelSelect
      searchFields={["name", "address1", "address2", "city", "state", "zip"]}
      fields={[
        "name",
        "address1",
        "address2",
        "city",
        "state",
        "zip",
        "doNotUse",
      ]}
      labelField="name"
      placeholder="Client"
      model="clients"
      components={{
        Option: (props) => {
          if (props.data.__isNew__) {
            return (
              <ReactSelectComponents.Option {...props}>
                <div className="font-bold">New Client: "{props.value}"</div>
              </ReactSelectComponents.Option>
            );
          }

          const client = props.data;

          return (
            <ReactSelectComponents.Option {...props}>
              <div
                className={classNames("font-bold", {
                  "text-error": client.doNotUse,
                })}
              >
                {client.name}
              </div>
              <div
                className={classNames("text-xs", {
                  "text-gray-500": !props.isSelected,
                  "text-gray-300": props.isSelected,
                })}
              >
                {client.address1} {client.address2}
                <br />
                {client.city}, {client.state} {client.zip}
              </div>
            </ReactSelectComponents.Option>
          );
        },
      }}
      {...props}
    />
  );
}

export function DefendantSelect(props) {
  const getName = useCallback((defendant) => {
    if (defendant.isBusiness) return defendant.businessName;

    let name = defendant.firstName;

    if (defendant.middleName) name += ` ${defendant.middleName}`;
    if (defendant.lastName) name += ` ${defendant.lastName}`;
    if (defendant.suffix) name += ` ${defendant.suffix}`;

    return name;
  }, []);

  return (
    <ModelSelect
      searchFields={["firstName", "lastName", "businessName"]}
      fields={[
        "firstName",
        "lastName",
        "businessName",
        "isBusiness",
        "address1",
        "address2",
        "city",
        "state",
        "zip",
      ]}
      placeholder="Defendant"
      labelField="firstName"
      model="defendants"
      searchPath="/api/defendants/search"
      getOptionLabel={(option) => {
        if (option.__isNew__) return option.label;

        return getName(option);
      }}
      components={{
        Option: (props) => {
          if (props.data.__isNew__) {
            return (
              <ReactSelectComponents.Option {...props}>
                <div className="font-bold">New Defendant: "{props.value}"</div>
              </ReactSelectComponents.Option>
            );
          }

          const defendant = props.data;

          return (
            <ReactSelectComponents.Option {...props}>
              <div className="font-bold">{getName(defendant)}</div>
              <div
                className={classNames("text-xs", {
                  "text-gray-500": !props.isSelected,
                  "text-gray-300": props.isSelected,
                })}
              >
                {defendant.address1} {defendant.address2}
                <br />
                {defendant.city}, {defendant.state} {defendant.zip}
              </div>
            </ReactSelectComponents.Option>
          );
        },
      }}
      {...props}
    />
  );
}

export function ContactSelect(props) {
  return (
    <ModelSelect
      {...props}
      searchFields={["name"]}
      fields={["name"]}
      model="contacts"
      labelField="name"
      {...props}
    />
  );
}

export function OpposingCounselSelect(props) {
  return (
    <ModelSelect
      {...props}
      searchFields={["name", "barNumber"]}
      fields={["name", "barNumber"]}
      model="contacts"
      labelField="name"
      getOptionLabel={(option) => {
        if (option.__isNew__) return option.label;

        return `${option.name} - ${option.barNumber}`;
      }}
      {...props}
    />
  );
}

export function PropertySelect(props) {
  return (
    <ModelSelect
      searchFields={[
        "name",
        "address1",
        "address2",
        "city",
        "state",
        "zip",
        "propertyNotes",
      ]}
      fields={[
        "name",
        "address1",
        "address2",
        "city",
        "state",
        "zip",
        "county",
        "doNotUse",
      ]}
      placeholder="Property"
      labelField="name"
      model="properties"
      components={{
        Option: (props) => {
          if (props.data.__isNew__) {
            return (
              <ReactSelectComponents.Option {...props}>
                <div className="font-bold">New Property: "{props.value}"</div>
              </ReactSelectComponents.Option>
            );
          }

          const property = props.data;

          return (
            <ReactSelectComponents.Option {...props}>
              <div
                className={classNames("font-bold", {
                  "text-error": property.doNotUse,
                })}
              >
                {property.name}
              </div>
              <div
                className={classNames("text-xs", {
                  "text-gray-500": !props.isSelected,
                  "text-gray-300": props.isSelected,
                })}
              >
                {property.address1} {property.address2}
                <br />
                {property.city}, {property.state} {property.zip}
              </div>
            </ReactSelectComponents.Option>
          );
        },
      }}
      {...props}
    />
  );
}

const stateFilter = (option, search) => {
  if (!search) return true;

  const nameMatches = option.value
    .toLowerCase()
    .startsWith(search.toLowerCase());
  const abbreviationMatches = option.label
    .toLowerCase()
    .startsWith(search.toLowerCase());

  return nameMatches || abbreviationMatches;
};

export function LimitedStateSelect(props) {
  const { data } = useBaseData();
  const options = useMemo(
    () =>
      data?.states?.map((state) => ({
        label: state.fullName,
        value: state.name,
      })),
    [data?.states]
  );

  return (
    <SimpleSelect filterOption={stateFilter} options={options} {...props} />
  );
}

export function ShortStateSelect(props) {
  return (
    <SimpleSelect
      filterOption={stateFilter}
      options={TEMPLATE_STATE_OPTIONS}
      {...props}
    />
  );
}

export function StateSelect(props) {
  return (
    <SimpleSelect
      filterOption={stateFilter}
      options={STATE_OPTIONS}
      {...props}
    />
  );
}

export function TemplateStateSelect(props) {
  return <SimpleSelect options={TEMPLATE_STATE_OPTIONS} {...props} />;
}

export function StaffSelect(props) {
  const { data } = useBaseData();
  const options = useMemo(
    () =>
      data?.users
        ?.filter((user) => !user.isAttorney && !user.isNonStaff)
        .map((user) => ({ label: user.name, value: user.name })),
    [data?.users]
  );

  return <SimpleSelect options={options} {...props} />;
}

export function AttorneySelect(props) {
  const { data } = useBaseData();
  const options = useMemo(
    () =>
      data?.users
        ?.filter((user) => user.isAttorney)
        .map((user) => ({ label: user.name, value: user.name })),
    [data?.users]
  );

  return <SimpleSelect options={options} {...props} />;
}

export function CountySelect({ state, ...props }: { state: string } & any) {
  const { data } = useBaseData();
  const options = useMemo(
    () =>
      data?.counties
        ?.filter((county) => {
          if (!state) return true;

          return county.state === state;
        })
        .map((county) => ({
          label: `${county.name}, ${county.state}`,
          value: county.name,
        })),
    [data?.counties, state]
  );

  return <SimpleSelect options={options} {...props} />;
}

export function CitySelect({ state, ...props }: { state: string } & any) {
  const { data } = useBaseData();
  const options = useMemo(
    () =>
      data?.cities
        ?.filter((city) => {
          if (!state) return true;

          return city.state === state;
        })
        .map((city) => ({
          label: `${city.name}, ${city.state}`,
          value: city.name,
        })),
    [data?.cities, state]
  );

  return (
    <SimpleSelect
      options={options}
      components={{
        Option: (props) => {
          if (props.data.__isNew__) {
            return (
              <ReactSelectComponents.Option {...props}>
                {props.value}
              </ReactSelectComponents.Option>
            );
          }

          return <ReactSelectComponents.Option {...props} />;
        },
      }}
      {...props}
    />
  );
}

export function SettingSelect({ setting, ...props }: any) {
  const { data } = useBaseData();

  const options = useMemo(() => {
    return data?.[setting]?.map((value) => ({
      label: value,
      value: value,
    }));
  }, [data?.[setting]]);

  return <SimpleSelect options={options} {...props} />;
}

export function RequestStatusSelect(props) {
  return <SimpleSelect options={REQUEST_STATUS_OPTIONS} {...props} />;
}

export function parseNameString(nameString: string) {
  if (!nameString) return { firstName: "", lastName: "", middleName: "" };

  const parts = nameString.trim().split(/\s+/);

  if (parts.length === 1) {
    // Only one part - treat as first name
    return {
      firstName: parts[0],
      lastName: "",
      middleName: "",
    };
  } else if (parts.length === 2) {
    // Two parts - treat as first and last name
    return {
      firstName: parts[0],
      lastName: parts[1],
      middleName: "",
    };
  } else {
    // Three or more parts - treat first part as first name, last part as last name, and middle parts as middle name
    const firstName = parts[0];
    const lastName = parts[parts.length - 1];
    const middleName = parts.slice(1, parts.length - 1).join(" ");

    return {
      firstName,
      middleName,
      lastName,
    };
  }
}

export function ClioContactSelect({
  allowCreate = false,
  onChange,
  ...props
}: { allowCreate?: boolean; onChange: Function } & any) {
  const { create: createClioContact, isPending } = useCreateClioContact();

  const handleCreation = (inputValue: string) => {
    const { firstName, middleName, lastName } = parseNameString(inputValue);

    return {
      __isNew__: true,
      name: inputValue,
      firstName,
      middleName,
      lastName,
      type: "Person",
      value: inputValue,
      label: inputValue,
    };
  };

  const handleChange = async (selected: any) => {
    // If nothing is selected, pass null to the parent onChange
    if (!selected) {
      return onChange(null);
    }

    // If this is a new contact that needs to be created
    if (selected.__isNew__ && allowCreate) {
      // Parse the name from the input string
      const { firstName, lastName, middleName } = parseNameString(
        selected.value
      );

      // Make sure we have at least a first or last name
      if (!firstName && !lastName) {
        console.error("Cannot create contact: Name is required");
        return onChange(selected);
      }

      // Create the contact data object with required fields but don't create it yet
      // Keep the __isNew__ flag and add the parsed name data
      const contactData = {
        ...selected, // This already has __isNew__: true from react-select
        name: selected.value,
        type: "Person",
        firstName: firstName || "",
        lastName: lastName || "",
        middleName: middleName || "",
      };

      // Pass the contact data to the parent onChange without creating it
      onChange(contactData);
    } else {
      // For existing contacts, just pass the selected value
      onChange(selected);
    }
  };

  return (
    <ModelSelect
      searchFields={["name", "email", "phone"]}
      fields={[
        "name",
        "clioId",
        "type",
        "email",
        "phone",
        "address",
        "first_name",
        "last_name",
      ]}
      labelField="name"
      model="clioContacts"
      returnFullObject={true}
      create={allowCreate}
      getNewOptionData={handleCreation}
      onChange={handleChange}
      isDisabled={isPending}
      components={{
        Option: (optionProps: any) => {
          if (optionProps.data.__isNew__) {
            const { firstName, middleName, lastName } = parseNameString(
              optionProps.value
            );
            const nameParts = [
              firstName && `First: ${firstName}`,
              middleName && `Middle: ${middleName}`,
              lastName && `Last: ${lastName}`,
            ].filter(Boolean);

            return (
              <ReactSelectComponents.Option {...optionProps}>
                <div className="font-bold">
                  New Clio Contact: "{optionProps.value}"
                </div>
                {nameParts.length > 0 && (
                  <div className="text-xs text-gray-500">
                    {nameParts.join(", ")}
                  </div>
                )}
              </ReactSelectComponents.Option>
            );
          }

          const clioContact = optionProps.data;

          return (
            <ReactSelectComponents.Option {...optionProps}>
              <div className="font-bold">{clioContact.name}</div>
              <div
                className={classNames("text-xs", {
                  "text-gray-500": !optionProps.isSelected,
                  "text-gray-300": optionProps.isSelected,
                })}
              >
                Phone: {clioContact.phone || "No phone"}
                <br />
                Email: {clioContact.email || "No email"}
                <br />
                Address: {clioContact.address || "No address"}
              </div>
            </ReactSelectComponents.Option>
          );
        },
      }}
      {...props}
    />
  );
}

export function ClioMatterSelect(props: any) {
  return (
    <ModelSelect
      searchFields={["matterName", "description"]}
      fields={["matterName", "clioId", "description", "practiceArea"]}
      labelField="matterName"
      model="clioMatters"
      returnFullObject={true}
      components={{
        Option: (optionProps: any) => {
          if (optionProps.data.__isNew__) {
            return (
              <ReactSelectComponents.Option {...optionProps}>
                <div className="font-bold">
                  New Clio Matter: "{optionProps.value}"
                </div>
              </ReactSelectComponents.Option>
            );
          }

          const clioMatter = optionProps.data;

          return (
            <ReactSelectComponents.Option {...optionProps}>
              <div className="font-bold">
                {clioMatter.matterName} - {clioMatter.description}
              </div>
              <div
                className={classNames("text-xs", {
                  "text-gray-500": !optionProps.isSelected,
                  "text-gray-300": optionProps.isSelected,
                })}
              >
                Description: {clioMatter.description || "No description"}
                <br />
                Practice Area: {clioMatter.practiceArea || "No practice area"}
              </div>
            </ReactSelectComponents.Option>
          );
        },
      }}
      {...props}
    />
  );
}
