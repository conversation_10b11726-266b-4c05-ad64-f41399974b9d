import AdjustmentsHorizontalIcon from "@heroicons/react/24/outline/AdjustmentsHorizontalIcon";
import {
  CASE_STATUS_OPTIONS,
  STAGE_OPTIONS,
  TEMPLATE_STATE_OPTIONS,
} from "@shared/constants";
import {
  AttorneySelect,
  ClientSelect,
  CountySelect,
  DefendantSelect,
  PropertySelect,
  StaffSelect,
} from "@web/src/components/forms/selects";
import { useUrlQuery } from "@web/src/lib/hooks";
import { debounce, find } from "lodash";
import { useCallback } from "react";
import Select from "react-select";
import { Button } from "../buttons";
import { Popover } from "../utils";
import { InputElement } from "./forms";

export function TextFilter({ paramKey, Component = InputElement, ...props }) {
  const { params, updateParams } = useUrlQuery();
  const onChange = useCallback(
    debounce(
      (event) => updateParams({ [paramKey]: event.target.value || null }),
      150
    ),
    [params]
  );
  const value = params[paramKey];

  return <Component {...props} onChange={onChange} defaultValue={value} />;
}

export function SelectFilter({ paramKey, ...props }) {
  const { params, updateParams } = useUrlQuery();

  return (
    <Select
      {...props}
      value={find(props.options, { value: params[paramKey] || null })}
      onChange={(value) => updateParams({ [paramKey]: value?.value })}
      styles={{
        option: (styles) => ({ ...styles, fontSize: 12 }),
      }}
    />
  );
}

export function CustomSelectFilter({
  paramKey,
  Component = Select,
  isMulti,
  ...props
}: {
  paramKey: string;
  Component: any;
  isMulti?: boolean;
} & Record<string, any>) {
  const { params, updateParams } = useUrlQuery();
  const onChange = (selected: any) => updateParams({ [paramKey]: selected });
  const closeOnSelect = isMulti ? false : true;

  return (
    <Component
      closeMenuOnSelect={false}
      value={params[paramKey]}
      onChange={onChange}
      closeOnSelect={closeOnSelect}
      isClearable
      {...props}
    />
  );
}

export const ClientSelectFilter = (props) => (
  <CustomSelectFilter paramKey="clientId" Component={ClientSelect} {...props} />
);

export const DefendantSelectFilter = (props) => (
  <CustomSelectFilter
    paramKey="defendantId"
    Component={DefendantSelect}
    {...props}
  />
);

export const PropertySelectFilter = (props) => (
  <CustomSelectFilter
    paramKey="propertyId"
    Component={PropertySelect}
    {...props}
  />
);

export function CaseFilters({ propertyFilters }) {
  return (
    <div className="flex-grow flex">
      <div className="w-64">
        <TextFilter paramKey="search" placeholder="Search" autoFocus />
      </div>
      <Popover trigger="click">
        <Popover.Trigger>
          <Button className="btn-light ml-3">
            <AdjustmentsHorizontalIcon height={24} width={24} />
          </Button>
        </Popover.Trigger>
        <Popover.Content>
          <div className="w-72 py-2">
            <DefendantSelectFilter className="mb-3" />
            <ClientSelectFilter className="mb-3" />
            <PropertySelectFilter filters={propertyFilters} className="mb-3" />
            <SelectFilter
              paramKey="status"
              placeholder="Status"
              options={CASE_STATUS_OPTIONS}
              isClearable
              className="mb-3"
            />
            <SelectFilter
              paramKey="stage"
              placeholder="Stage"
              options={STAGE_OPTIONS}
              isClearable
              className="mb-3"
            />
            <SelectFilter
              paramKey="state"
              placeholder="State"
              options={TEMPLATE_STATE_OPTIONS}
              isClearable
              className="mb-3"
            />
            <CustomSelectFilter
              Component={StaffSelect}
              paramKey="staff"
              placeholder="Staff"
              className="mb-3"
            />
            <CustomSelectFilter
              Component={AttorneySelect}
              paramKey="attorney"
              placeholder="Attorney"
              className="mb-3"
            />
            <CustomSelectFilter
              Component={CountySelect}
              paramKey="county"
              placeholder="County"
            />
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
}
