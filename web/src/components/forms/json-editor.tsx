import { useEffect, useState } from "react";

interface JsonEditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: string;
}

export function JsonEditor({ value, onChange, height = "400px" }: JsonEditorProps) {
  const [error, setError] = useState<string | null>(null);
  const [formattedValue, setFormattedValue] = useState<string>(value);

  // Format the JSON when the value changes
  useEffect(() => {
    try {
      if (value) {
        // Parse and then stringify with indentation
        const parsed = JSON.parse(value);
        const formatted = JSON.stringify(parsed, null, 2);
        setFormattedValue(formatted);
        setError(null);
      } else {
        setFormattedValue("");
      }
    } catch (err) {
      // If it's not valid JSON, just use the raw value
      setFormattedValue(value);
      setError("Invalid JSON format");
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    try {
      // Validate JSON
      JSON.parse(newValue);
      setError(null);
      onChange(newValue);
    } catch (err) {
      setError("Invalid JSON format");
      onChange(newValue); // Still update the value so user can fix it
    }
  };

  return (
    <div className="flex flex-col">
      <textarea
        className={`font-mono text-sm p-2 border rounded whitespace-pre ${
          error ? "border-red-500" : "border-gray-300"
        }`}
        style={{ height, resize: "vertical" }}
        value={formattedValue}
        onChange={handleChange}
        spellCheck="false"
      />
      {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
    </div>
  );
}
