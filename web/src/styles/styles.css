@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "QuadrantText";
    src: url("../styles/fonts/QuadrantText-Regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: "QuadrantText";
    src: url("../styles/fonts/QuadrantText-Medium.woff2") format("woff2");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: "QuadrantText";
    src: url("../styles/fonts/QuadrantText-Italic.woff2") format("woff2");
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }
  @font-face {
    font-family: "SuisseIntl";
    src: url("../styles/fonts/SuisseIntl-Regular-WebM.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: "SuisseIntl";
    src: url("../styles/fonts/SuisseIntl-RegularItalic-WebM.woff2")
      format("woff2");
    font-weight: 400;
    font-style: italic;
    font-display: swap;
  }

  /* Set base font to SuisseIntl for body content */
  html,
  body {
    font-family: "SuisseIntl", system-ui, sans-serif;
  }

  /* Set headings to use QuadrantText */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "QuadrantText", system-ui, sans-serif;
  }
}

.tab {
  --tab-border-color: #bbb;
}

.btn:not(.btn-sm, .btn-xs, .btn-lg, .btn-xl),
.btn-md {
  height: 2.4rem;
  min-height: 2.4rem;
}

.lusitana-regular {
  font-family: "Lusitana", serif;
  font-weight: 400;
  font-style: normal;
}

.lusitana-bold {
  font-family: "Lusitana", serif;
  font-weight: 700;
  font-style: normal;
}

.inter-regular {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "slnt" 0;
}

.inter-bold {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  font-variation-settings: "slnt" 0;
}

.shrink-table-cell {
  width: 1px;
}

.input {
  height: 2.4rem;
}

.input-sm {
  height: 2rem;
}

a.themed-link {
  text-decoration: none;
  position: relative;
  transition: all 0.1s ease-in-out;
}

a.themed-link:hover {
  text-decoration: underline;
  text-decoration-thickness: 2px;
}

.react-datepicker__close-icon {
  color: #ccc !important;
  padding-right: 12px !important;
}

.react-datepicker__close-icon:hover {
  color: #777 !important;
}

.react-datepicker__close-icon::after {
  content: "✕" !important;
  color: inherit !important;
  font-size: 1.3rem !important;
  font-weight: 900 !important;
  background-color: transparent !important;
  font-family: monospace !important;
}
