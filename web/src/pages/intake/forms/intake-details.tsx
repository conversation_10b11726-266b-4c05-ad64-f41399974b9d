import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { Tooltip } from "@web/src/components/utils";
import { format } from "date-fns";
import { Card } from "react-daisyui";
import { useParams } from "react-router-dom";
import {
  ClioDocumentLink,
  CognitoDocumentLink,
  EmailLink,
} from "../../intake-requests/components";
import { useIntakeRequest } from "../../intake-requests/hooks";

export default function IntakeDetails() {
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId);

  // Handle the case where there's no data available
  if (!intakeRequest)
    return <div className="p-4 text-gray-500">No intake details available</div>;

  return (
    <>
      <Card className="bg-slate-100 mb-3 rounded">
        <Card.Body className="px-3 py-2">
          <Card.Title className="text-md font-normal flex justify-between items-center">
            <div className="flex items-center gap-2">
              {intakeRequest?.type}
              {intakeRequest?.submittedDate &&
                ` - ${format(new Date(intakeRequest.submittedDate), "PPP")}`}
              {intakeRequest.priority && (
                <span className="badge badge-error text-white">Priority</span>
              )}
            </div>
            <div className="flex items-center justify-end gap-2">
              <Tooltip
                title={
                  intakeRequest?.formEntryId
                    ? "View in Cognito Forms"
                    : "No Cognito form found"
                }
              >
                <CognitoDocumentLink
                  intakeRequest={intakeRequest}
                  className="px-2 btn-ghost btn btn-sm"
                />
              </Tooltip>
              <Tooltip
                title={
                  intakeRequest?.clioFolderId
                    ? "View documents in Clio"
                    : "No Clio folder found"
                }
              >
                <ClioDocumentLink
                  intakeRequest={intakeRequest}
                  className="px-2 btn-ghost btn btn-sm"
                />
              </Tooltip>
              <EmailLink intakeRequest={intakeRequest} iconOnly={true} />
            </div>
          </Card.Title>
          <MetadataTable>
            <div className="flex gap-6">
              <div className="flex-grow">
                <MetadataItem compact label="Tenant Name">
                  {intakeRequest.tenantName}
                </MetadataItem>
                <MetadataItem compact label="Unit">
                  {intakeRequest.unitNumber}
                </MetadataItem>
                <MetadataItem compact label="Client">
                  {intakeRequest.clientName}
                </MetadataItem>
                <MetadataItem compact label="Property">
                  {intakeRequest.propertyName}
                </MetadataItem>
              </div>
              <div className="flex-grow">
                <MetadataItem compact label="County">
                  {intakeRequest.county}
                </MetadataItem>
                <MetadataItem compact label="State">
                  {intakeRequest.state}
                </MetadataItem>
              </div>
            </div>
          </MetadataTable>
        </Card.Body>
      </Card>
    </>
  );
}
