import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  createRoutesFromElements,
  Navigate,
  Route,
} from "react-router-dom";

import Efilings from "@web/src/pages/activities/efilings";
import ClioSettings from "@web/src/pages/admin/settings/clio";
import ClioData from "@web/src/pages/admin/settings/clio-data";
import AllCounties, {
  CountyTable,
} from "@web/src/pages/admin/settings/counties/counties";
import DebugTool from "@web/src/pages/admin/settings/debug-tool";
import FirmSettings from "@web/src/pages/admin/settings/firm";
import FormSettings from "@web/src/pages/admin/settings/forms";
import OptionSettings from "@web/src/pages/admin/settings/options/options";
import Settings from "@web/src/pages/admin/settings/settings";
import StateSettings from "@web/src/pages/admin/settings/states/states";
import UserCreate from "@web/src/pages/admin/users/user-create";
import UserEdit from "@web/src/pages/admin/users/user-edit";
import Users from "@web/src/pages/admin/users/users";
import App from "@web/src/pages/app";
import CaseView from "@web/src/pages/cases/case";
import CaseCreate from "@web/src/pages/cases/case-create";
import CaseEdit from "@web/src/pages/cases/case-edit";
import Cases from "@web/src/pages/cases/cases";
import CustomFieldsForm from "@web/src/pages/cases/custom-fields";
import CaseDetails from "@web/src/pages/cases/details";
import Documents from "@web/src/pages/cases/documents";
import CaseEfilings from "@web/src/pages/cases/efilings";
import Client from "@web/src/pages/clients/client";
import ClientCreate from "@web/src/pages/clients/client-create";
import ClientEdit from "@web/src/pages/clients/client-edit";
import Clients from "@web/src/pages/clients/clients";
import Properties from "@web/src/pages/clients/properties/properties";
import Property from "@web/src/pages/clients/properties/property";
import PropertyCreate from "@web/src/pages/clients/properties/property-create";
import PropertyEdit from "@web/src/pages/clients/properties/property-edit";
import Defendant from "@web/src/pages/defendants/defendant";
import DefendantCreate from "@web/src/pages/defendants/defendant-create";
import Defendants from "@web/src/pages/defendants/defendants";
import IntakeRequests from "@web/src/pages/intake-requests/requests";
import CountyCreate from "./admin/settings/counties/county-create";
import CountyEdit from "./admin/settings/counties/county-edit";
import EmailTemplateCreate from "./admin/settings/email-templates/template-create";
import EmailTemplateEdit from "./admin/settings/email-templates/template-edit";
import EmailTemplateSettings from "./admin/settings/email-templates/templates";
import OptionEdit from "./admin/settings/options/option-edit";
import StateCreate from "./admin/settings/states/state-create";
import StateEdit from "./admin/settings/states/state-edit";
import TemplateCopy from "./admin/settings/templates/template-copy";
import TemplateCreate from "./admin/settings/templates/template-create";
import TemplateEdit from "./admin/settings/templates/template-edit";
import {
  default as AllTemplates,
  TemplateTable,
} from "./admin/settings/templates/templates";
import ActivityLog from "./cases/activity-log";
import { Checklists } from "./cases/checklists/checklists";
import Templates from "./cases/templates";
import ClientIntakePage from "./client-intake/client-intake";
import ConnectClioMatter from "./clio/clio-matter-form";
import IntakeCreate from "./intake-requests/intake-create";
import IntakeList from "./intake/intake-list";

export const router = createBrowserRouter(
  createRoutesFromElements(
    <>
      {/* App routes */}
      <Route path="/" element={<App />}>
        <Route index element={<Navigate to="/cases" replace />} />

        <Route path="intake">
          <Route index element={<IntakeList />} />
          <Route
            path=":requestId/create"
            id="intake-create"
            element={<IntakeCreate />}
          >
            <Route path=":tab" />
          </Route>
        </Route>

        <Route path="cases">
          <Route index element={<Cases />} />
          <Route path="create" id="case-create" element={<CaseCreate />}>
            <Route path=":tab" />
          </Route>
          <Route path=":caseId" id="case" element={<CaseView />}>
            <Route index element={<ActivityLog />} />
            <Route path="details" element={<CaseDetails />} />
            <Route path="documents" element={<Documents />} />
            <Route path="templates" element={<Templates />}>
              <Route path=":court" />
            </Route>
            <Route path="efilings" element={<CaseEfilings />} />
            <Route path="checklist" element={<Checklists />} />
            <Route
              path="templates/custom-fields/:templateId"
              element={<CustomFieldsForm />}
            />
          </Route>
          <Route path=":caseId/edit" id="case-edit" element={<CaseEdit />}>
            <Route path=":tab" />
          </Route>
        </Route>

        <Route path="requests">
          <Route index element={<IntakeRequests />} />
          <Route
            path=":requestId/create-clio-matter"
            element={<ConnectClioMatter />}
          />
          <Route path=":requestId/create" element={<IntakeCreate />}>
            <Route path=":tab" />
          </Route>
        </Route>

        <Route path="defendants">
          <Route index element={<Defendants />} />
          <Route path="create" element={<DefendantCreate />} />
          <Route path=":defendantId" id="defendant">
            <Route index element={<Defendant />} />
            <Route path="edit" element={<Defendant />} />
          </Route>
        </Route>

        <Route path="efilings">
          <Route index element={<Efilings />} />
        </Route>

        <Route path="clients">
          <Route index element={<Clients />} />
          <Route path="create" element={<ClientCreate />} />
          <Route path=":clientId" id="client">
            <Route index element={<Client />} />
            <Route path="edit" element={<ClientEdit />} />
            <Route path="contacts/create" element={<Client />} />
            <Route path="contacts/:contactId/edit" element={<Client />} />
          </Route>
        </Route>

        <Route path="properties">
          <Route index element={<Properties />} />
          <Route path="create" element={<PropertyCreate />} />
          <Route path=":propertyId" id="property">
            <Route index element={<Property />} />
            <Route path="edit" element={<PropertyEdit />} />
            <Route path="contacts/create" element={<Property />} />
            <Route path="contacts/:contactId/edit" element={<Property />} />
          </Route>
        </Route>

        <Route path="admin">
          <Route path="settings" element={<Settings />}>
            <Route path="firm" element={<FirmSettings />} />
            <Route path="clio" element={<ClioSettings />} />
            <Route path="forms" element={<FormSettings />} />

            <Route path="states">
              <Route index element={<StateSettings />} />
              <Route path="create" element={<StateCreate />} />
              <Route path=":stateId/edit" element={<StateEdit />} />
            </Route>

            <Route path="counties" element={<AllCounties />}>
              <Route index element={<Navigate to="OR" replace />} />
              <Route path=":state" element={<CountyTable />} />
            </Route>
            <Route path="counties/create" element={<CountyCreate />} />
            <Route path="counties/:countyId/edit" element={<CountyEdit />} />

            <Route path="templates" element={<AllTemplates />}>
              <Route index element={<Navigate to="OR" replace />} />
              <Route path=":state" element={<TemplateTable />} />
            </Route>
            <Route path="templates/copy" element={<TemplateCopy />} />
            <Route path="templates/create" element={<TemplateCreate />} />
            <Route
              path="templates/:templateId/edit"
              element={<TemplateEdit />}
            />

            <Route path="email-templates">
              <Route index element={<EmailTemplateSettings />} />
              <Route path="create" element={<EmailTemplateCreate />} />
              <Route path=":templateId/edit" element={<EmailTemplateEdit />} />
            </Route>

            <Route path="options">
              <Route index element={<OptionSettings />} />
              <Route path=":key/edit" element={<OptionEdit />} />
            </Route>

            <Route path="debug-tool" element={<DebugTool />} />
          </Route>

          <Route path="clio-data" element={<ClioData />} />

          <Route path="users">
            <Route index element={<Users />} />
            <Route path=":userId/edit" element={<UserEdit />} />
            <Route path="create" element={<UserCreate />} />
          </Route>
        </Route>

        <Route path="*" element={<Navigate to="/cases" replace />} />
      </Route>

      {/* Public routes */}
      <Route path="portal">
        <Route path="intake" element={<ClientIntakePage />} />
      </Route>
    </>
  )
);
