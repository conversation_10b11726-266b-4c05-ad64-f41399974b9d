import { MapPinIcon, ScaleIcon } from "@heroicons/react/24/outline";
import { CASE_STATUSES } from "@shared/constants";
import { CaseClass } from "@shared/db/schema/cases";
import {
  AddressLink,
  ThemedLink,
  TruncateText,
} from "@web/src/components/content";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { useBaseData } from "@web/src/lib/hooks";
import classNames from "classnames";
import { Card } from "react-daisyui";
import { NavLink } from "react-router-dom";
import ContactCopyAllEmails from "../clients/contacts/contact-copy-all-emails";
import { Checklists } from "./checklists/checklists";

export default function CaseInfo({ caseData }: { caseData: CaseClass }) {
  const { data } = useBaseData();
  if (!caseData) return null;

  const county = data.counties?.find(
    (county) => county.name === caseData.property?.county
  );
  const courtInfo = county?.getCourtInfo(
    caseData.defendant?.zip!,
    caseData.courtType
  );

  return (
    <>
      <h1 className="text-xl mb-3 flex justify-between items-top">
        <div className="min-w-0">
          <div className="inline-flex items-center">
            <div className="pr-3">
              <ContactCopyAllEmails contacts={caseData.client?.contacts!} />
            </div>
            <ThemedLink to={`/clients/${caseData.client?.id}`} color="accent">
              <span>
                {caseData.client?.doNotUse && (
                  <span className="text-error">
                    <TruncateText>{caseData.client?.name}</TruncateText>
                  </span>
                )}
                {!caseData.client?.doNotUse && (
                  <TruncateText>{caseData.client?.name}</TruncateText>
                )}
              </span>
            </ThemedLink>
          </div>
          <br />
          <div className="flex items-center text-slate-500 font-light">
            <div className="pr-3">
              <ContactCopyAllEmails contacts={caseData.property?.contacts!} />
            </div>
            <ThemedLink
              to={`/properties/${caseData.property?.id}`}
              color="accent"
              className="max-w-[75%] min-w-0 overflow-x-hidden"
            >
              <span
                className={classNames({
                  "text-error": caseData.property?.doNotUse,
                })}
              >
                <TruncateText>{caseData.property?.name}</TruncateText>
              </span>
            </ThemedLink>
            <span className="px-3">.vs</span>
            <ThemedLink
              to={`/defendants/${caseData.defendant?.id}`}
              color="secondary"
              className="max-w-[75%] min-w-0"
            >
              <TruncateText>{caseData.getDefendantName()}</TruncateText>
            </ThemedLink>
          </div>
        </div>
        <NavLink to="edit/client" className="btn btn-sm btn-primary ml-3">
          Edit
        </NavLink>
      </h1>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-3">
          <div className="flex gap-6">
            <div className="flex-grow">
              <MetadataTable>
                <MetadataItem
                  title="Address"
                  label={<MapPinIcon height={24} width={24} />}
                >
                  <AddressLink
                    address1={caseData.defendant?.address1!}
                    address2={caseData.defendant?.address2!}
                    city={caseData.defendant?.city!}
                    state={caseData.defendant?.state!}
                    zip={caseData.defendant?.zip!}
                    county={caseData.property?.county}
                  />
                </MetadataItem>
              </MetadataTable>
            </div>
            <div className="flex-grow text-sm">
              <MetadataTable>
                <MetadataItem
                  title="Court Details"
                  label={<ScaleIcon height={24} width={24} />}
                >
                  <div>{courtInfo?.courtName}</div>
                  <div>
                    {caseData.caseNumber || (
                      <span className="text-gray-400">No case number set</span>
                    )}
                  </div>
                  <div>
                    {caseData.firstAppearanceTerms || (
                      <span className="text-gray-400">No terms set</span>
                    )}
                  </div>
                </MetadataItem>
              </MetadataTable>
            </div>
          </div>
        </Card.Body>
      </Card>

      {caseData.status === CASE_STATUSES.CANCELED && (
        <div className="text-center text-white tracking-widest bg-gray-600 m-5 p-5 rounded-lg">
          THIS INTAKE HAS BEEN CANCELED
        </div>
      )}
      {caseData.property?.state === "OR" && <Checklists />}
    </>
  );
}
