import {
  BellIcon,
  BuildingOffice2Icon,
  CalendarDaysIcon,
  ListBulletIcon,
  PencilSquareIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import { VerticalTab, VerticalTabs } from "@web/src/components/layout";
import CaseFormClient from "./case-form-client";
import CaseFormDates from "./case-form-dates";
import CaseFormDefendant from "./case-form-defendant";
import CaseFormDetails from "./case-form-details";
import CaseFormNotes from "./case-form-notes";
import CaseFormNotices from "./case-form-notices";

export default function CaseForm() {
  return (
    <VerticalTabs
      content={{
        client: CaseFormClient,
        defendant: CaseFormDefendant,
        details: CaseFormDetails,
        notices: CaseFormNotices,
        dates: CaseFormDates,
        notes: CaseFormNotes,
      }}
    >
      <VerticalTab to="client" label="Client">
        <BuildingOffice2Icon color="#8e44ad" />
      </VerticalTab>
      <VerticalTab to="defendant" label="Defendant">
        <UserIcon color="#2980b9" />
      </VerticalTab>
      <VerticalTab to="details" label="Details">
        <ListBulletIcon color="#27ae60" />
      </VerticalTab>
      <VerticalTab to="notices" label="Notices">
        <BellIcon color="#c0392b" />
      </VerticalTab>
      <VerticalTab to="dates" label="Dates">
        <CalendarDaysIcon color="#f39c12" />
      </VerticalTab>
      <VerticalTab to="notes" label="Notes">
        <PencilSquareIcon color="#34495e" />
      </VerticalTab>
    </VerticalTabs>
  );
}
