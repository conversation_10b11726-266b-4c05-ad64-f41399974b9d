import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { SERVICE_TYPE_OPTIONS } from "@shared/constants";
import { Placeholder } from "@web/src/components/content";
import {
  HookDatePicker,
  HookSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { NavLink } from "react-router-dom";

export default function CaseFormDates() {
  const formContext = useFormContext();
  const [{ state }, noticeServedDate] = formContext.watch([
    "meta",
    "caseDates.noticeServedDate",
  ]);
  const isWashington = state === "WA";
  const isOregon = state === "OR";
  const noState = !state;

  useEffect(() => {
    const isDirty =
      formContext.formState.dirtyFields?.caseDates?.noticeServedDate;
    const servedDate = noticeServedDate;

    if (isDirty && servedDate) {
      formContext.setValue(
        "caseDates.noticeExpiresDate",
        dayjs(servedDate).add(60, "days").toDate()
      );
    }
  }, [
    noticeServedDate,
    formContext.formState.dirtyFields?.caseDates?.noticeServedDate,
  ]);

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        <div className="flex justify-between items-center py-1 mb-2 border-b border-b-slate-300">
          <h2 className="text-lg">Dates</h2>
        </div>
        {noState && (
          <Placeholder minHeight={200}>
            Enter a defendant to view case dates
          </Placeholder>
        )}
        {isWashington && (
          <>
            <HookDatePicker name="caseDates.leaseDate" label="Lease Signed" />
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.noticeServedDate"
                label="Notice Served"
              />
              <HookSelect
                name="caseDates.noticeServedType"
                label="Service Type"
                options={SERVICE_TYPE_OPTIONS}
              />
            </div>
            <HookDatePicker
              name="caseDates.noticeExpiresDate"
              label="Notice Expires"
            />
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.sAndCToProcessServer"
                label="S&C Sent to Process Server"
              />
              <HookDatePicker
                name="caseDates.serviceDeadline"
                label="Service Deadline"
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.sAndCService"
                label="S&C Service"
              />
              <HookSelect
                name="caseDates.sAndCServiceType"
                label="Service Type"
                options={SERVICE_TYPE_OPTIONS}
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.sAndCresponseDeadline"
                label="S&C Response Deadline"
              />
              <HookDatePicker
                name="caseDates.tenantNoaSubmission"
                label="Tenant NOA Submission"
                placeholder="Date... (if applicable)"
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.showCauseHearing"
                label="Show Cause Hearing"
                showTimeSelect
              />
              <HookDatePicker
                name="caseDates.showCauseSetover"
                label="Show Cause Setover"
                showTimeSelect
              />
            </div>
            <HookDatePicker
              name="caseDates.ocNoaSubmission"
              label="OC NOA Submission"
              placeholder="Date... (if applicable)"
            />
            <div className="flex gap-3 items-end">
              <HookDatePicker
                name="caseDates.orderOnShowCauseSigned"
                label="Order on Show Cause Signed"
              />
              <HookToggle
                name="caseDates.moneyJudgement"
                label="Money Judgement"
                color="success"
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.lockoutPacketSent"
                label="Lockout Packet Sent"
                showTimeSelect
              />
              <HookDatePicker
                name="caseDates.lockout"
                label="Lockout Date"
                showTimeSelect
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker name="caseDates.dismissed" label="Strike Filed" />
              <HookDatePicker
                name="caseDates.dismissalOrderSigned"
                label="Dismissal Order Signed"
              />
            </div>
          </>
        )}
        {isOregon && (
          <>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.dateFA"
                label="FA"
                showTimeSelect
              />
              <HookDatePicker
                name="caseDates.resetFA"
                label="FA Reset"
                showTimeSelect
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker name="caseDates.exParte" label="Ex Parte" />
              <HookDatePicker
                name="caseDates.hearing"
                label="Hearing"
                showTimeSelect
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker
                name="caseDates.nOfR"
                label="Notice of Restitution"
              />
              <HookDatePicker
                name="caseDates.trialDate"
                label="Trial"
                showTimeSelect
              />
            </div>
            <div className="flex gap-3">
              <HookDatePicker name="caseDates.writDate" label="Writ" />
              <HookDatePicker name="caseDates.moveoutDate" label="Move Out" />
            </div>
            <HookDatePicker
              name="caseDates.callDate"
              label="Call"
              showTimeSelect
            />
          </>
        )}
      </div>
      <div className="mt-4 flex justify-between">
        <NavLink to="notices" className="btn btn-ghost">
          <ChevronLeftIcon height={18} width={18} />
          Back (Notices)
        </NavLink>
        <NavLink to="notes" className="btn btn-primary">
          Next (Notes)
          <ChevronRightIcon height={18} width={18} />
        </NavLink>
      </div>
    </div>
  );
}
