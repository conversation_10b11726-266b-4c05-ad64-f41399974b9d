import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { PLAINTIFF_OPTIONS } from "@shared/constants";
import {
  HookButtonGroup,
  HookClientSelect,
  HookPropertySelect,
  HookSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useClient } from "@web/src/pages/clients/hooks";
import { useProperty } from "@web/src/pages/clients/properties/hooks";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { NavLink } from "react-router-dom";

export default function CaseFormClient() {
  const formContext = useFormContext();
  const [clientId, propertyId, { isNewClient }] = formContext.watch([
    "clientId",
    "propertyId",
    "meta",
  ]);
  const [originalClientId, setOriginalClientId] = useState(clientId);
  const [originalPropertyId, setOriginalPropertyId] = useState(propertyId);
  const { client } = useClient(clientId);
  const { property } = useProperty(propertyId);

  useEffect(() => {
    if (clientId !== originalClientId) {
      formContext.setValue("clientContact", "");
      setOriginalClientId(clientId);
    }
  }, [clientId, originalClientId]);

  useEffect(() => {
    if (propertyId !== originalPropertyId) {
      formContext.setValue("propertyContact", "");
      setOriginalPropertyId(propertyId);
    }
  }, [propertyId, originalPropertyId]);

  const onPropertySelect = (property: any) => {
    formContext.setValue("property", property);
  };

  const propertyFilters: any = {};
  if (clientId && !isNewClient) propertyFilters.clientId = clientId;

  const propertyContacts = property?.contacts || [];
  const clientContacts = client?.contacts || [];

  const propertyContactOptions = propertyContacts
    .filter(({ deletedAt }) => !deletedAt)
    .map(({ name }) => ({
      label: name,
      value: name,
    }));

  const clientContactOptions = clientContacts
    .filter(({ deletedAt }) => !deletedAt)
    .map(({ name }) => ({
      label: name,
      value: name,
    }));

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        <HookClientSelect
          name="clientId"
          label="Client"
          placeholder="Search Clients"
          rules={{
            required: "Client is required",
          }}
        />

        <HookPropertySelect
          filters={propertyFilters}
          selectCallback={onPropertySelect}
          name="propertyId"
          label="Property"
          placeholder="Search Properties"
          rules={{
            required: "Property is required",
          }}
        />

        <HookButtonGroup
          label="Plaintiff Type"
          name="plaintiffs"
          options={PLAINTIFF_OPTIONS}
        />

        <HookToggle
          name="isPickupCase"
          label="Pickup Case (uses legacy case caption if marked Client & Property above)"
          color="success"
        />

        <div className="flex gap-3">
          <HookSelect
            name="clientContact"
            options={clientContactOptions}
            clearable={true}
            label="Client Contact"
            placeholder="Client Contact"
          />
          <HookSelect
            name="propertyContact"
            options={propertyContactOptions}
            clearable={true}
            label="Property Contact"
            placeholder="Property Contact"
          />
        </div>
      </div>

      <div className="mt-4 flex justify-end">
        <NavLink to="defendant" className="btn btn-primary">
          Next (Defendant)
          <ChevronRightIcon height={18} width={18} />
        </NavLink>
      </div>
    </div>
  );
}
