import { ChevronLeftIcon } from "@heroicons/react/24/outline";
import {
  HookExpandingTextarea,
  HookSettingSelect,
} from "@web/src/components/forms/forms";
import { useFormContext } from "react-hook-form";
import { NavLink } from "react-router-dom";

export default function CaseFormNotes() {
  const formContext = useFormContext();
  const [{ state }] = formContext.watch(["meta"]);
  const isOregon = state === "OR";

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        {isOregon && (
          <HookSettingSelect
            label="First Appearance Terms"
            name="firstAppearanceTerms"
            setting="first_appearance_terms"
            isClearable
          />
        )}
        <HookExpandingTextarea label="Issues Notes" name="issueSummarySheet" />
        <HookExpandingTextarea
          label="General Notes"
          name="generalNotes"
          readOnly
          disabled
        />
      </div>
      <div className="mt-4 flex justify-start">
        <NavLink to="dates" className="btn btn-ghost">
          <ChevronLeftIcon height={18} width={18} />
          Back (Dates)
        </NavLink>
      </div>
    </div>
  );
}
