import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import {
  CASE_STATUS_OPTIONS,
  OR_COURT_TYPE_OPTIONS,
  WA_COURT_TYPE_OPTIONS,
} from "@shared/constants";
import { But<PERSON> } from "@web/src/components/buttons";
import {
  HookAttorneySelect,
  HookDatePicker,
  HookExpandingTextarea,
  HookInput,
  HookOpposingCounselSelect,
  HookSelect,
  HookSettingSelect,
  HookStaffSelect,
} from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { find } from "lodash";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { NavLink } from "react-router-dom";
import OpposingCounselForm from "./opposing-counsel-form";

export default function CaseFormDetails() {
  const formContext = useFormContext();
  const [{ state, isNewOpposingCounsel }, county, courtType] =
    formContext.watch(["meta", "property.county", "courtType"]);
  const [cachedCounty, setCachedCounty] = useState();
  const { data: baseData } = useBaseData();
  const isOregon = state === "OR";
  const isWashington = state === "WA";

  const onResetOpposingCounsel = () => {
    formContext.setValue("meta.isNewOpposingCounsel", false);
    formContext.setValue("opposingCounsel", null);
  };

  const onNewOpposingCounsel = (name) => {
    formContext.setValue("meta.isNewOpposingCounsel", true);
    formContext.setValue("opposingCounselId", null);
    formContext.setValue("opposingCounsel", { name });
  };

  useEffect(() => {
    if (isWashington) return;

    const countyChanged = county !== cachedCounty && county;

    if (countyChanged && (cachedCounty || !courtType)) {
      const defaultCourtType = find(baseData?.counties, {
        name: county,
      })?.defaultCourtType;

      if (defaultCourtType !== courtType && defaultCourtType) {
        formContext.setValue("courtType", defaultCourtType);
      }
    }

    setCachedCounty(county);
  }, [county, courtType]);

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        <div className="flex justify-between items-center py-1 mb-2 border-b border-b-slate-300">
          <h2 className="text-lg">
            {formContext.watch("status") === "Intake"
              ? "Intake Details"
              : "Case Details"}
          </h2>
        </div>
        {/* <div>
          <HookToggle name="priority" label="Priority" color="success" />
          <HookToggle name="filed" label="Filed" color="success" />
          <HookToggle name="canceled" label="Canceled" color="success" />
        </div> */}
        <div className="flex gap-3">
          <HookInput
            label="Case #"
            name="caseNumber"
            placeholder="Case Number"
          />
          <HookSelect
            label="Status"
            options={CASE_STATUS_OPTIONS}
            name="status"
          />
        </div>
        <div className="flex gap-3">
          <HookAttorneySelect label="Attorney" name="attorney" isClearable />
          <HookStaffSelect label="Staff" name="staff" isClearable />
        </div>
        <div className="flex gap-3">
          <HookSettingSelect
            label="Property Type"
            name="residentialCommercial"
            setting="residence_type"
            isClearable
          />
          {isWashington && (
            <HookSelect
              label="Court Type"
              options={WA_COURT_TYPE_OPTIONS}
              name="courtType"
            />
          )}
          {isOregon && (
            <HookInput
              label="Court Room #"
              name="courtroomNumber"
              placeholder="Court Room Number"
            />
          )}
        </div>
        {isOregon && (
          <>
            <div className="flex gap-3">
              <HookInput
                label="Lease Type"
                name="leaseType"
                placeholder="Lease Type"
              />
              <HookSelect
                label="Court Type"
                options={OR_COURT_TYPE_OPTIONS}
                name="courtType"
              />
            </div>
            <div className="flex gap-3">
              <HookSettingSelect
                label="Limited or General Judgment of Restitution"
                placeholder="Select... (Default is General)"
                name="limitedOrGeneralJudgment"
                setting="judgment_type"
                isClearable
              />
              <HookSettingSelect
                label="Order or Judgment by Stipulation"
                placeholder="Select... (Default is Order)"
                name="orderOrJudgment"
                setting="stipulation_type"
                isClearable
              />
            </div>
            <HookDatePicker
              name="caseDates.stipulationAgreementDate"
              label="Stipulation Agreement Date"
            />
            <HookExpandingTextarea
              label="Description of Noncompliance"
              name="noncomplianceDesc"
              minRows={2}
              placeholder="Description of Noncompliance"
            />
          </>
        )}
        <div>
          <div className="flex justify-between items-center py-1 mb-2 border-b border-b-slate-300">
            <h2 className="text-lg">Opposing Counsel</h2>
            {!isNewOpposingCounsel && (
              <Button
                className="btn btn-xs btn-primary"
                onClick={() => onNewOpposingCounsel("")}
              >
                New Opposing Counsel
              </Button>
            )}
            {isNewOpposingCounsel && (
              <Button
                className="btn btn-xs btn-primary"
                onClick={() => onResetOpposingCounsel()}
              >
                Cancel
              </Button>
            )}
          </div>
          {!isNewOpposingCounsel && (
            <HookOpposingCounselSelect
              create
              onCreateOption={onNewOpposingCounsel}
              filters={{ type: "Opposing Counsel" }}
              name="opposingCounselId"
            />
          )}

          {isNewOpposingCounsel && (
            <div className="flex gap-3">
              <OpposingCounselForm prefix="opposingCounsel" />
            </div>
          )}
        </div>
      </div>
      <div className="mt-4 flex justify-between">
        <NavLink to="defendant" className="btn btn-ghost">
          <ChevronLeftIcon height={18} width={18} />
          Back (Defendant)
        </NavLink>
        <NavLink to="notices" className="btn btn-primary">
          Next (Notices) <ChevronRightIcon height={18} width={18} />
        </NavLink>
      </div>
    </div>
  );
}
