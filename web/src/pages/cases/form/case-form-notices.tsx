import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { Placeholder } from "@web/src/components/content";
import {
  HookCheckbox,
  HookInput,
  HookSettingSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { NavLink } from "react-router-dom";

export default function CaseFormNotices() {
  const formContext = useFormContext();
  const [{ state }, residentialCommercial, nt30DayWithCause] =
    formContext.watch([
      "meta",
      "residentialCommercial",
      "resNoticesOR.nt30DayWithCause",
    ]);
  const isWashington = state === "WA";
  const isOregon = state === "OR";
  const noState = !state;
  const isCommercial = residentialCommercial === "Commercial";

  useEffect(() => {
    if (!nt30DayWithCause) {
      formContext.setValue("resNoticesOR.causeIsNonpayment", false);
    }
  }, [nt30DayWithCause]);

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        <div className="flex justify-between items-center py-1 mb-2 border-b border-b-slate-300">
          <h2 className="text-lg">{state} Notice Types</h2>
        </div>
        {noState && (
          <Placeholder minHeight={200}>
            Enter a defendant to view notice types
          </Placeholder>
        )}
        {isWashington && (
          <HookSettingSelect
            label="Notice Type"
            name="waNoticeType"
            setting="notice_types"
          />
        )}
        {isOregon && isCommercial && (
          <div>
            <div className="flex gap-3">
              <HookCheckbox
                label="30-day month-to-month"
                name="commNoticesOR.nt30DayMonthToMonth"
              />
              <HookCheckbox
                label="30-day with cause"
                name="commNoticesOR.nt30DayWithCause"
              />
            </div>
            <div className="flex gap-3">
              <HookCheckbox
                label="Notice to Bona Fide Tenant"
                name="commNoticesOR.ntForeclosure"
              />
              <HookInput
                placeholder="Other notice"
                name="commNoticesOR.otherExplanation"
              />
            </div>
            <HookInput
              label="No Notice Explanation"
              placeholder="No notice explanation"
              name="commNoticesOR.noNoticeExplanation"
            />
          </div>
        )}
        {isOregon && !isCommercial && (
          <div>
            <div className="flex gap-3">
              <div className="w-full mb-2">
                <div className="form-control w-full">
                  <div
                    style={{ paddingTop: 6, paddingBottom: 6 }}
                    className="label py-1 items-center gap-2 justify-between bg-white border border-gray-300 rounded px-2"
                  >
                    <div className="flex items-center gap-2 justify-start">
                      <HookCheckbox
                        id="nt30DayWithCause"
                        name="resNoticesOR.nt30DayWithCause"
                        raw
                      />
                      <label
                        htmlFor="nt30DayWithCause"
                        className="label-text cursor-pointer"
                      >
                        30-day with cause
                      </label>
                    </div>
                    <div className="flex items-center gap-2 justify-end">
                      <label
                        htmlFor="causeIsNonpayment"
                        className="label-text cursor-pointer"
                      >
                        Stated cause is nonpayment
                      </label>
                      <HookToggle
                        id="causeIsNonpayment"
                        name="resNoticesOR.causeIsNonpayment"
                        disabled={!nt30DayWithCause}
                        color="success"
                        raw
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-3">
              <HookCheckbox
                label="90-day with cause"
                name="resNoticesOR.nt90DayWithCause"
              />
              <HookCheckbox
                label="30, 60, or 180-day without cause"
                name="resNoticesOR.nt30To180DayWithoutCause"
              />
            </div>
            <div className="flex gap-3">
              <HookCheckbox
                label="24-hour outrageous"
                name="resNoticesOR.nt24HourOutrageous"
              />
              <HookCheckbox
                label="24 or 48-hour drug/alcohol"
                name="resNoticesOR.nt24Or48DrugAlcohol"
              />
            </div>
            <div className="flex gap-3">
              <HookCheckbox
                label="24-hour domestic violence"
                name="resNoticesOR.nt24Domestic"
              />
              <HookCheckbox
                label="10-day pet, repeat or week-to-week"
                name="resNoticesOR.nt10DayPetRepeatWeekToWeek"
              />
            </div>
            <div className="flex gap-3">
              <HookCheckbox
                label="10 or 13-day nonpayment"
                name="resNoticesOR.nt10DayOr13DayNonpayment"
              />
              <HookInput
                placeholder="Other notice"
                name="resNoticesOR.otherExplanation"
              />
            </div>
            <HookInput
              label="No Notice Explanation"
              placeholder="No notice explanation"
              name="resNoticesOR.noNoticeExplanation"
            />
          </div>
        )}
      </div>
      <div className="mt-4 flex justify-between">
        <NavLink to="details" className="btn btn-ghost">
          <ChevronLeftIcon height={18} width={18} />
          Back (Details)
        </NavLink>
        <NavLink to="dates" className="btn btn-primary">
          Next (Dates)
          <ChevronRightIcon height={18} width={18} />
        </NavLink>
      </div>
    </div>
  );
}
