import { CheckCircleIcon } from "@heroicons/react/24/outline";
import { But<PERSON> } from "@web/src/components/buttons";
import { HookForm } from "@web/src/components/forms/forms";
import { useForm } from "react-hook-form";
import { Link, useNavigate, useParams } from "react-router-dom";
import CaseForm from "./form/case-form";
import { useCase, useUpdateCase } from "./hooks";
import { getCaseFormDefaults } from "./utils";

function CaseEditForm({ caseData }) {
  const navigate = useNavigate();
  const { update: updateCase, isPending } = useUpdateCase(caseData!);
  const formContext = useForm({ defaultValues: getCaseFormDefaults(caseData) });

  const onSubmit = async (data) => {
    try {
      const { meta, ...updates } = data;
      await updateCase(updates);
      navigate(`/cases/${caseData.id}`);
    } catch (error) {}
  };

  return (
    <div className="p-4" style={{ maxWidth: 800 }}>
      <HookForm context={formContext} onSubmit={onSubmit}>
        <CaseForm />
        <div className="p-4 rounded-md bg-slate-100 mt-4 flex justify-between">
          <Link className="btn btn-ghost" to={`/cases/${caseData.id}`}>
            Cancel
          </Link>
          <Button
            className="btn-success text-white"
            disabled={isPending}
            type="submit"
          >
            {formContext.watch("status") === "Intake"
              ? "Update Intake"
              : "Update Case"}
            <CheckCircleIcon height={18} width={18} />
          </Button>
        </div>
      </HookForm>
    </div>
  );
}

// We use a wrapper component here to make sure we have the
// case data loaded before we render the form the first time
// so the default values, which are only pulled on first render,
// are correct
export default function CaseEdit() {
  const { caseId } = useParams();
  // RE: gcTime, see NOTES section in README
  const { caseData } = useCase(caseId, { decrypt: true }, { gcTime: 0 });

  if (!caseData) return null;

  return <CaseEditForm caseData={caseData} />;
}
