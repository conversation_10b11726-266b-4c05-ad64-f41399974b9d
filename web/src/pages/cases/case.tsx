import { Column, Columns, Tab, Tabs } from "@web/src/components/layout";
import { useParams } from "react-router-dom";
import CaseInfo from "./case-info";
import { useCase } from "./hooks";

export default function CaseView() {
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);

  if (!caseData) return null;

  return (
    <Columns columnCount={2}>
      <Column>
        <CaseInfo caseData={caseData} />
      </Column>
      <Column className="!p-0">
        <div className="mt-4">
          <Tabs className="px-4">
            <Tab to="">Activity</Tab>
            <Tab to="details">Details</Tab>
            <Tab to="templates" end={false}>
              Templates
            </Tab>
            <Tab to="documents" end={false}>
              Documents
            </Tab>
            {caseData.defendant?.state === "OR" && (
              <Tab to={`efilings`}>Efilings</Tab>
            )}
          </Tabs>
        </div>
      </Column>
    </Columns>
  );
}
