import { DocumentIcon } from "@heroicons/react/24/outline";
import { But<PERSON> } from "@web/src/components/buttons";
import { Input } from "@web/src/components/forms/forms";

import {
  useBaseData,
  useDocuments,
  useRenderTemplate,
} from "@web/src/lib/hooks";
import dayjs from "dayjs";
import { capitalize, filter } from "lodash";
import { Toggle } from "react-daisyui";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useCase } from "./hooks";

function TemplateTable({
  label,
  category,
  courtType,
  state,
  county,
  caseId,
}: {
  label?: string;
  category?: string;
  courtType?: string;
  state: string;
  county?: string;
  caseId: number | string;
}) {
  const isOregon = state === "OR";
  const { data: baseData } = useBaseData();
  const { documents } = useDocuments({ caseId });

  const { renderTemplate } = useRenderTemplate();
  if (!baseData) return null;

  const templates = filter(baseData.templatesByState?.[state], (template) => {
    if (category && template.category !== category) return false;
    if (isOregon && courtType && template.courtType !== courtType) return false;
    if (county && template.county !== county) return false;
    return true;
  });

  if (!templates?.length) return null;

  return (
    <table className="table table-sm my-3">
      <thead>
        <tr>
          <th className="text-lg text-gray-800 font-normal mt-3">
            {label || category}
            {county && ` - ${county}`}
          </th>
        </tr>
      </thead>
      <tbody>
        {templates.map((template) => {
          const isPending = documents?.some((document) => {
            const isNew = dayjs()
              .subtract(5, "minutes")
              .isBefore(document.createdAt);
            const isPending = document.status === "pending";
            const matchesTemplate = document.fileName.startsWith(template.name);

            return isNew && isPending && matchesTemplate;
          });
          const hasCustomFields =
            template.customFields && template.customFields.length > 0;
          return (
            <tr key={template.id}>
              <td>
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <DocumentIcon
                      color="#345b80"
                      height="30"
                      width="30"
                      className="inline pr-2"
                    />
                    {template.name}
                  </div>
                  {isPending && (
                    <div className="flex items-center gap-2">
                      Generating
                      <span className="loading loading-infinity"></span>
                    </div>
                  )}
                  {!isPending && hasCustomFields && (
                    <Link
                      className="btn-xs btn btn-primary"
                      to={`custom-fields/${template.id}`}
                    >
                      Generate
                    </Link>
                  )}
                  {!isPending && !hasCustomFields && (
                    <Button
                      className="btn-xs btn-primary"
                      onClick={() =>
                        renderTemplate({
                          caseId: caseId as string,
                          templateId: template.id,
                          customFields: {},
                        })
                      }
                    >
                      Generate
                    </Button>
                  )}
                </div>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}

export default function Templates() {
  const { caseId, court } = useParams();
  const navigate = useNavigate();
  const { caseData } = useCase(caseId);
  const state = caseData?.defendant?.state;

  if (!caseData || !state) return null;

  const courtType = court || caseData.courtType || "circuit";
  const isCircuit = courtType === "circuit";
  const alternateCourtType = isCircuit ? "justice" : "circuit";
  const isOregon = caseData?.defendant?.state === "OR";

  return (
    <div className="mt-4">
      <div className="mt-5 px-3 py-1 flex justify-between items-center border-b-slate-400 border-b">
        <div className="text-xl">
          {isOregon ? capitalize(courtType) : null} Templates
        </div>
        {isOregon && (
          <div>
            <Input
              label={`Toggle to use ${capitalize(
                alternateCourtType
              )} templates`}
              groupClassName="mb-0"
              inline
            >
              <Toggle
                onChange={() => {
                  navigate(`/cases/${caseId}/templates/${alternateCourtType}`);
                }}
                color="primary"
                checked={!isCircuit}
              />
            </Input>
          </div>
        )}
      </div>
      <TemplateTable
        label="Frequently Used"
        category="Basic"
        state={state}
        caseId={caseData.id}
      />
      <TemplateTable
        label="County Specific"
        county={caseData.property?.county!}
        state={state}
        caseId={caseData.id}
      />
      <TemplateTable
        category="Initial Filings"
        courtType={courtType}
        state={state}
        caseId={caseData.id}
      />
      <TemplateTable
        label={isOregon ? "First Appearance Prep" : "Hearing Prep"}
        category="FA & Hearing Prep"
        courtType={courtType}
        state={state}
        caseId={caseData.id}
      />
      <TemplateTable
        category="Defaults & Restitutions"
        courtType={courtType}
        state={state}
        caseId={caseData.id}
      />
      <TemplateTable
        category="Dismissals"
        courtType={courtType}
        state={state}
        caseId={caseData.id}
      />
    </div>
  );
}
