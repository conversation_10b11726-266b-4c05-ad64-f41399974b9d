import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  HookInput,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useBaseData, useRenderTemplate } from "@web/src/lib/hooks";
import "react";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useCase } from "./hooks";

export default function CustomFieldsForm() {
  const navigate = useNavigate();
  const { caseId, templateId } = useParams();
  const { caseData } = useCase(caseId);
  const { data } = useBaseData();
  const { renderTemplate } = useRenderTemplate();

  if (!caseData) return null;

  const template = data.templatesByState?.[caseData.defendant?.state!].find(
    (tmplt) => tmplt.id === Number(templateId)
  );

  const defaultValues = {};
  for (const field of template.customFields) {
    if (field.type === "boolean") {
      if (field.key === "mailing") defaultValues[field.key] = true;
      else defaultValues[field.key] = false;
    } else {
      defaultValues[field.key] = "";
    }
  }

  const formContext = useForm({ defaultValues });
  const isSubmitting = formContext.formState.isSubmitting;
  const render = async (
    caseId: string,
    templateId: string,
    customFields = {}
  ) =>
    await renderTemplate({
      caseId,
      templateId,
      customFields,
    });

  const onSubmit = async (data) => {
    try {
      render(caseId!, templateId!, data);
      navigate(-1);
    } catch (error) {}
  };

  return (
    <FormWrapper maxWidth="none" className="mt-4">
      <FormHeader>{template.name} ::: Template Options</FormHeader>
      <HookForm context={formContext} onSubmit={onSubmit}>
        {template.customFields.map((field) => {
          if (field.type === "boolean") {
            return (
              <HookToggle
                key={field.key}
                label={field.label}
                name={field.key}
                color="success"
              />
            );
          } else if (field.type === "string") {
            return (
              <HookInput key={field.key} label={field.label} name={field.key} />
            );
          }
        })}
        <FormButtons>
          <Button className="btn-sm btn-ghost" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button
            className="btn-sm btn-primary"
            disabled={isSubmitting}
            type="submit"
          >
            Generate
          </Button>
        </FormButtons>
      </HookForm>
    </FormWrapper>
  );
}
