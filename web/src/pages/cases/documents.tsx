import { DocumentArrowDownIcon } from "@heroicons/react/24/outline";
import { useDocuments } from "@web/src/lib/hooks";
import dayjs from "dayjs";
import { useParams } from "react-router-dom";

export default function Documents() {
  const { caseId } = useParams();
  const { documents } = useDocuments({ caseId });
  const recentDocuments = documents?.filter((doc) =>
    dayjs().subtract(5, "minutes").isBefore(doc.createdAt)
  );

  return (
    <div className="mt-4">
      {Boolean(recentDocuments?.length) ? (
        <>
          <div className="mt-5 px-3 py-1 flex justify-between items-center border-b-slate-400 border-b">
            <div className="text-xl">Recent Documents</div>
          </div>
          <table className="table table-sm">
            <tbody>
              {recentDocuments.map((document) => (
                <tr key={document.id}>
                  <td>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <DocumentArrowDownIcon
                          color="#345b80"
                          height="30"
                          width="30"
                          className="inline pr-2"
                        />
                        {document.fileName}
                      </div>
                      {document.status === "complete" && (
                        <div className="flex items-center gap-2">
                          <a
                            href={`/__/files/${document.filePath}?action=open`}
                            className="btn btn-primary btn-xs text-white"
                            target="_blank"
                          >
                            open
                          </a>
                          <a
                            href={`/__/files/${document.filePath}?action=download`}
                            className="btn btn-success btn-xs text-white"
                          >
                            Download
                          </a>
                        </div>
                      )}
                      {document.status === "failed" && (
                        <span className="text-red-500">Failed</span>
                      )}
                      {document.status === "pending" && (
                        <div className="flex items-center gap-2">
                          Loading
                          <span className="loading loading-infinity"></span>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      ) : (
        <>
          <div className="mt-5 px-3 py-1 flex justify-between items-center border-b-slate-400 border-b">
            <div className="text-xl">Recent Documents</div>
          </div>
          <div className="mt-5">No Documents to View</div>
        </>
      )}
    </div>
  );
}
