import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { ThemedLink } from "@web/src/components/content";
import { Card } from "react-daisyui";
import { CaseStatus } from "../intake/components";

import { Link } from "react-router-dom";
export function CaseLink({ caseData }) {
  const isActive = caseData.status === "Active";

  return (
    <ThemedLink to={`/cases/${caseData.id}`} color="primary">
      <div className="flex items-center justify-between w-full">
        <div>
          <span className="font-semibold">
            {caseData.client?.name}
            {caseData.property?.county && (
              <> ~ {caseData.property.county} County</>
            )}
          </span>
          <br />
          <span className="text-slate-500">
            {caseData.caseNumber} {caseData.property?.name} vs.{" "}
            {caseData.getDefendantName()}
          </span>
        </div>
        <CaseStatus caseData={caseData} />
      </div>
    </ThemedLink>
  );
}

export function CaseCard({ caseData }) {
  return (
    <Card className="bg-slate-100 mb-3">
      <Card.Body className="px-4 py-2">
        <CaseLink caseData={caseData} />
      </Card.Body>
    </Card>
  );
}

export const SearchLink = ({
  url,
  searchName = "Clio",
  className,
  iconClassName,
  label,
}: {
  url: string;
  searchName?: string;
  className?: string;
  iconClassName?: string;
  label?: string;
}) => {
  return (
    <Link
      className={`hover ${className}`}
      to={url || "#"}
      target="_blank"
      rel="noopener noreferrer"
    >
      <MagnifyingGlassIcon className={iconClassName} />
      {label || `Search ${searchName}`}
    </Link>
  );
};
