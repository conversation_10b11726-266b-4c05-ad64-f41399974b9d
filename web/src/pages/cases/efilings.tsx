import { EfileButton } from "@web/src/components/buttons";
import EfileCard from "@web/src/components/cards";
import { Placeholder } from "@web/src/components/content";
import { useEfile } from "@web/src/lib/hooks";
import { useParams } from "react-router-dom";

export default function CaseEfilings() {
  const { caseId } = useParams();
  const { efilings } = useEfile({ caseId });

  return (
    <div className="mt-4">
      <div className="mt-5 mb-4 px-3 py-1 flex justify-between items-center border-b-slate-400 border-b">
        <div className="text-xl">Efilings</div>
        <EfileButton className="btn-sm" caseId={caseId} />
      </div>
      {Boolean(efilings.length) &&
        efilings.map((efile) => <EfileCard key={efile.id} efile={efile} />)}
      {!efilings.length && <Placeholder>Not yet filed</Placeholder>}
    </div>
  );
}
