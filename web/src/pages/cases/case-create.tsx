import { ISSUE_NOTES_DEFAULT } from "@shared/constants";
import {
  FormValidationButton,
  HookForm,
} from "@web/src/components/forms/forms";
import { useForm } from "react-hook-form";
import { Link, useLocation, useNavigate } from "react-router-dom";
import CaseForm from "./form/case-form";
import { useCreateCase } from "./hooks";
import { getCaseFormDefaults } from "./utils";

export default function CaseCreate() {
  console.log("CaseCreate");
  const navigate = useNavigate();
  const { state } = useLocation();
  const { create: createCase, isPending } = useCreateCase();
  const formContext = useForm({
    defaultValues: getCaseFormDefaults({
      status: "Active",
      residentialCommercial: "Residential",
      plaintiffs: "both",
      limitedOrGeneralJudgment: "General",
      orderOrJudgment: "Order",
      issueSummarySheet: ISSUE_NOTES_DEFAULT,
      ...state,
    }),
  });

  const onSubmit = async (data) => {
    try {
      const { meta, ...caseData } = data;
      console.log("caseData", caseData);
      const createdCase = await createCase(caseData);
      navigate(`/cases/${createdCase.id}`);
    } catch (error) {}
  };

  return (
    <div className="p-4" style={{ maxWidth: 800 }}>
      <HookForm context={formContext} onSubmit={onSubmit}>
        <CaseForm />
        <div className="p-4 rounded-md bg-slate-100 mt-4 flex justify-between">
          <Link
            className="btn btn-ghost"
            to={
              formContext.watch("status") === "Intake" ? `/requests` : `/cases`
            }
          >
            Cancel
          </Link>
          <FormValidationButton formContext={formContext} disabled={isPending}>
            {formContext.watch("status") === "Intake"
              ? "Create Intake"
              : "Create Case"}
          </FormValidationButton>
        </div>
      </HookForm>
    </div>
  );
}
