import { stringToHtml } from "@shared/utils";
import { CommentForm, CommentLogView } from "@web/src/components/comments";
import { ButtonGroup } from "@web/src/components/forms/forms";
import { useSSO } from "@web/src/lib/hooks";
import { useState } from "react";
import { But<PERSON>, <PERSON> } from "react-daisyui";
import { useParams } from "react-router-dom";
import { useComments, useCreateComment } from "../intake/hooks";
import { useCase } from "./hooks";

export default function ActivityLog() {
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);
  const { comments } = useComments({ caseId });
  const { user } = useSSO();
  const { createComment, isPending } = useCreateComment();
  const [commentsToShow, setCommentsToShow] = useState("comment");
  const onSubmit = (data) => {
    createComment(data);
  };
  if (!user) return null;

  return (
    <div className="mt-4">
      {caseData?.generalNotes && (
        <Card className="bg-slate-100 mb-3">
          <Card.Body className="px-4 py-2">
            <div className="flex flex-col justify-between">
              <span className="mb-5">Archival (add comments below)</span>
              <p
                className="text-sm whitespace-pre-line mb-0"
                dangerouslySetInnerHTML={{
                  __html: stringToHtml(caseData?.generalNotes!, {
                    doubleSpace: true,
                  }),
                }}
              />
            </div>
          </Card.Body>
        </Card>
      )}
      <div>
        <ButtonGroup
          options={[
            { value: "activity", label: "Activity" },
            { value: "both", label: "Both" },
            { value: "comment", label: "Comments" },
          ]}
          className="mb-3"
          buttonClassName="btn-sm btn-primary"
          value={commentsToShow}
          onChange={setCommentsToShow}
        />
        <div className="mb-1">
          <CommentForm
            comment={{
              caseId,
              staffId: user.id,
              description: "",
            }}
            onSubmit={onSubmit}
          >
            <Button
              className="btn-primary btn-sm"
              disabled={isPending}
              type="submit"
            >
              Add Comment
            </Button>
          </CommentForm>
        </div>

        {comments &&
          comments.map((comment) => {
            // Skip rendering based on filter selection
            if (commentsToShow === "comment" && comment.type === "auto")
              return null;
            if (commentsToShow === "activity" && comment.type === "manual")
              return null;

            return <CommentLogView comment={comment} key={comment.id} />;
          })}
      </div>
    </div>
  );
}
