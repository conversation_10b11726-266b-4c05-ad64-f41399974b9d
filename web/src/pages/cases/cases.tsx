import { AddressLink } from "@web/src/components/content";
import { CaseFilters } from "@web/src/components/forms/filters";
import { CaseLabelSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useUrlQuery } from "@web/src/lib/hooks";
import { times } from "lodash";
import { Link } from "react-router-dom";
import { CaseLink } from "./components";
import { useCases } from "./hooks";

export default function AllCases() {
  const { params } = useUrlQuery();
  const { cases, count, isFetching } = useCases(params);
  const { Table, SelectableTD, SelectableTH, Pager } = useTableWithQueryParams({
    totalRecordCount: count,
    data: cases,
  });

  if (!cases) return null;

  const propertyFilters: any = {};
  if (params.clientId) propertyFilters.clientId = params.clientId;

  return (
    <div className="px-4 pb-4">
      <div className="navbar sticky top-0 z-20 bg-base-100 flex items-center justify-between border-slate-300 border-b">
        <div className="flex items-center">
          {params.status !== "Intake" ? (
            <h1 className="text-3xl font-light pl-3 pr-8">Cases</h1>
          ) : (
            <h1 className="text-3xl font-light pl-3 pr-8">Intake</h1>
          )}
          <CaseFilters propertyFilters={propertyFilters} />
        </div>
        {params.status !== "Intake" && (
          <div>
            <Link to="/cases/create/client" className="btn btn-primary">
              Add Case
            </Link>
          </div>
        )}
      </div>

      <Table className="table table-sm table-pin-rows">
        <thead>
          <tr className="bg-base-100" style={{ top: "4rem" }}>
            <SelectableTH className="pt-4" />
            {params.status !== "Intake" ? (
              <th className="pt-4">Case</th>
            ) : (
              <th className="pt-4">Intake</th>
            )}
            <th>Address</th>
            <th>Status</th>
            <th>Staff</th>
            <th>Attorney</th>
          </tr>
        </thead>
        <tbody>
          {cases.length > 0 &&
            cases.map((data, index) => {
              return (
                <tr key={data.id}>
                  <SelectableTD value={data.id} index={index} />
                  <td>
                    <CaseLink caseData={data} />
                  </td>
                  <td>
                    <AddressLink
                      address1={data.defendant?.address1!}
                      address2={data.defendant?.address2!}
                      city={data.defendant?.city!}
                      state={data.defendant?.state!}
                      zip={data.defendant?.zip!}
                    />
                  </td>
                  <td>
                    <span>{data.status}</span>
                  </td>
                  <td>
                    <span>{data.staff}</span>
                  </td>
                  <td>
                    <span>{data.attorney}</span>
                  </td>
                </tr>
              );
            })}

          {isFetching &&
            cases.length == 0 &&
            times(30, () => (
              <tr>
                <td></td>
                <td>
                  <CaseLabelSkeleton />
                </td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            ))}
        </tbody>
      </Table>
      <Pager className="mt-8" showCount />
    </div>
  );
}
