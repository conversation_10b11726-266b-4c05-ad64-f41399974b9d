import {
  ArrowTopRightOnSquareIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import { Input } from "@web/src/components/forms/forms";
import { ShortStateSelect } from "@web/src/components/forms/selects";
import { useDefendant, useUpdateDefendant } from "../../defendants/hooks";
import { useCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";
import { ChecklistLink } from "./checklist-links";

export const ChecklistItemUSPS = ({ item }) => {
  const { caseData } = useCase(item.caseId);
  const { defendant } = useDefendant(String(caseData?.defendant?.id));
  const { update: updateDefendant } = useUpdateDefendant(defendant!);

  return (
    <ChecklistItemDefault item={item}>
      <ChecklistLink to="https://tools.usps.com/zip-code-lookup.htm?byaddress">
        <MapPinIcon className="h-[18px] w-[18px] inline mr-2" />
        USPS Address Verification
        <ArrowTopRightOnSquareIcon className="h-[18px] w-[18px] inline ml-2" />
      </ChecklistLink>
      <Input
        label="Address Line 1"
        name="defendant.address1"
        placeholder="Address Line 1"
        value={defendant?.address1 || ""}
        size={15}
        onChange={(value) => {
          updateDefendant({ address1: value.target.value });
        }}
      />
      <Input
        label="Address Line 2"
        name="defendant.address2"
        placeholder="Address Line 2"
        value={defendant?.address2 || ""}
        size={15}
        onChange={(value) => {
          updateDefendant({ address2: value.target.value });
        }}
      />
      <div className="flex gap-3 items-start align-start">
        <Input
          label="City"
          name="defendant.city"
          placeholder="City"
          value={defendant?.city || ""}
          size={15}
          onChange={(value) => {
            updateDefendant({ city: value.target.value });
          }}
        />
        <label className="w-full">
          <div className="label py-1 items-center cursor-pointer gap-2 text-sm justify-start">
            State
          </div>
          <ShortStateSelect
            label="State"
            name="defendant.state"
            value={defendant?.state || ""}
            onChange={(value) => {
              updateDefendant({ state: value });
            }}
          />
        </label>
        <Input
          label="Zip Code"
          name="defendant.zip"
          placeholder="Zip Code"
          value={defendant?.zip || ""}
          size={15}
          onChange={(value) => {
            updateDefendant({ zip: value.target.value });
          }}
        />
      </div>
    </ChecklistItemDefault>
  );
};
