import { InformationCircleIcon } from "@heroicons/react/24/outline";
import { useSSO } from "@web/src/lib/hooks";
import { useChecklistItemValidation } from "@web/src/pages/cases/checklists/checklist-validation";
import dayjs from "dayjs";
import { useState } from "react";
import { useUpdateChecklistItem } from "../hooks";

export const ChecklistItemDefault = ({
  item,
  children,
}: {
  item: any;
  children?: any;
}) => {
  const { update } = useUpdateChecklistItem(item);
  const [isComplete, setIsComplete] = useState(Boolean(item.complete));
  const { user } = useSSO();
  const isValidForCompletion = useChecklistItemValidation(item);
  async function handleToggleCompletion(
    event: React.ChangeEvent<HTMLInputElement>
  ) {
    // Only allow checking if validation passes or if unchecking
    if (event.target.checked && !isValidForCompletion) {
      return;
    }

    setIsComplete(event.target.checked);
    await update({
      completedById: user?.id,
      complete: event.target.checked,
      completedDate: new Date(),
    });
  }

  return (
    <div className="mb-2">
      <div className="flex items-center justify-between justify-items-center">
        <label
          key={item.id}
          className={`justify-start label gap-2 ${
            !isComplete && !isValidForCompletion
              ? "cursor-not-allowed opacity-60"
              : "cursor-pointer"
          }`}
        >
          <input
            type="checkbox"
            className={`checkbox checkbox-sm bg-white rounded-sm ${
              !isComplete && !isValidForCompletion
                ? "checkbox-disabled opacity-60"
                : ""
            }`}
            checked={isComplete}
            onChange={handleToggleCompletion}
            disabled={!isComplete && !isValidForCompletion}
          />
          <span className="flex items-center gap-1 font-semibold">
            {item.sop && (
              <a
                href={item.sop}
                target="_blank"
                rel="noreferrer"
                className="text-accent"
              >
                <InformationCircleIcon className="h-[24px] w-[24px]" />
              </a>
            )}
            {item.description}
            {!isComplete && !isValidForCompletion && (
              <span className="text-xs text-gray-500 ml-2">
                (Complete required actions below)
              </span>
            )}
          </span>
        </label>
        {item.complete && (
          <div className="text-sm text-slate-800">
            {item.completedBy?.name}
            {": "}
            {dayjs(item.updatedAt).format("M/D/YYYY")}
          </div>
        )}
      </div>
      <div className="flex flex-col ml-3 pl-2 border-l-slate-400 border-l-2">
        <div className="flex flex-col pl-3 gap-2">{children}</div>
      </div>
    </div>
  );
};
