import { ChecklistItem } from "@shared/db/schema/checklist-items";
import { Button } from "@web/src/components/buttons";
import { CommentForm, CommentListView } from "@web/src/components/comments";
import { useComments, useCreateComment } from "@web/src/pages/intake/hooks";
import { useParams } from "react-router-dom";
import { ChecklistItemDefault } from "./checklist-item-default";

export const ChecklistItemComments = ({ item }: { item: ChecklistItem }) => {
  const { caseId } = useParams();
  const { comments } = useComments({
    caseId,
    type: "manual",
    workflowCheck: "prep",
  });
  const { createComment } = useCreateComment();
  const newComment = { caseId, description: "" };
  const workflowComment = comments?.[0];
  const onSubmit = (data) => {
    createComment(data);
  };
  if (!caseId) return null;

  return (
    <ChecklistItemDefault item={item}>
      <>
        {item.description === "Enter Prep Notes" && !workflowComment && (
          <CommentForm
            comment={newComment}
            onSubmit={onSubmit}
            workflowCheck="prep"
          >
            <Button className="btn-primary btn-xs" type="submit">
              {"Add"}
            </Button>
            <Button className="btn-ghost btn-xs">Cancel</Button>
          </CommentForm>
        )}
      </>
      <CommentListView
        caseId={Number(caseId)}
        workflowCheck="prep"
        allowNewComment={false}
      ></CommentListView>
    </ChecklistItemDefault>
  );
};
