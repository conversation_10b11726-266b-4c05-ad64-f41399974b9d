import { useParams } from "react-router-dom";
import { SearchLink } from "../components";
import { useCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";

export const ChecklistItemConflictCheck = ({ item }) => {
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);
  if (!caseData) return null;

  return (
    <ChecklistItemDefault item={item}>
      <div>
        <div className="flex items-center">
          {caseData.defendant?.firstName} {caseData.defendant?.middleName}{" "}
          {caseData.defendant?.lastName} {caseData.defendant?.suffix}
          <SearchLink
            url={`https://app.clio.com/nc/#/search?query=${caseData.defendant?.firstName} ${caseData.defendant?.lastName}`}
            searchName="Clio"
            className="btn btn-sm btn-ghost text-primary"
            iconClassName="h-[18px] w-[18px]"
          />
        </div>
      </div>
      {caseData.additionalDefendants &&
        caseData.additionalDefendants.map((defendant, index) => (
          <div key={index}>
            <div className="flex items-center">
              {defendant.firstName} {defendant.middleName} {defendant.lastName}{" "}
              {defendant.suffix}
              <SearchLink
                url={`https://app.clio.com/nc/#/search?query=${defendant.firstName} ${defendant.lastName}`}
                searchName="Clio"
                className="btn btn-sm btn-ghost text-primary"
                iconClassName="h-[18px] w-[18px]"
              />
            </div>
          </div>
        ))}
    </ChecklistItemDefault>
  );
};
