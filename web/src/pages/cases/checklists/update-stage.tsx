import { STAGES, STAGE_DEFINITIONS } from "@shared/constants";
import { StaffSelect } from "@web/src/components/forms/selects";
import { useState } from "react";
import { Button } from "react-daisyui";
import { useParams } from "react-router-dom";
import { useCase, useUpdateCase } from "../hooks";

export default function UpdateStage() {
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);
  const { update: updateCase } = useUpdateCase(caseData!);

  const getNextStatusAndStage = (
    currentStage: string
  ): {
    LABEL: string;
    STATUS: string;
    STAGE: string;
    NEXT: any;
  } => {
    const currentStatusAndStage = STAGE_DEFINITIONS[currentStage.toUpperCase()];
    switch (currentStage) {
      case STAGES.PREP:
        return STAGE_DEFINITIONS.PREP;
      case STAGES.FILING:
        return STAGE_DEFINITIONS.FILING;
      default:
        return currentStatusAndStage;
    }
  };
  const STAGE = getNextStatusAndStage(caseData?.stage!);

  // In the following situations, default to unassigning staff when moving to the next status &/or stage
  const unassignStaff =
    STAGE.NEXT.STAGE === STAGES.FILING || STAGE.NEXT.STAGE === STAGES.FAPREP;
  const [assignee, setAssignee] = useState(
    unassignStaff ? null : caseData?.staff
  );
  const [assigneeUpdated, setAssigneeUpdated] = useState(false);
  if (!caseData) return null;

  const handleUpdate = async ({
    staff,
    nextStatus,
    nextStage,
  }: {
    staff: string | null | undefined;
    nextStatus: string;
    nextStage: string;
  }) => {
    await updateCase({
      staff: staff,
      status: nextStatus,
      stage: nextStage,
    });
  };

  return (
    <>
      <div className="flex flex-col border-slate-400 border-t bg-base-400 pt-3">
        {STAGE.NEXT.STAGE !== STAGES.FAPREP && (
          <>
            <span className="font-bold mb-2 text-lg">
              {STAGE.LABEL} stage complete.
            </span>
            <span>Assign {STAGE.NEXT.LABEL} stage to:</span>

            <StaffSelect
              value={assigneeUpdated ? assignee : null}
              placeholder={
                unassignStaff ? "Unassigned (default)" : "Select staff..."
              }
              onChange={(val) => {
                setAssigneeUpdated(true);
                setAssignee(val);
              }}
            />
          </>
        )}
        <Button
          className="justify-center bg-base-100 hover:bg-green-100 flex-nowrap mt-2 mb-2"
          onClick={() => {
            handleUpdate({
              staff: assignee,
              nextStatus: STAGE.NEXT.STATUS,
              nextStage: STAGE.NEXT.STAGE,
            });
          }}
        >
          <span>
            {STAGE.NEXT.STAGE === STAGES.FAPREP
              ? `Mark ${STAGE.STAGE} stage complete.`
              : `Click to move to ${STAGE.NEXT.LABEL} stage.`}
          </span>
        </Button>
      </div>
    </>
  );
}
