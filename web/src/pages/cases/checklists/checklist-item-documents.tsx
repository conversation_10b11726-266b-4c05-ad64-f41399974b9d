import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { ChecklistItemDefault } from "./checklist-item-default";
import { ChecklistNavLink } from "./checklist-links";

export const ChecklistItemDocuments = ({ item }) => {
  return (
    <ChecklistItemDefault item={item}>
      <ChecklistNavLink to={`templates`}>
        View Templates
        <ArrowTopRightOnSquareIcon className="h-[18px] w-[18px] inline ml-2" />
      </ChecklistNavLink>
    </ChecklistItemDefault>
  );
};
