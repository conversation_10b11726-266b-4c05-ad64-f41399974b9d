import { ATTORNEY_REVIEW_OPTIONS, CASE_STATUSES } from "@shared/constants";
import { Button } from "@web/src/components/buttons";
import { useConfirmation } from "@web/src/components/confirmation";
import {
  AttorneySelect,
  SimpleSelect,
} from "@web/src/components/forms/selects";
import { useSSO } from "@web/src/lib/hooks";
import { useCreateComment } from "@web/src/pages/intake/hooks";
import { useParams } from "react-router-dom";
import { ClioIcon } from "../../intake-requests/components";
import { useCase, useUpdateCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";
import { ChecklistLink } from "./checklist-links";

export const ChecklistItemAttorney = ({ item }) => {
  const { user } = useSSO();
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);
  const { update: updateCase } = useUpdateCase(caseData!);
  const { createComment } = useCreateComment();
  const { confirm } = useConfirmation();

  return (
    <ChecklistItemDefault item={item}>
      {item.description === "Assign Attorney" && (
        <AttorneySelect
          value={caseData?.attorney}
          onChange={(value) => {
            createComment({
              caseId,
              staffId: user?.id,
              description: value
                ? `Attorney assigned: ${value}`
                : "Attorney unassigned",
              type: "auto",
            });
            updateCase({
              attorney: value,
            });
          }}
        />
      )}
      {item.description === "Bill for Attorney Review" &&
        caseData?.clioMatterId && (
          <ChecklistLink
            to={`https://app.clio.com/nc/#/matters/${caseData.clioMatter?.clioId}/activities`}
          >
            <ClioIcon height={18} width={18} className="inline mr-2" />
            View Clio Activities
          </ChecklistLink>
        )}
      {item.description === "Task File to Attorney" &&
        caseData?.clioMatterId && (
          <ChecklistLink to={`https://app.clio.com/nc/#/tasks`}>
            <ClioIcon height={18} width={18} className="inline mr-2" />
            Set Clio Task
          </ChecklistLink>
        )}
      {item.description === "Confirm Attorney Review" && (
        <>
          <SimpleSelect
            paramKey="review"
            placeholder="Review Status"
            options={ATTORNEY_REVIEW_OPTIONS}
            value={caseData?.review}
            onChange={(value) => {
              const updateData = {
                review: value,
                canceled: caseData?.canceled,
                status: caseData?.status,
              };
              switch (value) {
                case "Approved":
                  updateData.status = CASE_STATUSES.INTAKE;
                  updateData.canceled = false;
                  break;
                case "Rejected":
                  break;
                case "Advising":
                  updateData.status = CASE_STATUSES.PENDING;
                  break;
                default:
                  updateData.status = caseData?.status;
                  break;
              }
              createComment({
                caseId,
                staffId: user?.id,
                description: value
                  ? `Attorney review status: ${value}`
                  : "Attorney review status removed",
                type: "auto",
              });
              updateCase(updateData).catch((error) =>
                console.log(`-------------error--------------`, error)
              );
            }}
            isClearable
            className="mb-3"
          />
          {caseData?.review === "Rejected" &&
            caseData?.status !== "Canceled" && (
              <Button
                children="Confirm Attorney Rejection"
                className="btn btn-error"
                onClick={() =>
                  confirm({
                    headerText: "Cancel Intake Request",
                    message: "Do you want to cancel this intake?",
                    confirmationText: "Yes, cancel intake",
                    confirmationColor: "error",
                    cancelText: "No, continue intake",
                    callback: () => {
                      updateCase({
                        canceled: true,
                        status: CASE_STATUSES.CANCELED,
                      }).catch((error) =>
                        console.log(`-------------error--------------`, error)
                      );
                    },
                  })
                }
              />
            )}
        </>
      )}
    </ChecklistItemDefault>
  );
};
