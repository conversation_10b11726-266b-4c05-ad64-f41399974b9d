import { EMAIL_TEMPLATE_TYPES } from "@shared/constants";
import { EmailButton } from "@web/src/components/emails";
import { ChecklistItemDefault } from "./checklist-item-default";

export const ChecklistItemServiceEmail = ({ item }) => {
  return (
    <ChecklistItemDefault item={item}>
      <EmailButton
        templateType={EMAIL_TEMPLATE_TYPES.SUMMONS_AND_COMPLAINT}
        caseId={item.caseId}
      />
    </ChecklistItemDefault>
  );
};
