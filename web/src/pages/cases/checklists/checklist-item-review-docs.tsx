import { ClioIcon } from "../../intake-requests/components";
import { useCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";
import { ChecklistLink } from "./checklist-links";

export const ChecklistItemReviewDocs = ({ item }) => {
  const { caseData } = useCase(item.caseId);

  return (
    <ChecklistItemDefault item={item}>
      {caseData?.clioMatterId && (
        <ChecklistLink
          to={`https://app.clio.com/nc/#/matters/${caseData.clioMatter?.clioId}/documents`}
        >
          <ClioIcon height={18} width={18} className="inline mr-2" />
          Review Documents
        </ChecklistLink>
      )}
    </ChecklistItemDefault>
  );
};
