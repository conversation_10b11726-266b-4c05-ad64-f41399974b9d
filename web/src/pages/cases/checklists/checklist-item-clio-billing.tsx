import { ClioIcon } from "../../intake-requests/components";
import { useCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";

export const ChecklistItemClioBilling = ({ item }) => {
  const { caseData } = useCase(item.caseId);

  return (
    <ChecklistItemDefault item={item}>
      {caseData?.clioMatterId && (
        <a
          href={`https://app.clio.com/nc/#/matters/${caseData.clioMatter?.clioId}/activities`}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-2 hover:bg-slate-100 rounded"
        >
          <ClioIcon height={18} width={18} />
          View Clio Activities
        </a>
      )}
    </ChecklistItemDefault>
  );
};
