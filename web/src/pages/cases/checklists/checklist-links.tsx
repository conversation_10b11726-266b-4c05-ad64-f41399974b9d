import { Link, NavLink } from "react-router-dom";

export const ChecklistLinkWrapper = ({ children }) => {
  return <div className="w-full max-w-1/3 whitespace-nowrap">{children}</div>;
};

export const ChecklistLink = ({ to, children }) => {
  return (
    <ChecklistLinkWrapper>
      <Link to={to} target="_blank" rel="noopener noreferrer">
        {children}
      </Link>
    </ChecklistLinkWrapper>
  );
};

export const ChecklistNavLink = ({ to, children }) => {
  return (
    <ChecklistLinkWrapper>
      <NavLink to={to}>{children}</NavLink>
    </ChecklistLinkWrapper>
  );
};
