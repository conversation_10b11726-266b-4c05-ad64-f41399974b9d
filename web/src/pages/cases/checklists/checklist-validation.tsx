import { CHECKLIST_ITEM_TYPES } from "@shared/constants";
import { useEfile } from "@web/src/lib/hooks";
import { useMemo } from "react";
import { useDefendant } from "../../defendants/hooks";
import { useComments } from "../../intake/hooks";
import { useCase } from "../hooks";

/**
 * Hook to validate if a checklist item has all required data to be marked complete
 */
export const useChecklistItemValidation = (item: any): boolean => {
  const { caseData } = useCase(item.caseId);
  const { defendant } = useDefendant(String(caseData?.defendant?.id));
  const { efilings } = useEfile({ caseId: caseData?.id });
  const efile = efilings[0];
  const { comments } = useComments({
    caseId: item.caseId,
    type: "manual",
    workflowCheck: "prep",
  });
  const workflowComment = comments?.[0];

  return useMemo(() => {
    if (!item || !caseData) return false;

    // If item is already complete, allow unchecking
    if (item.complete) return true;

    switch (item.type) {
      case CHECKLIST_ITEM_TYPES.USPS:
        return validateUSPSItem(defendant);

      case CHECKLIST_ITEM_TYPES.ATTORNEY:
        return validateAttorneyItem(item, caseData);

      case CHECKLIST_ITEM_TYPES.CASE_INFO:
        return validateCaseInfoItem(item, caseData);

      case CHECKLIST_ITEM_TYPES.EFILE:
        return validateEfileItem(item, caseData, efile);

      case CHECKLIST_ITEM_TYPES.COMMENT:
        return validateCommentItem(!!workflowComment);

      case CHECKLIST_ITEM_TYPES.FED:
      case CHECKLIST_ITEM_TYPES.REVIEW_DOCS:
      case CHECKLIST_ITEM_TYPES.DOCUMENTS:
      case CHECKLIST_ITEM_TYPES.SERVICE:
      case CHECKLIST_ITEM_TYPES.DEFAULT:
      case CHECKLIST_ITEM_TYPES.FAPREP:
        // These types don't require specific data validation
        return true;

      default:
        return true;
    }
  }, [item, caseData, defendant, efile, workflowComment]);
};

/**
 * Validate USPS checklist items - require complete address
 */
const validateUSPSItem = (defendant: any): boolean => {
  if (!defendant) return false;

  return !!(
    defendant.address1?.trim() &&
    defendant.city?.trim() &&
    defendant.state?.trim() &&
    defendant.zip?.trim()
  );
};

/**
 * Validate Attorney checklist items based on description
 */
const validateAttorneyItem = (item: any, caseData: any): boolean => {
  switch (item.description) {
    case "Assign Attorney":
      return !!caseData.attorney;

    case "Confirm Attorney Review":
      return !!caseData.review;

    case "Bill for Attorney Review":
    case "Task File to Attorney":
      return !!(caseData.attorney && caseData.clioMatterId);

    default:
      return true;
  }
};

/**
 * Validate Case Info checklist items based on description
 */
const validateCaseInfoItem = (item: any, caseData: any): boolean => {
  switch (item.description) {
    case "Record FA and Case #":
      return !!(caseData.caseDates?.dateFA && caseData.caseNumber?.trim());
    default:
      return true;
  }
};

/**
 * Validate Efile checklist items
 */
const validateEfileItem = (item: any, caseData: any, efile: any): boolean => {
  switch (item.description) {
    case "Efile":
      return efile?.status === "complete";

    case "Record Court's Acceptance or Rejection of Filing":
      return !!caseData.filingStatus;

    default:
      return true;
  }
};

/**
 * Validate Comment/Notes checklist items
 */
const validateCommentItem = (commentExists: boolean): boolean => {
  return commentExists;
};
