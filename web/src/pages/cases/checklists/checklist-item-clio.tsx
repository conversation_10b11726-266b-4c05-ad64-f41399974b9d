import { Link } from "react-router-dom";
import {
  ClioDocumentLink,
  ClioIcon,
  CognitoDocumentLink,
} from "../../intake-requests/components";
import { useIntakeRequest } from "../../intake-requests/hooks";
import { SearchLink } from "../components";
import { useCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";
export const ChecklistItemClio = ({ item }) => {
  const { caseData } = useCase(item.caseId);
  const { intakeRequest } = useIntakeRequest(
    caseData?.intakeFormResponseId?.toString()
  );

  return (
    <ChecklistItemDefault item={item}>
      {item.description === "Review Documents" && (
        <>
          <ClioDocumentLink
            intakeRequest={intakeRequest}
            description={
              intakeRequest?.clioFolderId
                ? "View in Clio"
                : "No Clio folder found"
            }
          />
          <CognitoDocumentLink
            intakeRequest={intakeRequest}
            description="View in Cognito"
          />
        </>
      )}
      {!caseData?.clioMatterId && item.description === "Conflict Check" && (
        <SearchLink
          url={`https://app.clio.com/nc/#/search?query=${caseData?.defendant?.getName()}`}
          searchName="Clio"
          className="flex items-center gap-2"
          iconClassName="h-[18px] w-[18px]"
        />
      )}
      {item.description === "Connect Clio" && (
        <>
          {!caseData?.clioMatterId && intakeRequest?.clioFolderId && (
            <Link
              to={`/intake/${caseData?.id}/create-clio-matter`}
              className="flex items-center gap-2"
              state={{
                ...caseData,
                type: "company",
              }}
            >
              <ClioIcon height={18} width={18} />
              Open Clio Form
            </Link>
          )}
          {!caseData?.clioMatterId && !intakeRequest?.clioFolderId && (
            <div className="flex items-center gap-2 opacity-50 cursor-not-allowed">
              <ClioIcon height={18} width={18} />
              No Clio folder to connect to
            </div>
          )}
          {caseData?.clioMatterId && (
            <a
              href={`https://app.clio.com/nc/#/matters/${caseData.clioMatter?.clioId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 hover:bg-slate-100 rounded"
            >
              <ClioIcon height={18} width={18} />
              View Clio Matter
            </a>
          )}
        </>
      )}
    </ChecklistItemDefault>
  );
};
