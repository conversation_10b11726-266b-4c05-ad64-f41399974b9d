import { FloatingPortal } from "@floating-ui/react";
import {
  ArrowTopRightOnSquareIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";
import { DebouncedTextInput } from "@web/src/components/utils";
import { useSSO } from "@web/src/lib/hooks";
import { useCreateComment } from "@web/src/pages/intake/hooks";
import dayjs from "dayjs";
import DatePicker from "react-datepicker";
import { useParams } from "react-router-dom";
import { useCase, useUpdateCase } from "../hooks";
import { ChecklistItemDefault } from "./checklist-item-default";
import { ChecklistNavLink } from "./checklist-links";

export const ChecklistItemCaseInfo = ({ item }) => {
  const { user } = useSSO();
  const { caseId } = useParams();
  const { caseData, refetch } = useCase(caseId);
  const { update: updateCase } = useUpdateCase(caseData!);
  const { createComment } = useCreateComment();

  const handleFADateChange = (value: Date | null) => {
    const timeString = value ? dayjs(value).format("h:mm A") : null;
    if (timeString !== "12:00 AM") {
      createComment({
        caseId,
        staffId: user?.id,
        description: value
          ? `FA date entered: ${dayjs(value).format("M/D/YYYY h:mm A")}`
          : "FA date removed",
        type: "auto",
      });
      updateCase({
        caseDates: {
          ...caseData?.caseDates,
          dateFA: value,
        },
      });
      refetch();
    }
  };

  return (
    <ChecklistItemDefault item={item}>
      {item.description === "Complete Case Info" && (
        <ChecklistNavLink to="edit/details">
          <span>Edit</span>
          <ArrowTopRightOnSquareIcon className="h-[18px] w-[18px] inline ml-2" />
        </ChecklistNavLink>
      )}

      {item.description === "Record FA and Case #" && (
        <>
          <label htmlFor="dateFa" className="flex flex-col">
            <span className="text-sm text-black">FA Date</span>
            <DatePicker
              name="dateFa"
              wrapperClassName="w-1/2"
              customInput={
                <input
                  type="text"
                  className="w-full border border-slate-300 rounded-md focus:outline-none focus:border-slate-500"
                  style={{ paddingLeft: "36px" }}
                />
              }
              selected={caseData?.caseDates?.dateFA}
              onChange={(value) => {
                handleFADateChange(value);
              }}
              dateFormat="MMMM d, yyyy h:mm aa"
              showIcon
              isClearable
              icon={<CalendarIcon style={{ width: 21, height: 21 }} />}
              todayButton="Today"
              timeIntervals={15}
              showTimeSelect={true}
              shouldCloseOnSelect={true}
              popperContainer={FloatingPortal}
            />
          </label>

          <DebouncedTextInput
            label="Case Number"
            name="caseData.caseNumber"
            placeholder="Case Number"
            value={caseData?.caseNumber || ""}
            fieldName="caseNumber" // This is the field name for the mutation data
            mutationFunction={updateCase}
            comment={{
              caseId,
              description: `Case number entered: `,
              type: "auto",
            }}
          />
        </>
      )}
    </ChecklistItemDefault>
  );
};
