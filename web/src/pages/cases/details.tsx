import { DocumentIcon, InboxArrowDownIcon } from "@heroicons/react/24/outline";
import { stringToHtml } from "@shared/utils";
import { EfileButton } from "@web/src/components/buttons";
import { ShortDate, ThemedLink } from "@web/src/components/content";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { ClickToReveal, Tooltip } from "@web/src/components/utils";
import { useEfile } from "@web/src/lib/hooks";
import { SearchLink } from "@web/src/pages/cases/components";
import { map } from "lodash";
import { Card } from "react-daisyui";
import { Link, NavLink, useParams } from "react-router-dom";
import {
  ClioDocumentLink,
  ClioIcon,
  CognitoDocumentLink,
} from "../intake-requests/components";
import { useIntakeRequest } from "../intake-requests/hooks";
import { useCase } from "./hooks";

export default function CaseDetails() {
  const { caseId } = useParams();
  const { caseData } = useCase(caseId);
  const { intakeRequest } = useIntakeRequest(
    caseData?.intakeFormResponseId?.toString()
  );
  const { efilings } = useEfile({ caseId: caseData?.id });
  const efile = efilings[0];
  const isResidential = caseData?.residentialCommercial === "Residential";
  const isOregon = caseData?.defendant?.state === "OR";
  const isWashington = caseData?.defendant?.state === "WA";

  const allDefendants = [
    caseData?.defendant,
    ...(caseData?.additionalDefendants || []),
  ];

  return (
    <div className="mt-4 text-sm">
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="text-lg font-normal flex justify-between items-center">
            Case Links
          </Card.Title>
          <MetadataTable>
            <MetadataItem
              title="Clio Matter"
              label={<ClioIcon height={24} width={24} />}
            >
              {caseData?.clioMatterId ? (
                <Link
                  to={`https://app.clio.com/nc/#/matters/${caseData.clioMatter?.clioId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:underline"
                >
                  View Clio Matter
                </Link>
              ) : (
                <span className="text-gray-400">No Connected Clio Matter</span>
              )}
            </MetadataItem>
            <MetadataItem
              title="View Supporting Documents"
              label={<DocumentIcon />}
            >
              <div className="flex items-center p-0 gap-5">
                <Tooltip
                  title={
                    intakeRequest?.formEntryId
                      ? "View in Cognito"
                      : "No Cognito form found"
                  }
                >
                  <CognitoDocumentLink
                    intakeRequest={intakeRequest}
                    description="View in Cognito"
                    className="px-2 btn-ghost btn btn-sm"
                  />
                </Tooltip>
                <Tooltip
                  title={
                    intakeRequest?.clioFolderId
                      ? "View documents in Clio"
                      : "No Clio folder found"
                  }
                >
                  <ClioDocumentLink
                    intakeRequest={intakeRequest}
                    description="View in Clio"
                    className="px-2 btn-ghost btn btn-sm"
                  />
                </Tooltip>
              </div>
            </MetadataItem>
            <MetadataItem
              title="Efiling"
              label={<InboxArrowDownIcon height={24} width={24} />}
            >
              {isOregon && (
                <div className="flex items-center gap-5">
                  {efile && efile.status === "complete" ? (
                    <ThemedLink
                      to={efile.eFileUrl}
                      color="primary"
                      target="_blank"
                    >
                      View Efile Draft
                    </ThemedLink>
                  ) : efile && efile.status === "processing" ? (
                    <>
                      <span className="text-gray-400">
                        Efile {efile.status}
                      </span>
                      <span className="loading loading-infinity"></span>
                    </>
                  ) : (
                    <span className="text-gray-400">Not yet filed</span>
                  )}
                  {!efile || efile.status !== "processing" ? (
                    <EfileButton className="btn-xs" caseId={caseData.id} />
                  ) : null}
                </div>
              )}
            </MetadataItem>
          </MetadataTable>
        </Card.Body>
      </Card>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="font-normal text-lg flex justify-between items-center">
            Issues Notes
            <NavLink
              to={`/cases/${caseId}/edit/notes`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </NavLink>
          </Card.Title>
          {caseData?.issueSummarySheet && (
            <p
              className="text-sm whitespace-pre-line"
              dangerouslySetInnerHTML={{
                __html: stringToHtml(caseData?.issueSummarySheet!),
              }}
            />
          )}
        </Card.Body>
      </Card>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="text-lg font-normal flex justify-between items-center">
            Parties
            <NavLink
              to={`/cases/${caseId}/edit/client`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </NavLink>
          </Card.Title>
          <MetadataTable>
            <MetadataItem compact label="Plaintiffs">
              {caseData?.plaintiffs}
            </MetadataItem>
            <MetadataItem compact label="Pickup">
              {caseData?.isPickupCase ? "Yes" : "No"}
            </MetadataItem>
            <MetadataItem compact label="Client Contact">
              {caseData?.clientContact}
            </MetadataItem>
            <MetadataItem compact label="Property Contact">
              {caseData?.propertyContact}
            </MetadataItem>

            <MetadataItem compact label="Defendants">
              {map(allDefendants, (defendant, index) => (
                <div
                  key={index}
                  className="border-l-primary border-l-2 pl-2 mb-2 last:mb-0"
                >
                  <div className="flex items-center">
                    {defendant?.firstName} {defendant?.middleName}{" "}
                    {defendant?.lastName} {defendant?.suffix}
                    <SearchLink
                      url={`https://app.clio.com/nc/#/search?query=${defendant?.firstName} ${defendant?.lastName}`}
                      searchName="Clio"
                      className="btn btn-sm btn-ghost text-primary ml-2"
                      iconClassName="h-[18px] w-[18px]"
                    />
                  </div>
                  {defendant?.ssn && (
                    <ClickToReveal
                      title="Click to reveal SSN"
                      secureKey={defendant?.ssn}
                    />
                  )}
                </div>
              ))}
            </MetadataItem>
          </MetadataTable>
        </Card.Body>
      </Card>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="text-lg font-normal flex justify-between items-center">
            Details
            <NavLink
              to={`/cases/${caseId}/edit/details`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </NavLink>
          </Card.Title>
          <MetadataTable>
            <MetadataItem compact label="Case Number">
              {caseData?.caseNumber}
            </MetadataItem>
            <MetadataItem compact label="Status">
              {caseData?.status}
            </MetadataItem>
            <MetadataItem compact label="Attorney">
              {caseData?.attorney}
            </MetadataItem>
            <MetadataItem compact label="Staff">
              {caseData?.staff}
            </MetadataItem>
            <MetadataItem compact label="Property Type">
              {caseData?.residentialCommercial}
            </MetadataItem>
            <MetadataItem compact label="Court Room #">
              {caseData?.courtroomNumber}
            </MetadataItem>
            <MetadataItem compact label="Lease Type">
              {caseData?.leaseType}
            </MetadataItem>
            <MetadataItem compact label="Military Notes">
              {caseData?.militaryAffidavitNotes}
            </MetadataItem>
            <MetadataItem compact label="Restitution">
              {caseData?.limitedOrGeneralJudgment}
            </MetadataItem>
            <MetadataItem compact label="Stipulation">
              {caseData?.orderOrJudgment}
            </MetadataItem>
            <MetadataItem compact label="Stipulation Date">
              <ShortDate date={caseData?.caseDates?.stipulationAgreementDate} />
            </MetadataItem>
            <MetadataItem compact label="Noncompliance">
              {caseData?.noncomplianceDesc}
            </MetadataItem>
            <MetadataItem compact label="Opposing Counsel">
              {caseData?.opposingCounsel && (
                <>
                  {caseData?.opposingCounsel.name}
                  {" - "}
                  {caseData?.opposingCounsel.barNumber}
                </>
              )}
            </MetadataItem>
          </MetadataTable>
        </Card.Body>
      </Card>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="text-lg font-normal flex justify-between items-center">
            Notices
            <NavLink
              to={`/cases/${caseId}/edit/notices`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </NavLink>
          </Card.Title>
          <MetadataTable>
            <MetadataItem compact label="Notices">
              {caseData?.getNotices().map((notice, index) => (
                <div key={index}>{notice}</div>
              ))}
            </MetadataItem>
            <MetadataItem compact label="Other Notice">
              {isResidential
                ? caseData?.resNoticesOR?.otherExplanation
                : caseData?.commNoticesOR?.otherExplanation}
            </MetadataItem>
            <MetadataItem compact label="No Notice">
              {isResidential
                ? caseData?.resNoticesOR?.noNoticeExplanation
                : caseData?.commNoticesOR?.noNoticeExplanation}
            </MetadataItem>
          </MetadataTable>
        </Card.Body>
      </Card>
      <Card className="bg-slate-100 mb-3">
        <Card.Body className="px-4 py-2">
          <Card.Title className="text-lg font-normal flex justify-between items-center">
            Dates
            <NavLink
              to={`/cases/${caseId}/edit/dates`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </NavLink>
          </Card.Title>
          <MetadataTable>
            {isOregon && (
              <>
                <MetadataItem compact label="FA">
                  <ShortDate date={caseData.caseDates?.dateFA} />
                </MetadataItem>
                <MetadataItem compact label="FA Reset">
                  <ShortDate date={caseData.caseDates?.resetFA} />
                </MetadataItem>
                <MetadataItem compact label="Ex Parte">
                  <ShortDate date={caseData.caseDates?.exParte} />
                </MetadataItem>
                <MetadataItem compact label="Hearing">
                  <ShortDate date={caseData.caseDates?.hearing} />
                </MetadataItem>
                <MetadataItem compact label="NOR">
                  <ShortDate date={caseData.caseDates?.nOfR} />
                </MetadataItem>
                <MetadataItem compact label="Trial">
                  <ShortDate date={caseData.caseDates?.trialDate} />
                </MetadataItem>
                <MetadataItem compact label="Writ">
                  <ShortDate date={caseData.caseDates?.writDate} />
                </MetadataItem>
                <MetadataItem compact label="Move Out">
                  <ShortDate date={caseData.caseDates?.moveoutDate} />
                </MetadataItem>
                <MetadataItem compact label="Call">
                  <ShortDate date={caseData.caseDates?.callDate} />
                </MetadataItem>
              </>
            )}
            {isWashington && (
              <>
                <MetadataItem compact label="Notice Served">
                  <ShortDate date={caseData.caseDates?.noticeServedDate} />
                </MetadataItem>
                <MetadataItem compact label="Notice Service Type">
                  {caseData.caseDates?.noticeServedType}
                </MetadataItem>
                <MetadataItem compact label="Notice Expires">
                  <ShortDate date={caseData.caseDates?.noticeExpiresDate} />
                </MetadataItem>
                <MetadataItem compact label="Tenant NOA Submission">
                  <ShortDate date={caseData.caseDates?.tenantNoaSubmission} />
                </MetadataItem>
                <MetadataItem compact label="OC NOA Submission">
                  <ShortDate date={caseData.caseDates?.ocNoaSubmission} />
                </MetadataItem>
                <MetadataItem compact label="Lease Signed">
                  <ShortDate date={caseData.caseDates?.leaseDate} />
                </MetadataItem>
                <MetadataItem compact label="Default Sent for Service">
                  <ShortDate date={caseData.caseDates?.defaultSentForService} />
                </MetadataItem>
                <MetadataItem compact label="Writ Issued">
                  <ShortDate date={caseData.caseDates?.writDate} />
                </MetadataItem>
                <MetadataItem compact label="Show Cause Hearing">
                  <ShortDate date={caseData.caseDates?.showCauseHearing} />
                </MetadataItem>
                <MetadataItem compact label="S&C Sent to Process Server">
                  <ShortDate date={caseData.caseDates?.sAndCToProcessServer} />
                </MetadataItem>
                <MetadataItem compact label="Show Cause Setover">
                  <ShortDate date={caseData.caseDates?.showCauseSetover} />
                </MetadataItem>
                <MetadataItem compact label="Service Deadline">
                  <ShortDate date={caseData.caseDates?.serviceDeadline} />
                </MetadataItem>
                <MetadataItem compact label="Strike Filed">
                  <ShortDate date={caseData.caseDates?.dismissed} />
                </MetadataItem>
                <MetadataItem compact label="S&C Service">
                  <ShortDate date={caseData.caseDates?.sAndCService} />
                </MetadataItem>
                <MetadataItem compact label="S&C Service Type">
                  {caseData.caseDates?.sAndCServiceType}
                </MetadataItem>
                <MetadataItem compact label="S&C Response Deadline">
                  <ShortDate date={caseData.caseDates?.sAndCresponseDeadline} />
                </MetadataItem>
                <MetadataItem compact label="Lockout">
                  <ShortDate date={caseData.caseDates?.lockout} />
                </MetadataItem>
              </>
            )}
          </MetadataTable>
        </Card.Body>
      </Card>
    </div>
  );
}
