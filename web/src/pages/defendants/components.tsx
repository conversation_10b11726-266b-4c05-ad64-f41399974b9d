import { ThemedLink } from "@web/src/components/content";
import { Card } from "react-daisyui";

export function DefendantLink({ defendant }) {
  return (
    <ThemedLink
      to={`/defendants/${defendant.id}`}
      color="secondary"
      className="font-semibold"
    >
      <div>
        {defendant.getName()}
        <div className="text-sm text-gray-400">
          {defendant.address1} {defendant.address2}
          <br />
          {defendant.city}, {defendant.state} {defendant.zip}
        </div>
      </div>
    </ThemedLink>
  );
}

export function DefendantCard({ defendant }) {
  return (
    <Card className="bg-slate-100 mb-3">
      <Card.Body className="px-4 py-2">
        <DefendantLink defendant={defendant} />
      </Card.Body>
    </Card>
  );
}
