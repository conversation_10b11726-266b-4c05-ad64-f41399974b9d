import { Defendant } from "@shared/db/schema/defendants";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useUpdateDefendant = (
  defendant: Defendant,
  options: object = {}
) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) =>
      axios.put(`/api/defendants/${defendant.id}`, changes),
    mutationKey: ["defendants", defendant?.id],
    onSettled: () =>
      queryClient.invalidateQueries({ queryKey: ["defendants"] }),
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useCreateDefendant = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (defendant: any) => {
      const response = await axios.post("/api/defendants", defendant);
      return response.data;
    },
    mutationKey: ["defendants", "create"],
    onSettled: () =>
      queryClient.invalidateQueries({ queryKey: ["defendants"] }),
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useDefendants = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["defendants", params],
    queryFn: async (): Promise<{ defendants: any; count: number }> => {
      const response = await axios.get("/api/defendants", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const defendants: Defendant[] = useMemo(() => {
    if (!data?.defendants) return null;
    return data.defendants.map((defendant) => new Defendant(defendant));
  }, [data?.defendants]);

  return {
    count: data?.count,
    defendants,
    ...passedProps,
  };
};

export const useDefendant = (
  id: string | undefined,
  params: object = {},
  options: object = {}
) => {
  const { data: defendantRaw, ...passedProps } = useQuery({
    queryKey: ["defendants", id, params],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/defendants/${id}`, { params });
      return response.data;
    },
    enabled: Boolean(id),
    ...options,
  });

  const defendant = useMemo(() => {
    if (!defendantRaw) return null;
    return new Defendant(defendantRaw);
  }, [defendantRaw]);

  return {
    defendant,
    ...passedProps,
  };
};
