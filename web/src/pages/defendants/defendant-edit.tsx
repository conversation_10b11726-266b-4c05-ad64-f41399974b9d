import { Button } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON><PERSON>er,
  FormW<PERSON>per,
  HookF<PERSON>,
} from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import DefendantForm from "./defendant-form";
import { useDefendant, useUpdateDefendant } from "./hooks";

export default function DefendantEdit() {
  const { defendantId } = useParams();
  const { defendant } = useDefendant(
    defendantId,
    { decrypt: true },
    { gcTime: 0 } // RE: gcTime, see NOTES section in README
  );
  const navigate = useNavigate();
  const { update: updateDefendant, isPending } = useUpdateDefendant(defendant!);

  if (!defendant) return null;

  const defaultValues = {
    propertyId: defendant.propertyId || null,
    firstName: defendant.firstName || "",
    middleName: defendant.middleName || "",
    lastName: defendant.lastName || "",
    suffix: defendant.suffix || "",
    ssn: defendant.ssn || "",
    dob: defendant.dob || "",
    isBusiness: defendant.isBusiness || false,
    businessName: defendant.businessName || "",
    phone: defendant.phone || "",

    address1: defendant.address1 || "",
    address2: defendant.address2 || "",
    city: defendant.city || "",
    state: defendant.state || "",
    zip: defendant.zip || "",

    useMailingAddress: defendant.useMailingAddress || false,
    mailing: {
      line1: defendant.mailing?.line1 || "",
      line2: defendant.mailing?.line2 || "",
      city: defendant.mailing?.city || "",
      state: defendant.mailing?.state || "",
      zip: defendant.mailing?.zip || "",
    },
  };

  const onSubmit = async (data) => {
    try {
      await updateDefendant(data);
      navigate(`/defendants/${defendantId}`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <FormWrapper>
      <FormHeader>
        <h2 className="text-xl">Edit Defendant: {defendant.getName()}</h2>
      </FormHeader>
      <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
        <DefendantForm>
          <FormButtons>
            <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button
              className="btn-primary btn-sm"
              disabled={isPending}
              type="submit"
            >
              Update Defendant
            </Button>
          </FormButtons>
        </DefendantForm>
      </HookForm>
    </FormWrapper>
  );
}
