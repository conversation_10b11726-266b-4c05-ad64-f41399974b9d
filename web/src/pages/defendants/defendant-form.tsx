import {
  HookCitySelect,
  <PERSON><PERSON>atePicker,
  <PERSON>Input,
  HookLimitedStateSelect,
  HookPropertySelect,
  HookSecureInput,
  HookSettingSelect,
  HookStateSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useFormContext } from "react-hook-form";

export default function DefendantForm({
  children: buttons,
  prefix = "",
}: {
  children?: any;
  prefix?: string;
}) {
  const getName = (name) => (prefix ? `${prefix}.${name}` : name);
  const formContext = useFormContext();
  const [isBusiness, useMailingAddress, state] = formContext.watch([
    getName("isBusiness"),
    getName("useMailingAddress"),
    getName("state"),
  ]);

  return (
    <>
      {!prefix && (
        <HookPropertySelect
          label="Property"
          name="propertyId"
          rules={{ required: "Property is required" }}
        />
      )}
      <HookToggle
        name={getName("isBusiness")}
        label="Defendant is a business"
        color="success"
      />

      {isBusiness ? (
        <div>
          <HookInput
            name={getName("businessName")}
            label="Business Name"
            placeholder="Business Name"
            rules={{ required: "Business name is required" }}
          />
        </div>
      ) : (
        <div>
          <div className="label">
            <span className="label-text">Name</span>
          </div>

          <div className="flex gap-3 items-start">
            <HookInput
              name={getName("firstName")}
              placeholder="First"
              rules={{ required: "Required" }}
            />
            <HookInput name={getName("middleName")} placeholder="Middle" />
            <HookInput
              name={getName("lastName")}
              placeholder="Last"
              rules={{ required: "Required" }}
            />
            <div style={{ minWidth: 100, width: 100 }}>
              <HookSettingSelect
                setting="name_suffixes"
                name={getName("suffix")}
                placeholder="Suffix"
              />
            </div>
          </div>

          <HookSecureInput
            name={getName("ssn")}
            label="SSN"
            placeholder="No SSN"
          />
          <HookSecureInput
            name={getName("dob")}
            InputComponent={HookDatePicker}
            label="DOB"
            placeholder="No DOB"
            showYearDropdown
            yearDropdownItemNumber={120}
            scrollableYearDropdown
            maxDate={new Date()}
            returnFormattedDate={true}
          />
        </div>
      )}

      <HookInput name={getName("phone")} label="Phone" placeholder="Phone" />

      <HookInput
        name={getName("address1")}
        label="Address"
        placeholder="Address Line 1"
        rules={{ required: "Address is required" }}
      />
      <HookInput name={getName("address2")} placeholder="Address Line 2" />

      <div className="flex gap-3 items-start">
        <HookCitySelect
          state={state}
          name={getName("city")}
          placeholder="City"
          create
          rules={{ required: "City is required" }}
        />
        <HookLimitedStateSelect
          name={getName("state")}
          placeholder="State"
          rules={{ required: "State is required" }}
        />
        <HookInput
          name={getName("zip")}
          placeholder="Zip"
          rules={{ required: "Zip is required" }}
        />
      </div>

      <HookToggle
        name={getName("useMailingAddress")}
        label="Use Mailing Address"
        color="success"
      />

      {useMailingAddress && (
        <div>
          <div className="label">
            <span className="label-text">Mailing Address</span>
          </div>
          <HookInput
            name={getName("mailing.line1")}
            placeholder="Mailing Address Line 1"
          />
          <HookInput
            name={getName("mailing.line2")}
            placeholder="Mailing Address Line 2"
          />
          <div className="flex gap-3 items-start">
            <HookInput name={getName("mailing.city")} placeholder="City" />
            <HookStateSelect
              name={getName("mailing.state")}
              placeholder="State"
            />
            <HookInput name={getName("mailing.zip")} placeholder="Zip" />
          </div>
        </div>
      )}

      {buttons}
    </>
  );
}
