import {
  BuildingOffice2Icon,
  CalendarDaysIcon,
  EnvelopeIcon,
  LockClosedIcon,
  MapPinIcon,
  PhoneIcon,
} from "@heroicons/react/24/outline";
import { ShortDate, ThemedLink } from "@web/src/components/content";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { ClickToReveal } from "@web/src/components/utils";
import { useBasePath } from "@web/src/lib/hooks";
import classNames from "classnames";
import dayjs from "dayjs";
import { Link } from "react-router-dom";

export default function DefendantInfo({ defendant }) {
  const basePath = useBasePath("defendant");

  return (
    <>
      <h1 className="text-xl mb-2 flex justify-between items-top">
        {defendant.getName()}
        <Link to={`${basePath}/edit`} className="btn btn-sm btn-primary">
          Edit
        </Link>
      </h1>
      <MetadataTable>
        <MetadataItem label={<BuildingOffice2Icon height={24} width={24} />}>
          <ThemedLink
            to={`/properties/${defendant.property.id}`}
            color="accent"
          >
            <span
              className={classNames({
                "text-error": defendant.property.doNotUse,
              })}
            >
              {defendant.property.name}
            </span>
          </ThemedLink>
        </MetadataItem>
        <MetadataItem label={<MapPinIcon height={24} width={24} />}>
          <a
            href={`https://maps.google.com/?q=${defendant.getAddressForUrl()}`}
            className="text-sm inline-block hover:underline"
            target="_blank"
          >
            {defendant.address1}
            {defendant.address2 && <>, {defendant.address2}</>}
            <br />
            {defendant.city}, {defendant.state} {defendant.zip}
          </a>
        </MetadataItem>
        {defendant.useMailingAddress && (
          <MetadataItem label={<EnvelopeIcon height={24} width={24} />}>
            <a
              href={`https://maps.google.com/?q=${defendant.getMailingAddressForUrl()}`}
              className="text-sm inline-block hover:underline"
              target="_blank"
            >
              {defendant.mailing.line1}
              {defendant.mailing.line2 && <>, {defendant.mailing.line2}</>}
              <br />
              {defendant.mailing.city}, {defendant.mailing.state}{" "}
              {defendant.mailing.zip}
            </a>
          </MetadataItem>
        )}
        <MetadataItem
          title="Phone"
          label={<PhoneIcon height={24} width={24} />}
        >
          <a
            href={`tel:${defendant.phone}`}
            className="hover:underline"
            target="_blank"
          >
            {defendant.phone}
          </a>
        </MetadataItem>
        <MetadataItem
          title="SSN"
          label={<LockClosedIcon height={24} width={24} />}
        >
          <ClickToReveal secureKey={defendant.ssn} />
        </MetadataItem>
        <MetadataItem
          title="DOB"
          label={<CalendarDaysIcon height={24} width={24} />}
        >
          <ClickToReveal
            secureKey={defendant.dob}
            formatter={(dob) => <ShortDate date={dob} shortFormat={"MM/DD/YYYY"} />}
            copyFormatter={(dob) => dayjs(dob).format("MM/DD/YYYY")}
          />
        </MetadataItem>
      </MetadataTable>
    </>
  );
}
