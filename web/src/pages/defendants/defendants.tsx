import { ThemedLink } from "@web/src/components/content";
import {
  PropertySelectFilter,
  TextFilter,
} from "@web/src/components/forms/filters";
import { CaseLabelSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useUrlQuery } from "@web/src/lib/hooks";
import { times } from "lodash";
import { Link } from "react-router-dom";
import { useDefendants } from "./hooks";

export default function AllDefendants() {
  const { params } = useUrlQuery();
  const { defendants, count, isFetching } = useDefendants(params);
  const { Table, SelectableTD, SelectableTH, Pager } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: count,
    data: defendants,
  });

  if (!defendants) return null;

  return (
    <div className="px-4 pb-4">
      <div className="navbar sticky top-0 z-20 bg-base-100 flex items-center justify-between border-slate-300 border-b">
        <div className="flex">
          <h1 className="text-3xl font-light pl-3 pr-8">Defendants</h1>
          <div className="flex-grow flex">
            <div className="w-64">
              <TextFilter paramKey="search" placeholder="Search" autoFocus />
            </div>
            <div className="w-64 ml-3">
              <PropertySelectFilter paramKey="propertyId" />
            </div>
          </div>
        </div>
        <div>
          <Link to="/defendants/create" className="btn btn-primary">
            Add Defendant
          </Link>
        </div>
      </div>

      <Table className="table table-sm table-pin-rows">
        <thead>
          <tr className="bg-base-100" style={{ top: "4rem" }}>
            <SelectableTH className="pt-4" />
            <th className="pt-4">Name</th>
            <th>Address</th>
          </tr>
        </thead>
        <tbody>
          {defendants.length > 0 &&
            defendants.map((defendant, index) => {
              return (
                <tr key={defendant.id}>
                  <SelectableTD value={defendant.id} index={index} />
                  <td>
                    <ThemedLink
                      to={`/defendants/${defendant.id}`}
                      color="secondary"
                      className="font-semibold"
                    >
                      {defendant.getName()}
                    </ThemedLink>
                  </td>
                  <td>
                    <span>
                      {defendant.address1} {defendant.address2}
                      <br />
                      {defendant.city}, {defendant.state} {defendant.zip}
                    </span>
                  </td>
                </tr>
              );
            })}

          {isFetching &&
            defendants.length == 0 &&
            times(30, (index) => (
              <tr>
                <td></td>
                <td>
                  <CaseLabelSkeleton />
                </td>
                <td></td>
              </tr>
            ))}
        </tbody>
      </Table>
      <Pager className="mt-8" showCount />
    </div>
  );
}
