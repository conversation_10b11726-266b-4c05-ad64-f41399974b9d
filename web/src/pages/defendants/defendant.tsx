import { Column, Columns } from "@web/src/components/layout";
import { useBasePath } from "@web/src/lib/hooks";
import { Link, useMatch, useParams } from "react-router-dom";
import { CaseCard } from "../cases/components";
import { useCases } from "../cases/hooks";
import DefendantEdit from "./defendant-edit";
import DefendantInfo from "./defendant-info";
import { useDefendant } from "./hooks";

export default function DefendantView() {
  const { defendantId } = useParams();
  const { defendant } = useDefendant(defendantId);
  const basePath = useBasePath("defendant");
  const isEditingDefendant = useMatch(`${basePath}/edit`);
  const { cases, count: caseCount } = useCases({
    defendantId,
    orderByStatus: true,
  });

  if (!defendant) return null;

  return (
    <Columns>
      <Column>
        <div className="w-full">
          {isEditingDefendant && <DefendantEdit />}
          {!isEditingDefendant && <DefendantInfo defendant={defendant} />}
        </div>
      </Column>
      <Column>
        <div className="w-full">
          <h2 className="text-lg pb-3 flex justify-between items-center">
            Cases
            <Link
              to="/cases/create/client"
              state={{
                defendantId: defendant.id,
                propertyId: defendant.property?.id,
                clientId: defendant.property?.clientId,
              }}
              className="btn btn-primary btn-sm"
            >
              Add Case
            </Link>
          </h2>
          {cases?.map((caseData) => (
            <CaseCard key={caseData.id} caseData={caseData} />
          ))}
          {(caseCount || 0) > 30 && (
            <div className="pt-4 pb-6 text-center">
              <Link
                to={`/cases?defendantId=${defendant.id}`}
                className="btn btn-link"
              >
                Showing 30 of {caseCount} cases - view all
              </Link>
            </div>
          )}
        </div>
      </Column>
    </Columns>
  );
}
