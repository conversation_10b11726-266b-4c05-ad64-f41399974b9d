import { DEFENDANT_DEFAULTS } from "@shared/constants";
import { Button } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON>eader,
  FormWrapper,
  HookForm,
} from "@web/src/components/forms/forms";
import { useLocation, useNavigate } from "react-router-dom";
import DefendantForm from "./defendant-form";
import { useCreateDefendant } from "./hooks";

export default function DefendantCreate() {
  const navigate = useNavigate();
  const { state } = useLocation();
  const { create: createDefendant, isPending } = useCreateDefendant();

  const defaultValues = { ...DEFENDANT_DEFAULTS, ...state };

  const onSubmit = async (data) => {
    try {
      const createdDefendant = await createDefendant(data);
      navigate(`/defendants/${createdDefendant.id}`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader title="New Defendant" />
        <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
          <DefendantForm>
            <FormButtons>
              <Button className="btn-ghost" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary"
                disabled={isPending}
                type="submit"
              >
                Create Defendant
              </Button>
            </FormButtons>
          </DefendantForm>
        </HookForm>
      </FormWrapper>
    </div>
  );
}
