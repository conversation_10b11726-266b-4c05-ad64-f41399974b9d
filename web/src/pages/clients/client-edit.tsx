import { <PERSON><PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON><PERSON><PERSON>,
  Hook<PERSON><PERSON>,
} from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import ClientForm from "./client-form";
import { useClient, useUpdateClient } from "./hooks";

export default function ClientEdit() {
  const { clientId } = useParams();
  const { client } = useClient(clientId);
  const navigate = useNavigate();
  const { update: updateClient, isPending } = useUpdateClient(client!, {
    gcTime: 0,
  }); // RE: gcTime, see NOTES section in README

  if (!client) return null;

  const defaultValues = {
    name: client.name,
    companyType: client.companyType,
    address1: client.address1,
    address2: client.address2,
    city: client.city,
    state: client.state,
    zip: client.zip,
    clientNotes: client.clientNotes,
    doNotUse: client.doNotUse,
  };

  const onSubmit = async (data) => {
    try {
      await updateClient(data);
      navigate(`/clients/${clientId}`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader>
          <h2 className="text-xl">Edit Client: {client.name}</h2>
        </FormHeader>
        <HookForm defaultValues={defaultValues} onSubmit={onSubmit}>
          <ClientForm />
          <FormButtons>
            <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button
              className="btn-primary btn-sm"
              disabled={isPending}
              type="submit"
            >
              Update Client
            </Button>
          </FormButtons>
        </HookForm>
      </FormWrapper>
    </div>
  );
}
