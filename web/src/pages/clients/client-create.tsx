import { CLIENT_DEFAULTS } from "@shared/constants";
import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FormW<PERSON>per,
  HookF<PERSON>,
} from "@web/src/components/forms/forms";
import queryString from "query-string";
import { useLocation, useNavigate } from "react-router-dom";
import ClientForm from "./client-form";
import { useCreateClient } from "./hooks";

export default function ClientCreate() {
  const navigate = useNavigate();
  const { state, search } = useLocation();
  const { create: createClient, isPending } = useCreateClient();
  const query = queryString.parse(search);
  const defaultValues = {
    ...CLIENT_DEFAULTS,
    ...state,
    ...query,
  };

  const onSubmit = async (data: any) => {
    try {
      const createdClient = await createClient(data);
      navigate(`/clients/${createdClient.id}`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader title="New Client" />
        <HookForm defaultValues={defaultValues} onSubmit={onSubmit}>
          <ClientForm />
          <FormButtons>
            <Button className="btn-ghost" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button className="btn-primary" disabled={isPending} type="submit">
              Create Client
            </Button>
          </FormButtons>
        </HookForm>
      </FormWrapper>
    </div>
  );
}
