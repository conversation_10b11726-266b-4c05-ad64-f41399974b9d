import {
  HookExpanding<PERSON>extarea,
  HookIn<PERSON>,
  HookStateSelect,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@web/src/components/forms/forms";

export default function ClientForm({ prefix = "" }: { prefix?: string }) {
  const getName = (name) => (prefix ? `${prefix}.${name}` : name);

  return (
    <>
      <HookInput
        rules={{ required: "Name is required" }}
        name={getName("name")}
        label="Name"
        placeholder="Name"
      />
      <HookToggle
        name={getName("doNotUse")}
        label="Special Use Only?"
        color="error"
      />
      <HookInput
        name={getName("companyType")}
        label="Company Type"
        placeholder="Company Type"
      />
      <div>
        <div className="label py-1">
          <span className="label-text">Address</span>
        </div>
        <HookInput name={getName("address1")} placeholder="Address Line 1" />
        <HookInput name={getName("address2")} placeholder="Address Line 2" />
        <div className="flex gap-3 items-start">
          <HookInput name={getName("city")} placeholder="City" />
          <HookStateSelect name={getName("state")} placeholder="State" />
          <HookInput name={getName("zip")} placeholder="Zip" />
        </div>
      </div>
      <HookExpandingTextarea
        name={getName("clientNotes")}
        label="Notes"
        placeholder="Notes"
      />
    </>
  );
}
