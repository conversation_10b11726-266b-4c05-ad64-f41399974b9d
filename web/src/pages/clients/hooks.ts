import { Client } from "@shared/db/schema/clients";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useUpdateClient = (client: Client, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) =>
      axios.put(`/api/clients/${client.id}`, changes),
    mutationKey: ["clients", client?.id],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["clients"] }),
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useCreateClient = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (client: any) => {
      const response = await axios.post("/api/clients", client);
      return response.data;
    },
    mutationKey: ["clients", "create"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["clients"] }),
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useClients = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["clients", params],
    queryFn: async (): Promise<{ clients: any; count: number }> => {
      const response = await axios.get("/api/clients", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const clients: Client[] = useMemo(() => {
    if (!data?.clients) return null;
    return data.clients.map((client) => new Client(client));
  }, [data?.clients]);

  return {
    count: data?.count,
    clients,
    ...passedProps,
  };
};

export const useClient = (id: string | undefined) => {
  const { data: clientRaw, ...passedProps } = useQuery({
    queryKey: ["clients", id],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/clients/${id}`);
      return response.data;
    },
    enabled: Boolean(id),
  });

  const client = useMemo(() => {
    if (!clientRaw) return null;
    return new Client(clientRaw);
  }, [clientRaw]);

  return {
    client,
    ...passedProps,
  };
};
