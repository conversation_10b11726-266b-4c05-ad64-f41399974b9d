import { MapPinIcon, PencilSquareIcon } from "@heroicons/react/24/outline";
import { TruncateText } from "@web/src/components/content";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { useBasePath } from "@web/src/lib/hooks";
import classNames from "classnames";
import { Link } from "react-router-dom";

export default function ClientInfo({ client }) {
  const basePath = useBasePath("client");

  return (
    <>
      <h1 className="text-xl mb-2 flex justify-between items-top">
        <div className="min-w-0">
          <TruncateText>
            <span className={classNames({ "text-error": client.doNotUse })}>
              {client.name} {client.companyType}
            </span>
          </TruncateText>
        </div>
        <Link to={`${basePath}/edit`} className="btn btn-sm btn-primary">
          Edit
        </Link>
      </h1>
      {client.companyType && <div className="text-sm"></div>}
      <MetadataTable>
        <MetadataItem label={<MapPinIcon height={24} width={24} />}>
          <a
            href={`https://maps.google.com/?q=${client.getAddressForUrl()}`}
            className="text-sm inline-block hover:underline"
            target="_blank"
          >
            {client.address1}
            <br />
            {client.address2 && (
              <>
                {client.address2}
                <br />
              </>
            )}
            {client.city}, {client.state} {client.zip}
          </a>
        </MetadataItem>
        <MetadataItem label={<PencilSquareIcon height={24} width={24} />}>
          <p className="text-sm whitespace-pre-wrap">{client.clientNotes}</p>
        </MetadataItem>
      </MetadataTable>
    </>
  );
}
