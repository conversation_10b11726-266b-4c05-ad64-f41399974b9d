import { HookForm, HookInput } from "@web/src/components/forms/forms";
import { useForm } from "react-hook-form";

export default function ContactForm({ contact, onSubmit, children: buttons }) {
  const defaultValues = {
    name: contact.name,
    phone: contact.phone,
    fax: contact.fax,
    cell: contact.cell,
    email: contact.email,
    other: contact.other,
  };

  const formContext = useForm({ defaultValues });

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <HookInput name="name" placeholder="Name" />
      <HookInput name="phone" placeholder="Phone" />
      <HookInput name="fax" placeholder="Fax" />
      <HookInput name="cell" placeholder="Cell" />
      <HookInput name="email" placeholder="Email" />
      <HookInput name="other" placeholder="Other" />
      {buttons}
    </HookForm>
  );
}
