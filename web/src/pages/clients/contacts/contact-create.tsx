import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON><PERSON>er,
  FormWrapper,
} from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import ContactForm from "./contact-form";
import { useCreateContact } from "./hooks";

export default function ContactCreate() {
  const navigate = useNavigate();
  const { clientId, propertyId } = useParams();
  const { create: createContact, isPending } = useCreateContact();

  const contact = {
    name: "",
    phone: "",
    fax: "",
    cell: "",
    email: "",
    other: "",
  };

  const onSubmit = async (data) => {
    try {
      const newContact = { ...data };

      if (propertyId) newContact.propertyId = propertyId;
      else newContact.clientId = clientId;
      await createContact(newContact);

      let redirectUrl = `/clients/${clientId}`;
      if (propertyId) redirectUrl = `/properties/${propertyId}`;

      navigate(redirectUrl);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <FormWrapper>
      <FormHeader>
        <h3 className="text-lg">New Contact</h3>
      </FormHeader>
      <ContactForm contact={contact} onSubmit={onSubmit}>
        <FormButtons>
          <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button
            className="btn-primary btn-sm"
            disabled={isPending}
            type="submit"
          >
            Create Contact
          </Button>
        </FormButtons>
      </ContactForm>
    </FormWrapper>
  );
}
