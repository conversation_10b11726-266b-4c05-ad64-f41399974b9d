import { Contact } from "@shared/db/schema/contacts";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { useConfirmation } from "@web/src/components/confirmation";
import axios from "axios";
import { useMemo } from "react";

export const useUpdateContact = (contact: Contact, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) =>
      axios.put(`/api/contacts/${contact.id}`, changes),
    mutationKey: ["contacts", contact?.id],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useCreateContact = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (contact: any) => {
      const response = await axios.post("/api/contacts", contact);
      return response.data;
    },
    mutationKey: ["contacts", "create"],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useContacts = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["contacts", params],
    queryFn: async (): Promise<{ contacts: any; count: number }> => {
      const response = await axios.get("/api/contacts", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const contacts: Contact[] = useMemo(() => {
    if (!data?.contacts) return null;
    return data.contacts.map((contact) => new Contact(contact));
  }, [data?.contacts]);

  return {
    count: data?.count,
    contacts,
    ...passedProps,
  };
};

export const useContact = (id: string | undefined) => {
  const { data: contactRaw, ...passedProps } = useQuery({
    queryKey: ["contacts", id],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/contacts/${id}`);
      return response.data;
    },
    enabled: Boolean(id),
  });

  const contact = useMemo(() => {
    if (!contactRaw) return null;
    return new Contact(contactRaw);
  }, [contactRaw]);

  return {
    contact,
    ...passedProps,
  };
};

export const useDeleteContact = (contact: Contact) => {
  const queryClient = useQueryClient();
  const { confirm } = useConfirmation();

  const { mutate: deleteContact, isPending } = useMutation({
    mutationFn: () => axios.delete(`/api/contacts/${contact.id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
  });

  const handleDelete = () => {
    confirm({
      headerText: "Delete Contact",
      message: `Are you sure you want to delete ${contact.name}? This action cannot be undone.`,
      confirmationText: "Delete",
      confirmationColor: "error",
      callback: () => deleteContact(),
    });
  };

  return { delete: handleDelete, isPending };
};
