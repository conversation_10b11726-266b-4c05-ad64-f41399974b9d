import {
  AtSymbolIcon,
  ClipboardIcon,
  DevicePhoneMobileIcon,
  PaperAirplaneIcon,
  PencilSquareIcon,
  PhoneIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { Contact } from "@shared/db/schema/contacts";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { TooltipCopy } from "@web/src/components/utils";
import { useBasePath } from "@web/src/lib/hooks";
import { Card } from "react-daisyui";
import { Link, useMatch, useParams } from "react-router-dom";
import ContactEdit from "./contact-edit";
import { useDeleteContact } from "./hooks";

export default function ContactCard({ contact }: { contact: Contact }) {
  const { propertyId } = useParams();
  const basePath = useBasePath(propertyId ? "property" : "client");
  const isEditingContact = useMatch(`${basePath}/contacts/${contact.id}/edit`);
  const { delete: deleteContact, isPending } = useDeleteContact(contact);
  if (isEditingContact) return <ContactEdit />;

  return (
    <Card className="bg-slate-100">
      <Card.Body className="px-4 py-2 text-sm">
        <Card.Title className="text-lg font-light justify-between">
          {contact.name}
          <div className="flex gap-2">
            <Link
              to={`${basePath}/contacts/${contact.id}/edit`}
              className="btn btn-sm btn-ghost text-primary"
            >
              Edit
            </Link>
            <button
              onClick={deleteContact}
              className="btn btn-sm btn-ghost text-error"
              disabled={isPending}
            >
              <TrashIcon height={18} width={18} />
            </button>
          </div>
        </Card.Title>
        <MetadataTable>
          {contact.email && (
            <MetadataItem label={<AtSymbolIcon height={18} width={18} />}>
              <div className="flex items-center">
                <TooltipCopy title={contact.email}>
                  <ClipboardIcon height={18} width={18} />
                </TooltipCopy>
                <a
                  href={`mailto:${contact.email}`}
                  className="hover:underline ml-1"
                  target="_blank"
                >
                  {contact.email}
                </a>
              </div>
            </MetadataItem>
          )}
          {contact.phone && (
            <MetadataItem label={<PhoneIcon height={18} width={18} />}>
              <a
                href={`tel:${contact.phone}`}
                className="hover:underline"
                target="_blank"
              >
                {contact.phone}
              </a>
            </MetadataItem>
          )}
          {contact.cell && (
            <MetadataItem
              label={<DevicePhoneMobileIcon height={18} width={18} />}
            >
              <a
                href={`tel:${contact.cell}`}
                className="hover:underline"
                target="_blank"
              >
                {contact.cell}
              </a>
            </MetadataItem>
          )}
          {contact.fax && (
            <MetadataItem label={<PaperAirplaneIcon height={18} width={18} />}>
              {contact.fax}
            </MetadataItem>
          )}
          {contact.notes && (
            <MetadataItem label={<PencilSquareIcon height={18} width={18} />}>
              {contact.notes}
            </MetadataItem>
          )}
        </MetadataTable>
      </Card.Body>
    </Card>
  );
}
