import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FormWrapper,
} from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import ContactForm from "./contact-form";
import { useContact, useUpdateContact } from "./hooks";

export default function ContactEdit() {
  const { contactId, clientId, propertyId } = useParams();
  const { contact } = useContact(contactId);
  const navigate = useNavigate();
  const { update: updateContact, isPending } = useUpdateContact(contact!, {
    gcTime: 0,
  }); // RE: gcTime, see NOTES section in README

  if (!contact) return null;

  const onSubmit = async (data) => {
    try {
      await updateContact(data);

      let redirectUrl = `/clients/${clientId}`;
      if (propertyId) redirectUrl = `/properties/${propertyId}`;

      navigate(redirectUrl);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <FormWrapper>
      <FormHeader>
        <h2 className="text-xl">Edit Contact: {contact.name}</h2>
      </FormHeader>
      <ContactForm contact={contact} onSubmit={onSubmit}>
        <FormButtons>
          <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button
            className="btn-primary btn-sm"
            disabled={isPending}
            type="submit"
          >
            Update Contact
          </Button>
        </FormButtons>
      </ContactForm>
    </FormWrapper>
  );
}
