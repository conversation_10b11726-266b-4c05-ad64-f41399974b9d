import { ClipboardIcon } from "@heroicons/react/24/outline";
import { Contact } from "@shared/db/schema/contacts";
import { Tooltip, TooltipCopy } from "@web/src/components/utils";

export default function ContactCopyAllEmails({
  contacts = [],
  ...props
}: {
  contacts: Contact[];
} & { [x: string]: any }) {
  const allContactEmails = contacts
    ?.filter((contact) => contact.email && !contact.deletedAt)
    .map((contact) => contact.email);

  if (!allContactEmails.length) {
    return (
      <Tooltip title="No contact emails">
        <ClipboardIcon {...props} height={18} width={18} />
      </Tooltip>
    );
  }

  return (
    <TooltipCopy
      title={`${allContactEmails.length} contact emails`}
      text={allContactEmails.join(";")}
    >
      <ClipboardIcon {...props} height={18} width={18} />
    </TooltipCopy>
  );
}
