import { ThemedLink } from "@web/src/components/content";
import { TextFilter } from "@web/src/components/forms/filters";
import { CaseLabelSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useUrlQuery } from "@web/src/lib/hooks";
import classNames from "classnames";
import { times } from "lodash";
import { Link } from "react-router-dom";
import { useClients } from "./hooks";

export default function AllClients() {
  const { params } = useUrlQuery();
  const { clients, count, isFetching } = useClients(params);
  const { Table, SelectableTD, SelectableTH, Pager } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: count,
    data: clients,
  });

  if (!clients) return null;

  return (
    <div className="px-4 pb-4">
      <div className="navbar sticky top-0 z-20 bg-base-100 flex items-center justify-between border-slate-300 border-b">
        <div className="flex">
          <h1 className="text-3xl font-light pl-3 pr-8">Clients</h1>
          <div className="flex-grow">
            <div className="w-64">
              <TextFilter paramKey="search" placeholder="Search" autoFocus />
            </div>
          </div>
        </div>
        <div>
          <Link to="/clients/create" className="btn btn-primary">
            Add Client
          </Link>
        </div>
      </div>

      <Table className="table table-sm table-pin-rows">
        <thead>
          <tr className="bg-base-100" style={{ top: "4rem" }}>
            <SelectableTH className="pt-4" />
            <th className="pt-4">Name</th>
            <th>Address</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          {clients.length > 0 &&
            clients.map((client, index) => {
              return (
                <tr key={client.id}>
                  <SelectableTD value={client.id} index={index} />
                  <td>
                    <ThemedLink
                      to={`/clients/${client.id}`}
                      color="accent"
                      className="font-semibold"
                    >
                      <span
                        className={classNames({
                          "text-error": client.doNotUse,
                        })}
                      >
                        {client.name}
                      </span>
                    </ThemedLink>
                  </td>
                  <td>
                    <span>
                      {client.address1} {client.address2}
                      <br />
                      {client.city}, {client.state} {client.zip}
                    </span>
                  </td>
                  <td>
                    <span>{client.clientNotes}</span>
                  </td>
                </tr>
              );
            })}

          {isFetching &&
            clients.length == 0 &&
            times(30, (index) => (
              <tr>
                <td></td>
                <td>
                  <CaseLabelSkeleton />
                </td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            ))}
        </tbody>
      </Table>
      <Pager className="mt-8" showCount />
    </div>
  );
}
