import { Column, Columns } from "@web/src/components/layout";
import { useBasePath } from "@web/src/lib/hooks";
import { filter } from "lodash";
import { Link, useMatch, useParams } from "react-router-dom";
import { CaseCard } from "../cases/components";
import { useCases } from "../cases/hooks";
import ClientInfo from "./client-info";
import ContactCard from "./contacts/contact-card";
import ContactCopyAllEmails from "./contacts/contact-copy-all-emails";
import ContactCreate from "./contacts/contact-create";
import { useClient } from "./hooks";
import { useProperties } from "./properties/hooks";
import PropertyCard from "./properties/property-card";

export default function ClientView() {
  const { clientId } = useParams();
  const { client } = useClient(clientId);
  const basePath = useBasePath("client");
  const isAddingContact = useMatch(`${basePath}/contacts/create`);
  const { properties, count: propertyCount } = useProperties({ clientId });
  const { cases, count: caseCount } = useCases({
    clientId,
    orderByStatus: true,
  });

  if (!client) return null;

  const activeContacts = filter(
    client.contacts,
    (contact) => !contact.deletedAt
  );

  return (
    <Columns columnCount={3}>
      <Column>
        <div className="w-full">
          <ClientInfo client={client} />
        </div>
        <div className="border-t border-t-slate-300 my-6">
          {!isAddingContact && (
            <h2 className="text-lg py-3 flex justify-between items-center">
              <div className="flex items-center">
                <span className="mr-1">Contacts</span>
                <ContactCopyAllEmails contacts={client.contacts!} />
              </div>
              <Link
                to={`${basePath}/contacts/create`}
                className="btn btn-primary btn-sm"
              >
                Add Contact
              </Link>
            </h2>
          )}
          {isAddingContact && (
            <div className="my-6">
              <ContactCreate />
            </div>
          )}
          {activeContacts?.map((contact) => {
            return (
              <div key={contact.id} className="mb-3">
                <ContactCard contact={contact} />
              </div>
            );
          })}
        </div>
      </Column>
      <Column>
        <div className="w-full">
          <h2 className="text-lg pb-3 flex justify-between items-center">
            Properties
            <Link
              to="/properties/create"
              state={{ clientId: client.id }}
              className="btn btn-primary btn-sm"
            >
              Add Property
            </Link>
          </h2>
          {properties?.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
          {(propertyCount || 0) > 30 && (
            <div className="pt-4 pb-6 text-center">
              <Link
                to={`/properties?clientId=${client.id}`}
                className="btn btn-link"
              >
                Showing 30 of {propertyCount} properties - view all
              </Link>
            </div>
          )}
        </div>
      </Column>
      <Column>
        <div className="w-full">
          <h2 className="text-lg pb-3 flex justify-between items-center">
            Cases
            <Link
              to="/cases/create/client"
              state={{ clientId: client.id }}
              className="btn btn-primary btn-sm"
            >
              Add Case
            </Link>
          </h2>
          {cases?.map((caseData) => (
            <CaseCard key={caseData.id} caseData={caseData} />
          ))}
          {(caseCount || 0) > 30 && (
            <div className="pt-4 pb-6 text-center">
              <Link
                to={`/cases?clientId=${client.id}`}
                className="btn btn-link"
              >
                Showing 30 of {caseCount} cases - view all
              </Link>
            </div>
          )}
        </div>
      </Column>
    </Columns>
  );
}
