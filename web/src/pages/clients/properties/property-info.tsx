import {
  BuildingOffice2Icon,
  EnvelopeIcon,
  MapPinIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { ThemedLink } from "@web/src/components/content";
import { MetadataItem, MetadataTable } from "@web/src/components/layout";
import { useBasePath } from "@web/src/lib/hooks";
import classNames from "classnames";
import { Link } from "react-router-dom";

export default function PropertyInfo({ property }) {
  const basePath = useBasePath("property");

  return (
    <>
      <h1 className="text-xl mb-2 flex justify-between items-top">
        <span className={classNames({ "text-error": property.doNotUse })}>
          {property.name}
        </span>
        <Link to={`${basePath}/edit`} className="btn btn-sm btn-primary">
          Edit
        </Link>
      </h1>
      <MetadataTable>
        <MetadataItem label={<BuildingOffice2Icon height={24} width={24} />}>
          <ThemedLink to={`/clients/${property.client.id}`} color="accent">
            <span
              className={classNames({ "text-error": property.client.doNotUse })}
            >
              {property.client.name}
            </span>
          </ThemedLink>
        </MetadataItem>
        <MetadataItem label={<MapPinIcon height={24} width={24} />}>
          <a
            href={`https://maps.google.com/?q=${property.getAddressForUrl()}`}
            className="text-sm inline-block hover:underline"
            target="_blank"
          >
            {property.address1}
            <br />
            {property.address2 && (
              <>
                {property.address2}
                <br />
              </>
            )}
            {property.city}, {property.state} {property.zip}
            {property.county && (
              <>
                <br />
                {property.county} County
              </>
            )}
          </a>
        </MetadataItem>
        {property.isHomeForward && (
          <MetadataItem
            title="Home Forward"
            label={<img src="/static/images/home-forward.png" />}
          >
            Home Forward Property
          </MetadataItem>
        )}
        <MetadataItem label={<PencilSquareIcon height={24} width={24} />}>
          <p className="text-sm whitespace-pre-wrap">
            {property.propertyNotes}
          </p>
        </MetadataItem>
        <MetadataItem label={<EnvelopeIcon height={24} width={24} />}>
          <p className="text-sm whitespace-pre-wrap">
            {property.actionServices}
          </p>
        </MetadataItem>
      </MetadataTable>
    </>
  );
}
