import { PROPERTY_DEFAULTS } from "@shared/constants";
import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON><PERSON>per,
  HookForm,
} from "@web/src/components/forms/forms";
import queryString from "query-string";
import { useLocation, useNavigate } from "react-router-dom";
import { useCreateProperty } from "./hooks";
import PropertyForm from "./property-form";

export default function PropertyCreate() {
  const navigate = useNavigate();
  const { state, search } = useLocation();
  const { create: createProperty, isPending } = useCreateProperty();
  const query = queryString.parse(search);
  const defaultValues = {
    ...PROPERTY_DEFAULTS,
    ...state,
    ...query,
  };

  const onSubmit = async (data: any) => {
    try {
      const createdProperty = await createProperty(data);

      const redirectUrl = `/properties/${createdProperty.id}`;
      navigate(redirectUrl);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader title="New Property" />
        <HookForm defaultValues={defaultValues} onSubmit={onSubmit}>
          <PropertyForm>
            <FormButtons>
              <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary btn-sm"
                disabled={isPending}
                type="submit"
              >
                Create Property
              </Button>
            </FormButtons>
          </PropertyForm>
        </HookForm>
      </FormWrapper>
    </div>
  );
}
