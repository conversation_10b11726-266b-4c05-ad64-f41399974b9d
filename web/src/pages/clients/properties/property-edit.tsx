import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import { useProperty, useUpdateProperty } from "./hooks";
import PropertyForm from "./property-form";

export default function PropertyEdit() {
  const { propertyId } = useParams();
  const { property } = useProperty(propertyId);
  const navigate = useNavigate();
  const { update: updateProperty, isPending } = useUpdateProperty(property!, {
    gcTime: 0,
  }); // RE: gcTime, see NOTES section in README
  if (!property) return null;

  const defaultValues = {
    clientId: property.clientId,
    name: property.name,
    address1: property.address1,
    address2: property.address2,
    city: property.city,
    state: property.state,
    zip: property.zip,
    county: property.county,
    propertyNotes: property.propertyNotes,
    actionServices: property.actionServices,
    doNotUse: property.doNotUse,
    isHomeForward: property.isHomeForward,
  };

  const onSubmit = async (data) => {
    try {
      await updateProperty(data);

      const redirectUrl = `/properties/${propertyId}`;
      navigate(redirectUrl);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader>
          <h2 className="text-xl">Edit Property: {property.name}</h2>
        </FormHeader>
        <HookForm defaultValues={defaultValues} onSubmit={onSubmit}>
          <PropertyForm>
            <FormButtons>
              <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary btn-sm"
                disabled={isPending}
                type="submit"
              >
                Update Property
              </Button>
            </FormButtons>
          </PropertyForm>
        </HookForm>
      </FormWrapper>
    </div>
  );
}
