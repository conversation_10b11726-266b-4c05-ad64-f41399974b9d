import { Column, Columns } from "@web/src/components/layout";
import { useBasePath } from "@web/src/lib/hooks";
import { filter } from "lodash";
import { Link, useMatch, useParams } from "react-router-dom";
import { CaseCard } from "../../cases/components";
import { useCases } from "../../cases/hooks";
import { DefendantCard } from "../../defendants/components";
import { useDefendants } from "../../defendants/hooks";
import ContactCard from "../contacts/contact-card";
import ContactCopyAllEmails from "../contacts/contact-copy-all-emails";
import ContactCreate from "../contacts/contact-create";
import { useProperty } from "./hooks";
import PropertyInfo from "./property-info";

export default function PropertyView() {
  const { propertyId } = useParams();
  const { property } = useProperty(propertyId);
  const basePath = useBasePath("property");
  const isAddingContact = useMatch(`${basePath}/contacts/create`);
  const { cases, count: caseCount } = useCases({
    propertyId,
    orderByStatus: true,
  });
  const { defendants, count: defendantCount } = useDefendants({ propertyId });

  if (!property) return null;

  const activeContacts = filter(
    property.contacts,
    (contact) => !contact.deletedAt
  );

  return (
    <Columns>
      <Column>
        <div className="w-full">
          <PropertyInfo property={property} />
        </div>
        <div className="border-t border-t-slate-300 my-6">
          {!isAddingContact && (
            <h2 className="text-lg py-3 flex justify-between items-center">
              <div className="flex items-center">
                <span className="mr-1">Contacts</span>
                <ContactCopyAllEmails contacts={property.contacts!} />
              </div>
              <Link
                to={`${basePath}/contacts/create`}
                className="btn btn-primary btn-sm"
              >
                Add Contact
              </Link>
            </h2>
          )}
          {isAddingContact && (
            <div className="my-6">
              <ContactCreate />
            </div>
          )}
          {activeContacts?.map((contact) => {
            return (
              <div key={contact.id} className="mb-3">
                <ContactCard contact={contact} />
              </div>
            );
          })}
        </div>
      </Column>
      <Column>
        <div className="w-full">
          <h2 className="text-lg pb-3 flex justify-between items-center">
            Defendants
            <Link
              to="/defendants/create"
              state={{ propertyId: property.id }}
              className="btn btn-primary btn-sm"
            >
              Add Defendant
            </Link>
          </h2>
          {defendants?.map((defendant) => (
            <DefendantCard key={defendant.id} defendant={defendant} />
          ))}
          {(defendantCount || 0) > 30 && (
            <div className="pt-4 pb-6 text-center">
              <Link
                to={`/defendants?propertyId=${property.id}`}
                className="btn btn-link"
              >
                Showing 30 of {defendantCount} defendants - view all
              </Link>
            </div>
          )}
        </div>
      </Column>
      <Column>
        <div className="w-full">
          <h2 className="text-lg pb-3 flex justify-between items-center">
            Cases
            <Link
              to="/cases/create/client"
              state={{ propertyId: property.id, clientId: property.client?.id }}
              className="btn btn-primary btn-sm"
            >
              Add Case
            </Link>
          </h2>
          {cases?.map((caseData) => (
            <CaseCard key={caseData.id} caseData={caseData} />
          ))}
          {(caseCount || 0) > 30 && (
            <div className="pt-4 pb-6 text-center">
              <Link
                to={`/cases?propertyId=${property.id}`}
                className="btn btn-link"
              >
                Showing 30 of {caseCount} cases - view all
              </Link>
            </div>
          )}
        </div>
      </Column>
    </Columns>
  );
}
