import { Property } from "@shared/db/schema/properties";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useUpdateProperty = (property: Property, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) =>
      axios.put(`/api/properties/${property.id}`, changes),
    mutationKey: ["properties", property?.id],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useCreateProperty = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (property: any) => {
      const response = await axios.post("/api/properties", property);
      return response.data;
    },
    mutationKey: ["properties", "create"],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useProperties = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["properties", params],
    queryFn: async (): Promise<{ properties: any; count: number }> => {
      const response = await axios.get("/api/properties", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const properties: Property[] = useMemo(() => {
    if (!data?.properties) return null;
    return data.properties.map((property) => new Property(property));
  }, [data?.properties]);

  return {
    count: data?.count,
    properties,
    ...passedProps,
  };
};

export const useProperty = (id: string | undefined) => {
  const { data: propertyRaw, ...passedProps } = useQuery({
    queryKey: ["properties", id],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/properties/${id}`);
      return response.data;
    },
    enabled: Boolean(id),
  });

  const property = useMemo(() => {
    if (!propertyRaw) return null;
    return new Property(propertyRaw);
  }, [propertyRaw]);

  return {
    property,
    ...passedProps,
  };
};
