import { PencilSquareIcon } from "@heroicons/react/24/outline";
import { ThemedLink } from "@web/src/components/content";
import { Popover } from "@web/src/components/utils";
import classNames from "classnames";
import { Card } from "react-daisyui";

export default function PropertyCard({ property }) {
  const hasNotes = property.propertyNotes || property.actionServices;

  return (
    <Popover>
      <Popover.Trigger>
        <Card className="bg-slate-100 mb-3">
          <Card.Body className="px-4 py-2">
            <div className="flex items-center justify-between">
              <ThemedLink to={`/properties/${property.id}`} color="accent">
                <div>
                  <span
                    className={classNames({
                      "text-error": property.doNotUse,
                    })}
                  >
                    {property.name}
                  </span>
                  <div className="text-sm text-gray-400">
                    {property.address1} {property.address2}
                    <br />
                    {property.city}, {property.state} {property.zip}
                  </div>
                </div>
              </ThemedLink>

              <PencilSquareIcon
                height={24}
                width={24}
                className={hasNotes ? "text-slate-900" : "text-slate-300"}
              />
            </div>
          </Card.Body>
        </Card>
      </Popover.Trigger>
      {hasNotes && (
        <Popover.Content>
          <div>
            {property.propertyNotes && (
              <div className={property.actionServices ? "mb-4" : ""}>
                <label className="border-b block border-slate-300 font-light mb-1">
                  Property Notes
                </label>
                <p className="text-sm">{property.propertyNotes}</p>
              </div>
            )}
            {property.actionServices && (
              <div>
                <label className="border-b block border-slate-300 font-light mb-1">
                  Action Services Notes
                </label>
                <p className="text-sm">{property.actionServices}</p>
              </div>
            )}
          </div>
        </Popover.Content>
      )}
    </Popover>
  );
}
