import {
  HookCitySelect,
  HookClientSelect,
  HookCountySelect,
  HookExpandingTextarea,
  HookInput,
  HookLimitedStateSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useFormContext } from "react-hook-form";

export default function ClientForm({
  children: buttons,
  prefix = "",
}: {
  children?: any;
  prefix?: string;
}) {
  const getName = (name) => (prefix ? `${prefix}.${name}` : name);
  const formContext = useFormContext();
  const state = formContext.watch(getName("state"));

  return (
    <>
      {!prefix && (
        <HookClientSelect
          label="Client"
          name="clientId"
          rules={{ required: "Client is required" }}
        />
      )}
      <HookInput
        name={getName("name")}
        label="Name"
        placeholder="Name"
        rules={{ required: "Name is required" }}
      />
      <div className="flex gap-3">
        <HookToggle
          name={getName("isHomeForward")}
          label="Is Home Forward?"
          color="primary"
        />
        <HookToggle
          name={getName("doNotUse")}
          label="Special Use Only?"
          color="error"
        />
      </div>
      <div>
        <div className="label">
          <span className="label-text">Address</span>
        </div>
        <HookInput
          name={getName("address1")}
          placeholder="Address Line 1"
          rules={{ required: "Address is required" }}
        />
        <HookInput name={getName("address2")} placeholder="Address Line 2" />
        <div className="flex gap-3 items-start">
          <HookCitySelect
            state={state}
            name={getName("city")}
            placeholder="City"
            create
            rules={{ required: "City is required" }}
          />
          <HookLimitedStateSelect
            name={getName("state")}
            placeholder="State"
            rules={{ required: "State is required" }}
          />
          <HookInput
            name={getName("zip")}
            placeholder="Zip"
            rules={{ required: "Zip is required" }}
          />
        </div>
        <HookCountySelect
          state={state}
          name={getName("county")}
          placeholder="County"
          rules={{ required: "County is required" }}
        />
      </div>
      <HookExpandingTextarea
        name={getName("propertyNotes")}
        label="Property Notes"
        placeholder="Notes"
      />
      <HookExpandingTextarea
        name={getName("actionServices")}
        label="Action Services Notes"
        placeholder="Action Services Notes"
      />
      {buttons}
    </>
  );
}
