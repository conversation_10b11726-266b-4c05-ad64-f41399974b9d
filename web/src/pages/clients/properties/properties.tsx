import { ThemedLink } from "@web/src/components/content";
import {
  ClientSelectFilter,
  TextFilter,
} from "@web/src/components/forms/filters";
import { CaseLabelSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useUrlQuery } from "@web/src/lib/hooks";
import classNames from "classnames";
import { times } from "lodash";
import { Link } from "react-router-dom";
import { useProperties } from "./hooks";

export default function AllProperties() {
  const { params } = useUrlQuery();
  const { properties, count, isFetching } = useProperties(params);
  const { Table, SelectableTD, SelectableTH, Pager } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: count,
    data: properties,
  });

  if (!properties) return null;

  return (
    <div className="px-4 pb-4">
      <div className="navbar sticky top-0 z-20 bg-base-100 flex items-center justify-between border-slate-300 border-b">
        <div className="flex">
          <h1 className="text-3xl font-light pl-3 pr-8">Properties</h1>
          <div className="flex-grow flex">
            <div className="w-64">
              <TextFilter paramKey="search" placeholder="Search" autoFocus />
            </div>
            <div className="w-64 ml-3">
              <ClientSelectFilter />
            </div>
          </div>
        </div>
        <div>
          <Link to="/properties/create" className="btn btn-primary">
            Add Property
          </Link>
        </div>
      </div>

      <Table className="table table-sm table-pin-rows">
        <thead>
          <tr className="bg-base-100" style={{ top: "4rem" }}>
            <SelectableTH className="pt-4" />
            <th className="pt-4">Name</th>
            <th>Address</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          {properties.length > 0 &&
            properties.map((property, index) => {
              return (
                <tr key={property.id}>
                  <SelectableTD value={property.id} index={index} />
                  <td>
                    <ThemedLink
                      to={`/properties/${property.id}`}
                      color="accent"
                      className="font-semibold"
                    >
                      <span
                        className={classNames({
                          "text-error": property.doNotUse,
                        })}
                      >
                        {property.name}
                      </span>
                    </ThemedLink>
                  </td>
                  <td>
                    <span>
                      {property.address1} {property.address2}
                      <br />
                      {property.city}, {property.state} {property.zip}
                    </span>
                  </td>
                  <td>
                    <span>{property.propertyNotes}</span>
                  </td>
                </tr>
              );
            })}

          {isFetching &&
            properties.length == 0 &&
            times(30, (index) => (
              <tr key={index}>
                <td></td>
                <td>
                  <CaseLabelSkeleton />
                </td>
                <td></td>
                <td></td>
              </tr>
            ))}
        </tbody>
      </Table>
      <Pager className="mt-8" showCount />
    </div>
  );
}
