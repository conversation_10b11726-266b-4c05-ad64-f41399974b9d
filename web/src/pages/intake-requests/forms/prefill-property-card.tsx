import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import PrefillCard from "@web/src/components/prefill-card";
import { useProperty } from "@web/src/pages/clients/properties/hooks";
import queryString from "query-string";
import { useFormContext } from "react-hook-form";
import { Link, useParams } from "react-router-dom";
import { useIntakeRequest, useRequestMatching } from "../hooks";

export default function PrefillPropertyCard() {
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId, { decrypt: true });
  const formContext = useFormContext();

  // First check if client matches
  const { exactMatch: clientExactMatch } = useRequestMatching(
    intakeRequest?.clientName || "",
    "client"
  );

  // Only try to match property if client matched
  const { matchId, exactMatch } = useRequestMatching(
    clientExactMatch && intakeRequest?.propertyName
      ? intakeRequest.propertyName
      : "",
    "property",
    intakeRequest?.county || ""
  );

  // Check if property is already set
  const [propertyId, clientId] = formContext.watch(["propertyId", "clientId"]);
  const isComplete = Boolean(propertyId);

  // Load property data if we have a match
  const { property } = useProperty(matchId?.toString());

  // Property prefill handler
  const confirmProperty = () => {
    formContext.setValue("propertyId", matchId);
  };

  // Determine if there was a match (only if client matched first)
  const isMatch = clientExactMatch && exactMatch;

  return (
    <PrefillCard title="Property" complete={isComplete} match={isMatch}>
      <div className="flex justify-between gap-3">
        <div className="flex-grow">
          <div className="mb-1">
            <div className="flex items-center gap-2">
              {property ? (
                <Link
                  to={`/properties/${property.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={
                    property.doNotUse
                      ? "font-bold text-error hover:underline flex items-center gap-1"
                      : "font-bold hover:underline flex items-center gap-1"
                  }
                >
                  <span>{property.name}</span>
                  <ArrowTopRightOnSquareIcon className="h-3 w-3 text-primary" />
                </Link>
              ) : (
                <span className="font-bold">{intakeRequest?.propertyName}</span>
              )}
            </div>
          </div>
          {property && (
            <div className="text-xs text-gray-500">
              {property.address1} {property.address2}
              <br />
              {property.city}, {property.state} {property.zip}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <button
            type="button"
            className="btn btn-sm btn-primary"
            onClick={confirmProperty}
            disabled={!isMatch}
          >
            Set as Property
          </button>
          <Link
            className="btn btn-sm btn-primary btn-outline"
            to={`/properties/create?${queryString.stringify({
              name: intakeRequest?.propertyName || "",
              clientId: clientId || "",
            })}`}
            target="_blank"
          >
            New Property
          </Link>
        </div>
      </div>
    </PrefillCard>
  );
}
