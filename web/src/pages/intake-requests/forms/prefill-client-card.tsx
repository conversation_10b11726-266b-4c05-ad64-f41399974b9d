import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import PrefillCard from "@web/src/components/prefill-card";
import { useClient } from "@web/src/pages/clients/hooks";
import queryString from "query-string";
import { useFormContext } from "react-hook-form";
import { Link, useParams } from "react-router-dom";
import { useIntakeRequest, useRequestMatching } from "../hooks";

export default function PrefillClientCard() {
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId, { decrypt: true });
  const { matchId, exactMatch } = useRequestMatching(
    intakeRequest?.clientName!,
    "client"
  );
  const formContext = useFormContext();

  // Check if client is already set
  const clientId = formContext.watch("clientId");
  const isComplete = Boolean(clientId);

  // Load client data if we have a match
  const { client } = useClient(matchId?.toString());

  // Client prefill handler
  const confirmClient = () => {
    formContext.setValue("clientId", matchId);
  };

  // Determine if there was a match
  const isMatch = Boolean(matchId);

  return (
    <PrefillCard title="Client" complete={isComplete} match={isMatch}>
      <div className="flex justify-between gap-3">
        <div className="flex-grow">
          <div className="mb-1">
            <div className="flex items-center gap-2">
              {client ? (
                <Link
                  to={`/clients/${client.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={
                    client.doNotUse
                      ? "font-bold text-error hover:underline flex items-center gap-1"
                      : "font-bold hover:underline flex items-center gap-1"
                  }
                >
                  <span>{client.name}</span>
                  <ArrowTopRightOnSquareIcon className="h-3 w-3 text-primary" />
                </Link>
              ) : (
                <span className="font-bold">{intakeRequest?.clientName}</span>
              )}
            </div>
          </div>
          {client && (
            <div className="text-xs text-gray-500">
              {client.address1} {client.address2}
              <br />
              {client.city}, {client.state} {client.zip}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <button
            type="button"
            className="btn btn-sm btn-primary"
            onClick={confirmClient}
            disabled={!isMatch}
          >
            Set as Client
          </button>
          <Link
            className="btn btn-sm btn-primary btn-outline"
            to={`/clients/create?${queryString.stringify({
              name: intakeRequest?.clientName || "",
            })}`}
            target="_blank"
          >
            New Client
          </Link>
        </div>
      </div>
    </PrefillCard>
  );
}
