import PrefillCard from "@web/src/components/prefill-card";
import pluralize from "pluralize";
import { useFormContext } from "react-hook-form";
import { useParams } from "react-router-dom";
import { useIntakeRequest } from "../hooks";

export default function PrefillAdditionalDefendantsCard() {
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId, { decrypt: true });
  const formContext = useFormContext();

  // Check if additional defendants are already set
  const additionalDefendants = formContext.watch("additionalDefendants") || [];
  const isComplete = additionalDefendants.length > 0;

  // Get all defendants from the intake request
  const intakeDefendants = intakeRequest?.getDefendants() || [];

  // Get only additional defendants (skip the first one)
  const additionalIntakeDefendants = intakeDefendants.slice(1);

  // Determine if there are any additional defendants
  const hasAdditionalDefendants = additionalIntakeDefendants.length > 0;

  // Handler to add all additional defendants to the form
  const addAllDefendants = () => {
    formContext.setValue("additionalDefendants", additionalIntakeDefendants);
  };

  // Format defendant name for display
  const formatDefendantName = (defendant: any) => {
    let name = defendant.firstName || "";
    if (defendant.middleName) name += ` ${defendant.middleName}`;
    if (defendant.lastName) name += ` ${defendant.lastName}`;
    return name;
  };

  return (
    <PrefillCard
      title="Additional Defendants"
      complete={isComplete}
      match={hasAdditionalDefendants}
    >
      <div className="flex justify-between gap-3">
        <div className="flex-grow">
          {hasAdditionalDefendants ? (
            <div>
              <div className="mb-2 font-bold">
                {additionalIntakeDefendants.length} Additional{" "}
                {pluralize("Defendant", additionalIntakeDefendants.length)}
              </div>
              <div className="list-decimal mb-2 text-sm text-gray-500">
                {additionalIntakeDefendants.map((defendant, index) => (
                  <div key={index} className="mb-1">
                    {formatDefendantName(defendant)}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div>No additional defendants found in the intake request.</div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <button
            type="button"
            className="btn btn-sm btn-primary"
            onClick={addAllDefendants}
            disabled={!hasAdditionalDefendants || isComplete}
          >
            Add Defendants
          </button>
        </div>
      </div>
    </PrefillCard>
  );
}
