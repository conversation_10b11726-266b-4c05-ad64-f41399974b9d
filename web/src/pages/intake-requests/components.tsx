import {
  EnvelopeIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import { Tooltip } from "@web/src/components/utils";
import { useUrlQuery } from "@web/src/lib/hooks";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useUpdateRequests } from "./hooks";

export function CheckboxFilter({ label, paramKey }) {
  const { params, updateParams } = useUrlQuery();
  const isChecked = params[paramKey] === "true";

  const handleChange = () => {
    updateParams({ [paramKey]: isChecked ? null : "true" });
  };

  return (
    <div className="flex items-center gap-2">
      <label className="cursor-pointer label gap-2">
        <input
          type="checkbox"
          className="checkbox checkbox-md"
          checked={isChecked}
          onChange={handleChange}
        />
        <span>{label}</span>
      </label>
    </div>
  );
}

export function CommentField({ data }) {
  const [notesText, setNotesText] = useState(data?.notes || "");
  const { update } = useUpdateRequests();

  const handleNotesChange = (e) => {
    setNotesText(e.target.value);
    // Update the notes field in the database immediately
    update({
      ids: [data.id],
      data: { notes: e.target.value },
    });
  };

  useEffect(() => {
    setNotesText(data?.notes || "");
  }, [data]);

  return (
    <div className="relative">
      <textarea
        className="textarea textarea-bordered w-full max-h-14"
        value={notesText}
        onChange={handleNotesChange}
        placeholder="Add Notes..."
      />
    </div>
  );
}

export function ClioDocumentLink({
  intakeRequest,
  description,
  className = "",
  wrapperClassName = "",
}: {
  intakeRequest: any;
  description?: string;
  className?: string;
  wrapperClassName?: string;
}) {
  // Common content for both cases
  const content = (
    <>
      <ClioIcon />
      {description}
    </>
  );

  // Always return a div wrapper with consistent structure for tooltip compatibility
  return (
    <div className={`inline-block ${wrapperClassName}`}>
      {!intakeRequest?.clioFolderId ? (
        <div
          className={`flex items-center gap-2 opacity-50 cursor-not-allowed ${className}`}
        >
          {content}
        </div>
      ) : (
        <Link
          className={`flex items-center gap-2 ${className}`}
          to={`https://app.clio.com/nc/#/documents?folderId=${intakeRequest?.clioFolderId}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {content}
          {intakeRequest?.uploadError && (
            <Tooltip title="Missing Files, Please check Cognito Form">
              <ExclamationCircleIcon
                className="text-error"
                height={20}
                width={20}
              />
            </Tooltip>
          )}
        </Link>
      )}
    </div>
  );
}

export function ClioIcon({ height = 20, width = 20, className = "" }) {
  return (
    <img
      src="/static/images/clio_logo.svg"
      alt="Clio"
      height={height}
      width={width}
      className={className}
    />
  );
}

export function CognitoDocumentLink({
  intakeRequest,
  description,
  className = "",
  wrapperClassName = "",
}: {
  intakeRequest: any;
  description?: string;
  className?: string;
  wrapperClassName?: string;
}) {
  // Common content for both cases
  const content = (
    <>
      <CognitoIcon />
      {description}
    </>
  );

  // Always return a div wrapper with consistent structure for tooltip compatibility
  return (
    <div className={`inline-block ${wrapperClassName}`}>
      {!intakeRequest?.formEntryId ? (
        <div
          className={`flex items-center gap-2 opacity-50 cursor-not-allowed ${className}`}
        >
          {content}
        </div>
      ) : (
        <Link
          className={`flex items-center gap-2 ${className}`}
          to={`https://www.cognitoforms.com/AndorLaw2/${
            intakeRequest?.formEntryId?.split("-")[0]
          }/entries/${intakeRequest?.formEntryId?.split("-")[1]}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {content}
        </Link>
      )}
    </div>
  );
}

export function CognitoIcon({ height = 20, width = 20 }) {
  return (
    <img
      src="/static/images/cognito_logo.svg"
      alt="Cognito Forms"
      height={height}
      width={width}
    />
  );
}

interface EmailLinkProps {
  intakeRequest: any;
  className?: string;
  iconOnly?: boolean;
  tooltipTitle?: string;
}

export function EmailLink({
  intakeRequest,
  className = "px-2 btn-ghost btn btn-sm",
  iconOnly = false,
  tooltipTitle = "Email Submitter & CC's",
}: EmailLinkProps) {
  const getMailtoUrl = () => {
    const emails = intakeRequest?.getPropertyEmails();
    if (!emails) return "";

    const { submitterEmail, additionalIntakeEmails } = emails;
    const ccEmails = additionalIntakeEmails.filter(
      (email: string) => email && email.trim() !== ""
    );

    let mailtoUrl = `mailto:${submitterEmail}`;

    if (ccEmails.length > 0) {
      mailtoUrl += `?cc=${ccEmails.join(",")}`;
    }

    return mailtoUrl;
  };

  // Handle click to prevent form validation
  const handleClick = (e: React.MouseEvent) => {
    // Stop event propagation to prevent form validation
    e.stopPropagation();
  };

  return (
    <Tooltip title={tooltipTitle}>
      <a
        className={className}
        href={getMailtoUrl()}
        target="_blank"
        rel="noopener noreferrer"
        onClick={handleClick}
      >
        <EnvelopeIcon height={20} width={20} />
        {!iconOnly && "Email All Contacts"}
      </a>
    </Tooltip>
  );
}
