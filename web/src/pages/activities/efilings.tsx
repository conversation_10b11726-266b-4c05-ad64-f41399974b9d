import { ThemedLink } from "@web/src/components/content";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useEfile } from "@web/src/lib/hooks";
import { Link } from "react-router-dom";

export default function Efilings() {
  const { efilings } = useEfile();

  const { Table } = useTableWithQueryParams({
    data: efilings,
  });

  return (
    <div className="p-4">
      <div className="flex items-start border-slate-300 border-b mb-3 pb-3">
        <h1 className="text-3xl font-light pl-3 pr-8">Efilings</h1>
      </div>
      <Table className="table table-sm">
        <thead>
          <tr>
            <th className="text-lg">Case</th>
            <th className="text-lg">Efile Status</th>
            <th className="text-lg">Efile Draft</th>
            <th className="text-lg">Filed By</th>
          </tr>
        </thead>
        <tbody>
          {efilings.map((efileOp) => {
            const statusIndication =
              efileOp.status === "complete"
                ? "badge-success"
                : efileOp.status === "failed"
                ? "badge-error"
                : "badge-ghost";
            const errorColor =
              efileOp.status === "failed" ? "text-red-500" : "";
            const isProcessing = efileOp.status === "processing";
            return (
              <tr key={efileOp.id}>
                <td className="text-base">
                  <ThemedLink to={`/cases/${efileOp.case.id}`} color="primary">
                    <span className="font-semibold">
                      {efileOp.client?.name} ~ {efileOp.property.county} County
                    </span>
                    <br />
                    <span className="text-slate-500">
                      {efileOp.case.caseNumber} {efileOp.property?.name} vs.{" "}
                      {`${efileOp.defendant?.firstName} ${efileOp.defendant?.lastName}`}
                    </span>
                  </ThemedLink>
                </td>
                <td>
                  {!isProcessing && (
                    <span
                      className={`badge ${statusIndication} p-3 font-semibold text-white`}
                    >
                      {efileOp.status}
                    </span>
                  )}
                  {isProcessing && (
                    <div className="flex items-center">
                      <span
                        className={`badge ${statusIndication} p-3 font-semibold text-gray-800 mr-5`}
                      >
                        {efileOp.status}
                      </span>
                      <span className="loading loading-infinity"></span>
                    </div>
                  )}
                </td>
                <td className={`font-semibold underline ${errorColor}`}>
                  {efileOp.status === "complete" ? (
                    <Link to={efileOp.eFileUrl} target="_blank">
                      View Draft
                    </Link>
                  ) : efileOp.status === "failed" ? (
                    <Link to={`/cases/${efileOp.caseId}/efilings`}>
                      View Error
                    </Link>
                  ) : null}
                </td>
                <td>{efileOp.user?.name}</td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );
}
