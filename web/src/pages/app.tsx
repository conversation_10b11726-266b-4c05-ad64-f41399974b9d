import { Bars3Icon } from "@heroicons/react/24/outline";
import ConfirmationPopup from "@web/src/components/confirmation";
import { slice, split } from "lodash";
import { useCallback, useState } from "react";
import { Drawer } from "react-daisyui";
import { ErrorBoundary } from "react-error-boundary";
import { Outlet, useLocation } from "react-router-dom";
import { Button } from "../components/buttons";
import ErrorPage from "./error";
import { Menu } from "./menu";

export default function App() {
  const [visible, setVisible] = useState(false);
  const toggleVisible = useCallback(() => {
    setVisible((visible) => !visible);
  }, []);
  const location = useLocation();

  return (
    <>
      <ConfirmationPopup />
      <div className="w-full h-full">
        <Drawer
          open={visible}
          onClickOverlay={toggleVisible}
          className="lg:drawer-open"
          side={
            <div
              style={{ width: 250 }}
              className="h-lvh bg-gray-100 overflow-y-auto flex flex-col overscroll-contain z-50"
            >
              <div className="bg-primary px-4 py-1">
                <img
                  src="/static/images/andor_logo_full_white.png"
                  alt="Andor Logo"
                  className="w-[180px] my-4"
                />
              </div>
              <Menu />
            </div>
          }
        >
          <div className="lg:hidden bg-primary flex items-center justify-between py-1">
            <Button onClick={toggleVisible} className="btn-ghost">
              <Bars3Icon height={32} width={32} color="#fff" />
            </Button>
            <img
              src="/static/images/andor_logo_full_white.png"
              alt="Andor Logo"
              className="mr-3 w-[150px]"
            />
          </div>
          {!window.location.hostname.startsWith("app.") && (
            <div className="bg-red-500 text-white px-4 py-3 text-center font-semibold text-lg">
              YOU ARE ON THE STAGING CASE MANAGER. DO NOT WORK ON REAL CASES
              HERE!!! Looking for the production case manager? {""}
              <a
                href="https://app.andor-law.com"
                rel="noopener noreferrer"
                className="underline hover:no-underline font-bold"
              >
                CLICK HERE
              </a>
            </div>
          )}
          <main
            className="grow h-lvh overflow-y-auto overscroll-contain"
            id="main-content-container"
          >
            <ErrorBoundary
              FallbackComponent={ErrorPage}
              resetKeys={slice(split(location.pathname, "/"), 1, 3)}
            >
              <Outlet />
            </ErrorBoundary>
          </main>
        </Drawer>
      </div>
    </>
  );
}
