import PrefillCard from "@web/src/components/prefill-card";
import axios from "axios";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { Link, useParams } from "react-router-dom";
import { useIntakeRequest } from "../../intake-requests/hooks";

export default function PrefillClioContactCard() {
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId);
  const formContext = useFormContext();

  // Track the match state
  const [clioContactId, setClioContactId] = useState<number | null>(null);

  // Get the selected contact from the form
  const selectedClioContact = formContext.watch("clioContactId");
  const isComplete = Boolean(selectedClioContact);

  // Find contact match on load
  useEffect(() => {
    (async () => {
      if (intakeRequest?.clientName && intakeRequest?.propertyName) {
        try {
          const contactMatch = await axios.get(`/api/clio/search-contacts`, {
            params: {
              clientName: intakeRequest.clientName,
              propertyName: intakeRequest.propertyName,
            },
          });
          setClioContactId(contactMatch.data.id);
        } catch (error) {
          console.error("Error searching for contact match:", error);
        }
      }
    })();
  }, [intakeRequest]);

  // Get form data from intake request
  const formData: any = intakeRequest?.formData;

  // Confirm match handler
  const confirmMatch = () => {
    if (clioContactId) {
      formContext.setValue("clioContactId", clioContactId);
    }
  };

  // Check if there's a contact match that hasn't been selected yet
  const hasMatch = Boolean(clioContactId) && !Boolean(selectedClioContact);

  return (
    <PrefillCard title="Contact" complete={isComplete} match={hasMatch}>
      <div className="flex justify-between items-start">
        <div>
          <div className="font-bold">
            {intakeRequest?.clientName} / {intakeRequest?.propertyName}
          </div>
          {formData?.ResidentInformation?.Address && (
            <div className="text-sm text-gray-500">
              {formData.ResidentInformation.Address.Line1}
              <br />
              {formData.ResidentInformation.Address.City},{" "}
              {formData.ResidentInformation.Address.State}{" "}
              {formData.ResidentInformation.Address.PostalCode}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <button
            type="button"
            className="btn btn-sm btn-primary"
            onClick={confirmMatch}
            disabled={!hasMatch}
          >
            Set as Contact
          </button>
          <Link to={"https://app.clio.com/nc/#/contacts"} target="_blank">
            <button className="w-full btn btn-sm btn-primary btn-outline">
              New Contact
            </button>
          </Link>
        </div>
      </div>
    </PrefillCard>
  );
}
