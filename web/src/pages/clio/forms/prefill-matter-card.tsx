import PrefillCard from "@web/src/components/prefill-card";
import { useSSO } from "@web/src/lib/hooks";
import axios from "axios";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useParams } from "react-router-dom";
import { useIntakeRequest } from "../../intake-requests/hooks";

// Helper method to format unit number
const formatUnitNumber = (unitNumber: string | null | undefined): string => {
  if (!unitNumber) return "";

  // Check if unit number has multiple parts (contains spaces)
  const parts = unitNumber.trim().split(/\s+/);

  if (parts.length >= 2) {
    // Multi-part unit number (e.g., "Apt 106", "Suite B") - extract only the unit identifier
    const unitIdentifier = parts[parts.length - 1]; // Get the last part
    return `, #${unitIdentifier}`;
  } else {
    // Single part unit number (e.g., "106", "B")
    return `, #${unitNumber}`;
  }
};

export default function PrefillMatterCard() {
  const { user } = useSSO();
  const { requestId } = useParams();
  const { intakeRequest } = useIntakeRequest(requestId);
  const formContext = useFormContext();

  // Track the match state
  const [clioMatterId, setClioMatterId] = useState<number | null>(null);

  // Get the selected matter and description from the form
  const [selectedClioMatter, description] = formContext.watch([
    "clioMatterId",
    "description",
  ]);
  const isComplete = Boolean(selectedClioMatter) || Boolean(description);

  // Get defendants data for related contacts prefill
  const defendants = intakeRequest?.getDefendants() || [];

  // Format defendant name for display
  const formatDefendantName = (defendant: any) => {
    let name = defendant.firstName || "";
    if (defendant.middleName) name += ` ${defendant.middleName}`;
    if (defendant.lastName) name += ` ${defendant.lastName}`;
    return name;
  };

  // Find matter match on load
  useEffect(() => {
    (async () => {
      if (
        intakeRequest?.clientName &&
        intakeRequest?.propertyName &&
        intakeRequest?.tenantName
      ) {
        try {
          const matterMatch = await axios.get(`/api/clio/search-matters`, {
            params: {
              clientName: intakeRequest.clientName,
              propertyName: intakeRequest.propertyName,
              tenantName: intakeRequest.tenantName,
              unitNumber: intakeRequest.unitNumber || null,
            },
          });
          setClioMatterId(matterMatch.data.id);
        } catch (error) {
          console.error("Error searching for matter match:", error);
        }
      }
    })();
  }, [intakeRequest]);

  // Matter prefill handler
  const prefillMatterInfo = () => {
    formContext.setValue("meta.isNewMatter", true);

    // Set matter description using tenant name and unit number
    if (intakeRequest?.tenantName) {
      const descriptionValue = `${intakeRequest.tenantName}${formatUnitNumber(
        intakeRequest.unitNumber
      )}`;
      formContext.setValue("description", descriptionValue);
    }

    const practiceAreaMap = {
      OR: "OR_LANDLORD_TENANT",
      WA: "WA_LANDLORD_TENANT",
    };

    formContext.setValue(
      "practiceArea",
      practiceAreaMap[intakeRequest?.state!] || ""
    );
    formContext.setValue("staff", user?.name);

    // Prefill related contacts with defendant data
    if (defendants.length > 0) {
      const relatedContactsData = defendants.map((defendant: any) => {
        const name = formatDefendantName(defendant);
        return {
          contactId: {
            __isNew__: true,
            name: name,
            type: "Person",
            firstName: defendant.firstName || "",
            lastName: defendant.lastName || "",
            middleName: defendant.middleName || "",
            value: name,
            label: name,
          },
          relationship: "Adverse / Tenant",
        };
      });

      formContext.setValue("relatedContacts", relatedContactsData);
    }
  };

  // Determine if there is matter data available
  const hasMatterData = Boolean(intakeRequest?.tenantName);

  // Confirm match handler
  const confirmMatch = () => {
    if (clioMatterId) {
      formContext.setValue("clioMatterId", clioMatterId);
    }
  };

  // Check if there's a matter match that hasn't been selected yet
  const hasMatch = Boolean(clioMatterId) && !Boolean(selectedClioMatter);

  return (
    <PrefillCard title="Matter" complete={isComplete} match={hasMatch}>
      <div className="flex justify-between items-star">
        <div>
          <div className="font-bold">
            {intakeRequest?.clientName} / {intakeRequest?.propertyName}-
            {intakeRequest?.tenantName}
            {formatUnitNumber(intakeRequest?.unitNumber)}
          </div>
          <div className="text-sm text-gray-500">
            Description: {intakeRequest?.tenantName}
            {formatUnitNumber(intakeRequest?.unitNumber)}
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <>
            <button
              type="button"
              className="btn btn-sm btn-primary"
              onClick={confirmMatch}
              disabled={!hasMatch}
            >
              Set as Matter
            </button>
            <button
              type="button"
              className="btn btn-sm btn-primary btn-outline"
              onClick={prefillMatterInfo}
              disabled={!hasMatterData || isComplete}
            >
              New Matter
            </button>
          </>
        </div>
      </div>
    </PrefillCard>
  );
}
