import { useQueryClient } from "@tanstack/react-query";

import axios from "axios";

import { useMutation } from "@tanstack/react-query";

export const useCreateClioContact = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (clioContact: any) => {
      const response = await axios.post("/api/clio/contacts", clioContact);
      return response.data;
    },
    mutationKey: ["clio-contacts", "create"],
    onSettled: () =>
      queryClient.invalidateQueries({ queryKey: ["clio-contacts"] }),
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useCreateClioMatter = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (matterData: any) => {
      const response = await axios.post("/api/clio/create-matter", matterData);
      return response.data;
    },
    mutationKey: ["clio-matters", "create"],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["clio-matters"] });
      queryClient.invalidateQueries({ queryKey: ["cases"] });
    },
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};
