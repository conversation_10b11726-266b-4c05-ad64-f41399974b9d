import {
  ArchiveBoxXMarkIcon,
  CheckBadgeIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { User } from "@shared/db/schema/users";
import clsx from "clsx";

export function UserStatus({ user }: { user: User }) {
  const status = user.getStatus();
  let Icon = CheckBadgeIcon;
  let bgClass = "bg-green-500";
  let textClass = "text-white";

  if (user.isArchived) {
    Icon = ArchiveBoxXMarkIcon;
    bgClass = "bg-red-100";
    textClass = "text-gray-500";
  } else if (user.isDisabled) {
    Icon = ExclamationTriangleIcon;
    bgClass = "bg-gray-100";
    textClass = "text-gray-500";
  }

  return (
    <span
      className={clsx(
        "inline-flex items-center rounded-full px-2 py-1 text-xs",
        bgClass,
        textClass
      )}
    >
      {status}
      <Icon className={clsx("ml-1 w-4", textClass)} />
    </span>
  );
}

export function EmailAuthIcon() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 5.006C1 3.898 1.893 3 2.995 3h14.01C18.107 3 19 3.897 19 5.006v9.988C19 16.102 18.107 17 17.005 17H2.995C1.893 17 1 16.103 1 14.994V5.006zM3 5l7 4 7-4v2l-7 4-7-4V5z"
        fillOpacity=".54"
        fill="#000"
        fillRule="evenodd"
      />
    </svg>
  );
}

export function MsLiveAuthIcon() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g fillRule="nonzero" fill="none">
        <path fill="#F25022" d="M0 0h9.504v9.504H0z" />
        <path fill="#7FBA00" d="M10.496 0H20v9.504h-9.504z" />
        <path fill="#00A4EF" d="M0 10.496h9.504V20H0z" />
        <path fill="#FFB900" d="M10.496 10.496H20V20h-9.504z" />
      </g>
    </svg>
  );
}
