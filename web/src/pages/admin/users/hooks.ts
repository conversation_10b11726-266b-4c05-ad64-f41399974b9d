import { User } from "@shared/db/schema/users";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useUpdateUser = (user: User | null, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) => axios.put(`/api/users/${user?.id}`, changes),

    mutationKey: ["users", user?.id],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["users"] }),
    ...options,
  });

  return { updateUser: mutateAsync, ...mutationProps };
};

export const useCreateUser = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (user: any) => {
      const response = await axios.post("/api/users", user);
      return response.data;
    },
    mutationKey: ["users", "create"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["users"] }),
    ...options,
  });

  return { createUser: mutateAsync, ...mutationProps };
};

export const useUsers = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["users", params],
    queryFn: async (): Promise<{ users: any; count: number }> => {
      const response = await axios.get("/api/users", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const users: User[] = useMemo(() => {
    if (!data?.users) return null;
    return data.users.map((user) => new User(user));
  }, [data?.users]);

  return { count: data?.count, users, ...passedProps };
};

export const useUser = (id: number, params: object = {}) => {
  const { data: userRaw, ...passedProps } = useQuery({
    queryKey: ["users", id, params],
    queryFn: async () => {
      const response = await axios.get(`/api/users/${id}`, { params });
      return response.data;
    },
    enabled: Boolean(id),
  });

  const user = useMemo(() => {
    if (!userRaw) return null;
    return new User(userRaw);
  }, [userRaw]);

  return {
    user,
    ...passedProps,
  };
};
