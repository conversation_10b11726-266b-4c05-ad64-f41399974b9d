import { <PERSON><PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Form<PERSON>rapper,
} from "@web/src/components/forms/forms";
import { ListSkeleton } from "@web/src/components/skeletons";
import { useNavigate, useParams } from "react-router-dom";
import { useUpdateUser, useUser } from "./hooks";
import UserForm from "./user-form";

export default function UserEdit() {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { user, isLoading, isSuccess } = useUser(Number(userId!), {
    decrypt: true,
  });
  const { updateUser, isPending } = useUpdateUser(user);

  if (isLoading) return <ListSkeleton />;
  if (!isSuccess || !user)
    return <h2>There was a problem finding data for this user.</h2>;

  const onSubmit = async (updates: Object) => {
    try {
      await updateUser(updates);
      navigate(`/admin/users`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader title={`Edit User: ${user.name}`} />
        <UserForm user={user} onSubmit={onSubmit}>
          <FormButtons>
            <Button className="btn-ghost" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button className="btn-primary" disabled={isPending} type="submit">
              Update User
            </Button>
          </FormButtons>
        </UserForm>
      </FormWrapper>
    </div>
  );
}
