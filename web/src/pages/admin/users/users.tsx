import {
  ArchiveBoxArrowDownIcon,
  CheckCircleIcon,
  PencilIcon,
  StopCircleIcon,
} from "@heroicons/react/24/outline";
import { USER_STATUS_OPTIONS } from "@shared/constants";
import { But<PERSON> } from "@web/src/components/buttons";
import { <PERSON>Filter, TextFilter } from "@web/src/components/forms/filters";
import { useTableWithQueryParams } from "@web/src/components/table";
import { Overlay } from "@web/src/components/utils";
import { useUrlQuery } from "@web/src/lib/hooks";
import { UserStatus } from "@web/src/pages/admin/users/components";
import { Link } from "react-router-dom";
import { useUpdateUser, useUsers } from "./hooks";

function UserRow({ user }) {
  const { updateUser } = useUpdateUser(user);

  return (
    <tr className="border-b last-of-type:border-none">
      <td>
        <Link
          className="text-blue-600 hover:underline"
          to={`/admin/users/${user.id}/edit`}
        >
          {user.name}
        </Link>
      </td>
      <td>{user.email}</td>
      <td className="text-center">
        <UserStatus user={user} />
      </td>
      <td className="text-center shrink-table-cell text-nowrap">
        <Overlay arrowColor="#fff" trigger="click">
          <Overlay.Trigger>
            <div className="bg-slate-500 text-white inline-flex items-center rounded-full px-3 py-1 text-xs">
              Update Access
              <PencilIcon className="ml-2 w-4 text-white-500" />
            </div>
          </Overlay.Trigger>
          <Overlay.Content>
            <div className="p-1 bg-white shadow-lg">
              {!user.isDisabled && (
                <Button
                  className="justify-start bg-base-100 hover:bg-red-100 flex-nowrap mb-2"
                  onClick={() => {
                    updateUser({ isDisabled: true });
                  }}
                >
                  <StopCircleIcon className="w-5 inline mr-1" />
                  <span>Disable</span>
                </Button>
              )}
              {user.isDisabled && (
                <Button
                  className="justify-start bg-base-100 hover:bg-green-100 flex-nowrap mb-2"
                  onClick={() => {
                    updateUser({ isDisabled: false });
                  }}
                >
                  <CheckCircleIcon className="w-5 inline mr-1" />
                  <span>Enable</span>
                </Button>
              )}
              {!user.isArchived && (
                <Button
                  className="justify-start bg-base-100 hover:bg-red-100 flex-nowrap"
                  onClick={() => {
                    updateUser({ isDisabled: true, isArchived: true });
                  }}
                >
                  <ArchiveBoxArrowDownIcon className="w-5 inline mr-1" />
                  <span>Archive</span>
                </Button>
              )}
              {user.isArchived && (
                <Button
                  className="justify-start bg-base-100 hover:bg-green-100 flex-nowrap"
                  onClick={() => {
                    updateUser({ isArchived: false });
                  }}
                >
                  <ArchiveBoxArrowDownIcon className="w-5 inline mr-1" />
                  <span>Restore</span>
                </Button>
              )}
            </div>
          </Overlay.Content>
        </Overlay>
      </td>
    </tr>
  );
}

export default function UserList() {
  const { params } = useUrlQuery();
  const { users, count } = useUsers(params);

  const { Table, Pager } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: count,
    data: users,
  });

  return (
    <div className="px-4 pb-4">
      <div className="navbar sticky top-0 z-20 bg-base-100 flex items-center justify-between border-slate-300 border-b">
        <h1 className="text-3xl font-light pl-3 pr-8">Users</h1>
        <div className="flex-grow flex flex-row justify-start gap-3 items-center">
          <div className="w-64">
            <TextFilter paramKey="search" placeholder="Search" />
          </div>
          <div className="w-64">
            <SelectFilter
              paramKey="status"
              placeholder="Status"
              options={USER_STATUS_OPTIONS}
              isClearable
            />
          </div>
        </div>
        <Link to="/admin/users/create" className="btn btn-primary ml-3">
          New User
        </Link>
      </div>
      <Table className="table w-full table-pin-rows">
        <thead>
          <tr className="bg-base-100" style={{ top: "4rem" }}>
            <th className="pt-4">User</th>
            <th>Email</th>
            <th className="text-center">Status</th>
            <th className="text-center">Access</th>
          </tr>
        </thead>
        <tbody>
          {users?.map((user) => (
            <UserRow user={user} key={user.id} />
          ))}
        </tbody>
      </Table>
      <Pager className="mt-5" />
    </div>
  );
}
