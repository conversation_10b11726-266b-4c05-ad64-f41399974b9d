import { TEMPLATE_STATE_OPTIONS } from "@shared/constants";
import {
  HookColorPicker,
  HookForm,
  HookInput,
  HookSecureInput,
  HookStateSelect,
  HookToggle,
} from "@web/src/components/forms/forms";
import { useForm } from "react-hook-form";

export default function UserForm({ user, onSubmit, children: buttons }) {
  const defaultValues = {
    isAdmin: user.isAdmin,
    isAttorney: user.isAttorney,
    isNonStaff: user.isNonStaff,
    name: user.name,
    initials: user.initials,
    osbNumber: user.osbNumber,
    wsbNumber: user.wsbNumber,
    email: user.email,
    fileAndServeEmail: user.fileAndServeEmail,
    fileAndServePassword: user.fileAndServePassword,
    badgeColor: user.badgeColor,
    badgeTextColor: user.badgeTextColor,
    defaultState: user.defaultState,
  };

  const formContext = useForm({ defaultValues });
  const { isAttorney } = formContext.watch();

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <div className="flex gap-3">
        <HookInput label="Name" name="name" />
        <div className="w-24">
          <HookInput label="Initials" name="initials" required />
        </div>
      </div>
      <HookInput
        label="Email"
        name="email"
        type="email"
        rules={{ required: "Email is required" }}
      />
      <div className="flex gap-3">
        <HookToggle label="Admin Access" name="isAdmin" color="success" />
        <HookToggle
          label="User is Attorney"
          name="isAttorney"
          color="success"
        />
        <HookToggle
          label="User is Non-Staff"
          name="isNonStaff"
          color="success"
        />
      </div>
      <HookInput
        label="File and Serve Email"
        name="fileAndServeEmail"
        type="email"
      />
      <HookSecureInput
        label="File and Serve Password"
        name="fileAndServePassword"
      />
      <HookStateSelect
        label="Default State"
        name="defaultState"
        options={TEMPLATE_STATE_OPTIONS}
      />
      <HookColorPicker
        label="Badge Color"
        name="badgeColor"
        updateTextColor={true}
        user={user}
      />
      {isAttorney && (
        <>
          <HookInput label="Attorney OSB Number" name="osbNumber" />
          <HookInput label="Attorney WSB Number" name="wsbNumber" />
        </>
      )}
      {buttons}
    </HookForm>
  );
}
