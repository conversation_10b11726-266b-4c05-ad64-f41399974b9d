import { But<PERSON> } from "@web/src/components/buttons";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormHeader,
  FormWrapper,
} from "@web/src/components/forms/forms";
import { useNavigate } from "react-router-dom";
import { useCreateUser } from "./hooks";
import UserForm from "./user-form";

export default function UserCreate() {
  const navigate = useNavigate();
  const { createUser, isPending } = useCreateUser();

  const user = {
    name: "",
    email: "",
    osbNumber: "",
    wsbNumber: "",
    isAttorney: false,
    isAdmin: false,
    isNonStaff: false,
  };

  const onSubmit = async (data: Object) => {
    try {
      const createdUser = await createUser({ ...user, ...data });
      navigate(`/admin/users/${createdUser.id}/edit`);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div className="p-4 flex w-full">
      <FormWrapper>
        <FormHeader title="New User" />
        <UserForm user={user} onSubmit={onSubmit}>
          <FormButtons>
            <Button className="btn-ghost" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button className="btn-primary" disabled={isPending} type="submit">
              Create User
            </Button>
          </FormButtons>
        </UserForm>
      </FormWrapper>
    </div>
  );
}
