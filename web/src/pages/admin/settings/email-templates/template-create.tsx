import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { Button } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import { useCreateEmailTemplate } from "./hooks";
import TemplateForm from "./template-form";

export default function EmailTemplateCreate() {
  const navigate = useNavigate();
  const { createTemplate, isPending } = useCreateEmailTemplate();
  const defaultValues = {
    name: "",
    purpose: "",
    subject: "",
    body: "",
    to: [],
    cc: [],
    bcc: [],
  };

  const onSubmit = async ({ to, cc, bcc, ...formData }) => {
    try {
      await createTemplate({
        ...formData,
        to: to.map((email) => email.email),
        cc: cc.map((email) => email.email),
        bcc: bcc.map((email) => email.email),
      });
      navigate(-1);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">New Email Template</h1>
      </div>
      <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
        <TemplateForm />
        <FormButtons>
          <Button className="btn-ghost" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button className="btn-primary" disabled={isPending} type="submit">
            Create Email Template
          </Button>
        </FormButtons>
      </HookForm>
    </div>
  );
}
