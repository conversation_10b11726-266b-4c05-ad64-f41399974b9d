import { Button } from "@web/src/components/buttons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useBaseData } from "@web/src/lib/hooks";
import { Link, useNavigate } from "react-router-dom";

export default function EmailTemplateSettings() {
  const navigate = useNavigate();
  const { data } = useBaseData();
  const templates = data?.emailTemplates;
  const { Table } = useTableWithQueryParams({
    data: templates,
  });

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1">
        <h1 className="text-3xl font-light">Email Templates</h1>
        <div>
          <Link to="create" className="btn btn-sm btn-primary">
            Add Template
          </Link>
        </div>
      </div>
      <Table className="table table-sm">
        <thead>
          <tr>
            <th className="bg-white">Subject</th>
            <th className="bg-white">Purpose</th>
            <th className="bg-white">Audience</th>
            <th className="bg-white"></th>
          </tr>
        </thead>
        <tbody>
          {templates &&
            templates.map((template) => {
              return (
                <tr key={template.id} className="even:bg-gray-50 odd:bg-white">
                  <td>
                    <span>{template.subject}</span>
                  </td>
                  <td>
                    <span>{template.purpose}</span>
                  </td>
                  <td>
                    <span>{template.audience}</span>
                  </td>
                  <td>
                    <div className="flex justify-end">
                      <Button
                        className="btn-primary btn-xs"
                        onClick={() => navigate(`${template.id}/edit`)}
                      >
                        Edit
                      </Button>
                    </div>
                  </td>
                </tr>
              );
            })}
        </tbody>
      </Table>
    </div>
  );
}
