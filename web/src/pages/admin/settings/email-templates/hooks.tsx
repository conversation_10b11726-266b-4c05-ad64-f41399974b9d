import { EmailTemplate } from "@shared/db/schema/email-templates";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
export const useCreateEmailTemplate = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (templateData: any) =>
      axios.post(`/api/email-templates`, templateData),
    mutationKey: ["email-templates"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["base-data"] }),
    ...options,
  });

  return { createTemplate: mutateAsync, ...mutationProps };
};

export const useUpdateEmailTemplate = (
  emailTemplate: EmailTemplate,
  options: object = {}
) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) =>
      axios.put(`/api/email-templates/${emailTemplate.id}`, changes),
    mutationKey: ["email-templates", emailTemplate.id],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["base-data"] }),
    ...options,
  });

  return { updateTemplate: mutateAsync, ...mutationProps };
};
