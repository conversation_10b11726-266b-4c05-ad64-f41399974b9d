import { EmailTemplate } from "@shared/db/schema/email-templates";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { Button } from "react-daisyui";
import { useNavigate, useParams } from "react-router-dom";
import { useUpdateEmailTemplate } from "./hooks";
import TemplateForm from "./template-form";

export default function EmailTemplateEdit() {
  const navigate = useNavigate();
  const { templateId } = useParams();
  const { data } = useBaseData();
  const templates = data?.emailTemplates;
  const template = templates?.find(
    (template) => template.id === Number(templateId)
  );
  const { updateTemplate, isPending } = useUpdateEmailTemplate(
    new EmailTemplate(template)
  );
  const defaultValues = {
    audience: template.audience || "",
    name: template.name || "",
    body: template.body || "",
    purpose: template.purpose || "",
    subject: template.subject || "",
    type: template.type || "",
    to: template.to?.map((email) => ({ email })) || [],
    cc: template.cc?.map((email) => ({ email })) || [],
    bcc: template.bcc?.map((email) => ({ email })) || [],
  };

  const onSubmit = async ({ to, cc, bcc, ...formData }) => {
    try {
      await updateTemplate({
        ...formData,
        to: to.map((email) => email.email),
        cc: cc.map((email) => email.email),
        bcc: bcc.map((email) => email.email),
      });
      navigate(-1);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">
          Edit Email Template: {template.name}
        </h1>
      </div>
      <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
        <TemplateForm />
        <FormButtons>
          <Button
            className="btn-ghost"
            onClick={() => navigate(`/admin/settings/email-templates`)}
          >
            Cancel
          </Button>
          <Button className="btn-primary" disabled={isPending} type="submit">
            Update Email Template
          </Button>
        </FormButtons>
      </HookForm>
    </div>
  );
}
