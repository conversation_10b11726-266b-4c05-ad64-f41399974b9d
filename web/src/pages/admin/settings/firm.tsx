import { CheckCircleIcon } from "@heroicons/react/24/outline";
import { <PERSON><PERSON> } from "@web/src/components/buttons";
import { HookForm, HookInput } from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { useForm } from "react-hook-form";
import { useUpdateSetting } from "./hooks";

export default function FirmSettings() {
  const { data } = useBaseData();
  const { updateSetting, isSuccess } = useUpdateSetting("firm_data");

  const defaultValues = { ...data.firm_data };
  const formContext = useForm({ defaultValues });
  const isSubmitting = formContext.formState.isSubmitting;

  const onSubmit = async (data) => {
    try {
      await updateSetting(data);
    } catch (error) {}
  };

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <div className="flex items-center justify-between mb-3 pb-1 border-b border-slate-300">
        <h1 className="text-3xl font-light">Firm Settings</h1>
        <div className="flex items-center">
          {isSuccess && (
            <CheckCircleIcon
              style={{ width: 24 }}
              className="text-green-500 ml-3"
            />
          )}
          <Button
            className="btn-primary btn-sm ml-3"
            disabled={isSubmitting}
            type="submit"
          >
            Save
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-3">
        <div>
          <HookInput label="Name" name="firmName" />
          <HookInput label="Phone" name="firmPhone" />
          <HookInput label="Fax" name="firmFax" />
          <HookInput label="Address" name="firmAddress" />
          <HookInput label="City, State, Zip" name="firmCityStateZip" />
        </div>
      </div>
    </HookForm>
  );
}
