import { CheckCircleIcon } from "@heroicons/react/24/outline";
import { <PERSON><PERSON> } from "@web/src/components/buttons";
import { JsonEditor } from "@web/src/components/forms/json-editor";
import axios from "axios";
import { useState } from "react";

// Constant dummy PDF link that will always work for testing
const DUMMY_PDF_LINK =
  "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf";

export default function DebugTool() {
  const [jsonData, setJsonData] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [isSubmittingDebug, setIsSubmittingDebug] = useState(false);
  const [activeSample, setActiveSample] = useState(0);

  const handleDebugSubmit = async () => {
    try {
      setIsSubmittingDebug(true);
      setSubmitSuccess(false);
      setSubmitError("");

      const endpoint = "/api/intake/process-oregon-eviction-form";

      const parsedData = JSON.parse(jsonData);
      await axios.post(endpoint, parsedData);
      setSubmitSuccess(true);
    } catch (error: any) {
      console.error("Error submitting debug form:", error);
      setSubmitError(
        error.message || "An error occurred while submitting the form"
      );
    } finally {
      setIsSubmittingDebug(false);
    }
  };

  const handleTest = async () => {
    try {
      const response = await axios.get(`/api/clio/test`);
      console.log(response.data);
    } catch (error: any) {
      console.error("Error submitting debug form:", error);
    }
  };

  // Placeholder for sample data - you can replace these with your actual sample data
  // Note: All file links use DUMMY_PDF_LINK to ensure they remain accessible for testing
  // Randall
  const sampleData1 = JSON.stringify(
    {
      Form: {
        Id: "11",
        InternalName: "PraeceTestingOregonEvictionRequest",
        Name: "Praece Testing - Oregon Eviction Request",
      },
      RequiredDocumentsToAttachForReview: {
        FullLeaseAgreementWithAddenda: [],
        CompleteLedger: [],
        AffordableHousing: "Unsure / Please Advise",
        AffordableHousingOptions: [],
        OtherpleaseSpecify: null,
        HUD: [],
        OtherUnsure: null,
        DoNotHave: ["Full Lease Agreement with Addenda"],
        DoNotHave2: ["Complete Ledger"],
        FederallyBacked: "Unsure / Please Advise",
        AllRentIncreaseNotices: [],
        SubsidyDocuments: [],
        VAWAForms: [],
        CopyOfTerminationNotice: null,
        NotApplicableDoNotHave: ["All Rent Increase Notices"],
        TerminationNotices: [
          {
            ContentType: "application/pdf",
            Id: "F-jYbjoO5Qz2GsjMP9toq4rE",
            IsEncrypted: true,
            Name: "_Additional Documents - 1.pdf",
            Size: 261769,
            StorageUrl: null,
            File: DUMMY_PDF_LINK,
          },
        ],
        LastPayment: null,
        TerminationDate: "2025-04-15",
        FullLeaseAgreementWithAddenda_IsRequired: false,
        CompleteLedger_IsRequired: false,
        AffordableHousingOptions_IsRequired: false,
        OtherpleaseSpecify_IsRequired: false,
        HUD_IsRequired: false,
        OtherUnsure_IsRequired: false,
        AllRentIncreaseNotices_IsRequired: false,
        SubsidyDocuments_IsRequired: false,
        VAWAForms_IsRequired: false,
        CopyOfTerminationNotice_IsRequired: false,
      },
      ResidentInformation: {
        Address: {
          City: "Multnomah",
          CityStatePostalCode: "Multnomah, Oregon 97803",
          Country: "United States",
          CountryCode: "US",
          FullAddress: "25 16th St NE, Multnomah, Oregon 97803",
          FullInternationalAddress:
            "25 16th St NE, Multnomah, Oregon 97803, United States",
          Latitude: null,
          Line1: "25 16th St NE",
          Line2: "",
          Line3: null,
          Longitude: null,
          PostalCode: "97803",
          State: "Oregon",
          StreetAddress: "25 16th St NE",
          Type: "Home",
        },
        ReasonToBelieve: "No",
        SelectAllThatApply: [],
        SelectAllThatApply_Amount: 0,
        SelectAllThatApply_QuantitySelected: 1,
        SelectAllThatApply_Price: 0,
        Other: null,
        Residents18: [
          {
            Id: "3USAkE",
            Name: "Randall Custler",
            DateOfBirth: "2025-04-08",
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "Yes",
            ItemNumber: 1,
            DateOfBirth_IsRequired: true,
            SocialSecurityNumber_IsRequired: true,
          },
          {
            Id: "YDPMd",
            Name: "Chloe Sanchez",
            DateOfBirth: null,
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "No",
            ItemNumber: 2,
            DateOfBirth_IsRequired: false,
            SocialSecurityNumber_IsRequired: true,
          },
          {
            Id: "31T4VI",
            Name: "Tessa McNaught",
            DateOfBirth: "2025-04-09",
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "Yes",
            ItemNumber: 3,
            DateOfBirth_IsRequired: true,
            SocialSecurityNumber_IsRequired: true,
          },
        ],
        Residents18_Minimum: 1,
        SelectAllThatApply_IsRequired: false,
        Other_IsRequired: false,
      },
      NameOfSubmitter: {
        First: "Justin",
        FirstAndLast: "Justin Aebi",
        Last: "Aebi",
        Middle: null,
        MiddleInitial: null,
        Prefix: null,
        Suffix: null,
      },
      DateSubmitted: "2025-04-16",
      SaveAndResumeEmail: "<EMAIL>",
      PropertyInformation: {
        Property: "McKenzie Real Estate Partners",
        Unit: "782",
        ManagementCompany: "Acacia Property Group",
        Manager: "Justin Aebi",
        Email: "<EMAIL>",
        DirectPhone: "(*************",
        County: "Multnomah",
        SecuredPropertyAccessInstructions: null,
        SecuredProperty: "No",
        TitleDeed: "Justin Aebi",
        NamesAndEmailUpdates: [
          {
            Id: "3CDj7W",
            Name: {
              First: null,
              FirstAndLast: " ",
              Last: null,
              Middle: null,
              MiddleInitial: null,
              Prefix: null,
              Suffix: null,
            },
            Email: null,
            ItemNumber: 1,
          },
        ],
        NamesAndEmailUpdates_Maximum: 6,
        PayStay: null,
        BehavioralConditions: "Bad person",
        AdditionalTerms: null,
        CC1: "",
        CC2: "",
        CC3: "",
        CC4: "",
        CC5: "",
        CC6: "",
        FirstAppearanceSettlementTerms: "Behavioral Conditions",
        CourtAppearance: null,
        SecuredPropertyAccessInstructions_IsRequired: false,
        BehavioralConditions_IsRequired: true,
        CourtAppearance_IsRequired: false,
      },
      TenantName: "Randall Custler",
      Comments: null,
      AdditionalDocuments: [],
      Entry: {
        AdminLink: "https://www.cognitoforms.com/AndorLaw2/11/entries/16",
        DateCreated: "2025-04-16T20:41:41.532Z",
        DateSubmitted: new Date().toISOString(),
        DateUpdated: "2025-04-22T16:09:17.234Z",
        Number: 16,
        Order: null,
        Origin: {
          City: null,
          CountryCode: null,
          IpAddress: "*************",
          IsImported: false,
          Region: null,
          Timezone: null,
          UserAgent:
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
        Timestamp: "2025-04-16T20:41:41.532Z",
        User: {
          Email: "<EMAIL>",
          Name: "Praece Flow",
        },
        Version: 5,
        Action: "Submit",
        Role: "Internal",
        Status: "Submitted",
        PublicLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#AaXRT29xtIIxV4fJ-mqWjqzzpnfnXKCjE7w-RfIeAZ8$*",
        InternalLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#1bcoeJh3ZaRctbJ5iQLLmObDH0KP5DTysW1X0m9nMq4$*",
        Document3: DUMMY_PDF_LINK,
      },
      Id: "11-16",
    },
    null,
    2
  );
  // Chloe
  const sampleData2 = JSON.stringify(
    {
      Form: {
        Id: "11",
        InternalName: "PraeceTestingOregonEvictionRequest",
        Name: "Praece Testing - Oregon Eviction Request",
      },
      RequiredDocumentsToAttachForReview: {
        FullLeaseAgreementWithAddenda: [],
        CompleteLedger: [],
        AffordableHousing: "Unsure / Please Advise",
        AffordableHousingOptions: [],
        OtherpleaseSpecify: null,
        HUD: [],
        OtherUnsure: null,
        DoNotHave: ["Full Lease Agreement with Addenda"],
        DoNotHave2: ["Complete Ledger"],
        FederallyBacked: "Unsure / Please Advise",
        AllRentIncreaseNotices: [],
        SubsidyDocuments: [],
        VAWAForms: [],
        CopyOfTerminationNotice: null,
        NotApplicableDoNotHave: ["All Rent Increase Notices"],
        TerminationNotices: [
          {
            ContentType: "application/pdf",
            Id: "F-6kWc4tSM2Yiw5DtqtOjQdE",
            IsEncrypted: true,
            Name: "_Additional Documents - 1.pdf",
            Size: 261769,
            StorageUrl: null,
            File: DUMMY_PDF_LINK,
          },
        ],
        LastPayment: null,
        TerminationDate: "2025-04-08",
        FullLeaseAgreementWithAddenda_IsRequired: false,
        CompleteLedger_IsRequired: false,
        AffordableHousingOptions_IsRequired: false,
        OtherpleaseSpecify_IsRequired: false,
        HUD_IsRequired: false,
        OtherUnsure_IsRequired: false,
        AllRentIncreaseNotices_IsRequired: false,
        SubsidyDocuments_IsRequired: false,
        VAWAForms_IsRequired: false,
        CopyOfTerminationNotice_IsRequired: false,
      },
      ResidentInformation: {
        Address: {
          City: "Wilsonville",
          CityStatePostalCode: "Wilsonville, Oregon 97062",
          Country: "United States",
          CountryCode: "US",
          FullAddress: "25 16th Street Ne, 226, Wilsonville, Oregon 97062",
          FullInternationalAddress:
            "25 16th Street Ne, 226, Wilsonville, Oregon 97062, United States",
          Latitude: null,
          Line1: "25 16th Street Ne, 226",
          Line2: "",
          Line3: null,
          Longitude: null,
          PostalCode: "97062",
          State: "Oregon",
          StreetAddress: "25 16th Street Ne, 226",
          Type: "Home",
        },
        ReasonToBelieve: "No",
        SelectAllThatApply: [],
        SelectAllThatApply_Amount: 0,
        SelectAllThatApply_QuantitySelected: 1,
        SelectAllThatApply_Price: 0,
        Other: null,
        Residents18: [
          {
            Id: "1gXvLq",
            Name: "Chloe Alferez",
            DateOfBirth: "2025-04-02",
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "Yes",
            ItemNumber: 1,
            DateOfBirth_IsRequired: true,
            SocialSecurityNumber_IsRequired: true,
          },
          {
            Id: "3kApwt",
            Name: "Samwell Tully",
            DateOfBirth: "2025-04-01",
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "Yes",
            ItemNumber: 2,
            DateOfBirth_IsRequired: true,
            SocialSecurityNumber_IsRequired: true,
          },
        ],
        Residents18_Minimum: 1,
        SelectAllThatApply_IsRequired: false,
        Other_IsRequired: false,
      },
      NameOfSubmitter: {
        First: "Justin",
        FirstAndLast: "Justin Aebi",
        Last: "Aebi",
        Middle: null,
        MiddleInitial: null,
        Prefix: null,
        Suffix: null,
      },
      DateSubmitted: "2025-04-08",
      SaveAndResumeEmail: "<EMAIL>",
      PropertyInformation: {
        Property: "Southgate Management Group",
        Unit: "226",
        ManagementCompany: "SummitStone Realty",
        Manager: "Testa",
        Email: "<EMAIL>",
        DirectPhone: "(*************",
        County: "Multnomah",
        SecuredPropertyAccessInstructions: null,
        SecuredProperty: "No",
        TitleDeed: "Justin Aebi",
        NamesAndEmailUpdates: [
          {
            Id: "3jJ3Dq",
            Name: {
              First: null,
              FirstAndLast: " ",
              Last: null,
              Middle: null,
              MiddleInitial: null,
              Prefix: null,
              Suffix: null,
            },
            Email: null,
            ItemNumber: 1,
          },
        ],
        NamesAndEmailUpdates_Maximum: 6,
        PayStay: null,
        BehavioralConditions: null,
        AdditionalTerms: null,
        CC1: "",
        CC2: "",
        CC3: "",
        CC4: "",
        CC5: "",
        CC6: "",
        FirstAppearanceSettlementTerms: "Must Vacate",
        CourtAppearance: "No - Management will send their own representative",
        SecuredPropertyAccessInstructions_IsRequired: false,
        BehavioralConditions_IsRequired: false,
        CourtAppearance_IsRequired: false,
      },
      TenantName: "Chloe Alferez",
      Comments: null,
      AdditionalDocuments: [],
      Entry: {
        AdminLink: "https://www.cognitoforms.com/AndorLaw2/11/entries/15",
        DateCreated: "2025-04-08T19:44:29.948Z",
        DateSubmitted: new Date().toISOString(),
        DateUpdated: "2025-04-22T16:09:10.040Z",
        Number: 15,
        Order: null,
        Origin: {
          City: null,
          CountryCode: null,
          IpAddress: "*************",
          IsImported: false,
          Region: null,
          Timezone: null,
          UserAgent:
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
        Timestamp: "2025-04-08T19:44:29.948Z",
        User: {
          Email: "<EMAIL>",
          Name: "Praece Flow",
        },
        Version: 8,
        Action: "Submit",
        Role: "Internal",
        Status: "Submitted",
        PublicLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#csh19c0zGiPugyNNthy1UscXVIggzWLwffvoSFMlIZ8$*",
        InternalLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#AnBggqI-DBF70rLsO9ifh7_Xt1Vpqam3-QCz7UaFH_A$*",
        Document3: DUMMY_PDF_LINK,
      },
      Id: "11-15",
    },
    null,
    2
  );
  //Kendrick
  const sampleData3 = JSON.stringify(
    {
      Form: {
        Id: "11",
        InternalName: "PraeceTestingOregonEvictionRequest",
        Name: "Praece Testing - Oregon Eviction Request",
      },
      RequiredDocumentsToAttachForReview: {
        FullLeaseAgreementWithAddenda: [],
        CompleteLedger: [],
        AffordableHousing: "Unsure / Please Advise",
        AffordableHousingOptions: [],
        OtherpleaseSpecify: null,
        HUD: [],
        OtherUnsure: null,
        DoNotHave: ["Full Lease Agreement with Addenda"],
        DoNotHave2: ["Complete Ledger"],
        FederallyBacked: "Yes",
        AllRentIncreaseNotices: [],
        SubsidyDocuments: [],
        VAWAForms: [],
        CopyOfTerminationNotice: null,
        NotApplicableDoNotHave: ["All Rent Increase Notices"],
        TerminationNotices: [
          {
            ContentType: "application/pdf",
            Id: "F-qmR$y9ucG24xMdPat5usLE",
            IsEncrypted: true,
            Name: "_Additional Documents - 1.pdf",
            Size: 261769,
            StorageUrl: null,
            File: DUMMY_PDF_LINK,
          },
        ],
        LastPayment: null,
        TerminationDate: "2025-04-01",
        FullLeaseAgreementWithAddenda_IsRequired: false,
        CompleteLedger_IsRequired: false,
        AffordableHousingOptions_IsRequired: false,
        OtherpleaseSpecify_IsRequired: false,
        HUD_IsRequired: false,
        OtherUnsure_IsRequired: false,
        AllRentIncreaseNotices_IsRequired: false,
        SubsidyDocuments_IsRequired: false,
        VAWAForms_IsRequired: false,
        CopyOfTerminationNotice_IsRequired: false,
      },
      ResidentInformation: {
        Address: {
          City: "Banks",
          CityStatePostalCode: "Banks, Oregon 97106",
          Country: "United States",
          CountryCode: "US",
          FullAddress: "25 16th St NE, Banks, Oregon 97106",
          FullInternationalAddress:
            "25 16th St NE, Banks, Oregon 97106, United States",
          Latitude: null,
          Line1: "25 16th St NE",
          Line2: "",
          Line3: null,
          Longitude: null,
          PostalCode: "97106",
          State: "Oregon",
          StreetAddress: "25 16th St NE",
          Type: "Home",
        },
        ReasonToBelieve: "No",
        SelectAllThatApply: [],
        SelectAllThatApply_Amount: 0,
        SelectAllThatApply_QuantitySelected: 1,
        SelectAllThatApply_Price: 0,
        Other: null,
        Residents18: [
          {
            Id: "2rzNwA",
            Name: "Kendrick James",
            DateOfBirth: "2010-04-07",
            SocialSecurityNumber: "***********",
            SocialSecurityNumberOnFile: "Yes",
            DateOfBirthOnFile: "Yes",
            ItemNumber: 1,
            DateOfBirth_IsRequired: true,
            SocialSecurityNumber_IsRequired: true,
          },
        ],
        Residents18_Minimum: 1,
        SelectAllThatApply_IsRequired: false,
        Other_IsRequired: false,
      },
      NameOfSubmitter: {
        First: "Justin",
        FirstAndLast: "Justin Aebi",
        Last: "Aebi",
        Middle: null,
        MiddleInitial: null,
        Prefix: null,
        Suffix: null,
      },
      DateSubmitted: "2025-04-02",
      SaveAndResumeEmail: "<EMAIL>",
      PropertyInformation: {
        Property: "Chapman Investment Associates",
        Unit: "1200",
        ManagementCompany: "Acorn Realty Services",
        Manager: "Justin",
        Email: "<EMAIL>",
        DirectPhone: "(*************",
        County: "Washington",
        SecuredPropertyAccessInstructions: null,
        SecuredProperty: "No",
        TitleDeed: "Justin",
        NamesAndEmailUpdates: [
          {
            Id: "GXKPC",
            Name: {
              First: null,
              FirstAndLast: " ",
              Last: null,
              Middle: null,
              MiddleInitial: null,
              Prefix: null,
              Suffix: null,
            },
            Email: null,
            ItemNumber: 1,
          },
        ],
        NamesAndEmailUpdates_Maximum: 6,
        PayStay: null,
        BehavioralConditions: null,
        AdditionalTerms: null,
        CC1: "",
        CC2: "",
        CC3: "",
        CC4: "",
        CC5: "",
        CC6: "",
        FirstAppearanceSettlementTerms: "Must Vacate",
        CourtAppearance: null,
        SecuredPropertyAccessInstructions_IsRequired: false,
        BehavioralConditions_IsRequired: false,
        CourtAppearance_IsRequired: false,
      },
      TenantName: "Kendrick James",
      Comments: null,
      AdditionalDocuments: [],
      Entry: {
        AdminLink: "https://www.cognitoforms.com/AndorLaw2/11/entries/14",
        DateCreated: "2025-04-02T23:36:10.171Z",
        DateSubmitted: new Date().toISOString(),
        DateUpdated: "2025-04-22T16:09:00.209Z",
        Number: 14,
        Order: null,
        Origin: {
          City: null,
          CountryCode: null,
          IpAddress: "*************",
          IsImported: false,
          Region: null,
          Timezone: null,
          UserAgent:
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
        Timestamp: "2025-04-02T23:36:10.171Z",
        User: {
          Email: "<EMAIL>",
          Name: "Praece Flow",
        },
        Version: 7,
        Action: "Submit",
        Role: "Internal",
        Status: "Submitted",
        PublicLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#6vgMUTgjMdjCatt3Z9G_Cm62jDa7BputYAkV1LHrnSM$*",
        InternalLink:
          "https://www.cognitoforms.com/AndorLaw2/PraeceTestingOregonEvictionRequest#xTgKW8ET0orK2Ekt5yzygx9KJPoc-Cn7X5SupG8b3k4$*",
        Document3: DUMMY_PDF_LINK,
      },
      Id: "11-14",
    },
    null,
    2
  );

  return (
    <div>
      <div className="flex items-center mb-4">
        <h1 className="text-3xl">Oregon Eviction Request Debug Tool</h1>
      </div>

      <div
        className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4"
        role="alert"
      >
        <p className="font-bold">Development Tool Only</p>
        <p>
          This tool is for development and testing purposes only. It allows you
          to simulate Cognito webhook submissions to the intake controller.
        </p>
        <p className="mt-2">
          To use: Select a sample dataset or edit the JSON directly, then click
          "Submit Debug Form".
        </p>
        <p className="mt-2">
          Note: Sample data uses a dummy PDF link that will always work for
          testing purposes.
        </p>
      </div>

      <p className="mb-4 text-gray-700">
        Use this tool to test Oregon Eviction Request form submissions by
        directly sending JSON data to the intake endpoint. This simulates a
        webhook request from Cognito.
      </p>

      <div className="mb-4">
        <div className="flex items-center mb-3">
          <label className="block text-sm font-medium text-gray-700 mr-4">
            Sample Data:
          </label>
          <div className="flex space-x-2">
            <Button
              className={`btn-sm ${
                activeSample === 1 ? "btn-primary" : "btn-outline"
              }`}
              onClick={() => {
                setActiveSample(1);
                setJsonData(sampleData1);
              }}
              type="button"
            >
              Randall Custler
            </Button>
            <Button
              className={`btn-sm ${
                activeSample === 2 ? "btn-primary" : "btn-outline"
              }`}
              onClick={() => {
                setActiveSample(2);
                setJsonData(sampleData2);
              }}
              type="button"
            >
              Chloe Alferez
            </Button>
            <Button
              className={`btn-sm ${
                activeSample === 3 ? "btn-primary" : "btn-outline"
              }`}
              onClick={() => {
                setActiveSample(3);
                setJsonData(sampleData3);
              }}
              type="button"
            >
              Kendrick James
            </Button>
          </div>
        </div>
        <div className="mb-1">
          <label className="block text-sm font-medium text-gray-700">
            JSON Data
          </label>
        </div>
        <JsonEditor
          value={jsonData}
          onChange={(value) => {
            setJsonData(value);
            setActiveSample(0); // Reset active sample when user edits the JSON
          }}
          height="400px"
        />
      </div>

      <div className="flex items-center mb-4">
        <Button
          className="btn-primary"
          disabled={isSubmittingDebug || !jsonData}
          onClick={handleDebugSubmit}
          type="button"
        >
          {isSubmittingDebug ? "Submitting..." : "Submit Debug Form"}
        </Button>

        {submitSuccess && (
          <div className="flex items-center text-green-500 ml-3">
            <CheckCircleIcon style={{ width: 24 }} className="mr-1" />
            <span>Form submitted successfully</span>
          </div>
        )}

        {submitError && <div className="text-red-500 ml-3">{submitError}</div>}
      </div>
      <Button onClick={handleTest}>Test</Button>
    </div>
  );
}
