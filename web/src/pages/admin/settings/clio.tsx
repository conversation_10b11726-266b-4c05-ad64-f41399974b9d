import {
  BookmarkIcon,
  CheckCircleIcon,
  FolderIcon,
} from "@heroicons/react/24/outline";
import { But<PERSON> } from "@web/src/components/buttons";
import { HookForm, HookInput } from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { useForm } from "react-hook-form";
import { useUpdateSetting } from "./hooks";

export default function FirmSettings() {
  const { data } = useBaseData();
  const { updateSetting, isSuccess } = useUpdateSetting("clio");

  const defaultValues = { ...data.clio };
  const formContext = useForm({ defaultValues });
  const isSubmitting = formContext.formState.isSubmitting;

  const onSubmit = async (data) => {
    try {
      await updateSetting(data);
    } catch (error) {}
  };

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <div className="flex items-center justify-between mb-3 pb-1 border-b border-slate-300">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-light">Clio Settings</h1>
          <div className="flex items-center gap-2">
            <a href="/api/clio/auth" className="btn btn-sm btn-primary">
              Auth
            </a>
            {data?.clio?.accountName && (
              <span className="text-sm text-gray-600">
                Connected to:{" "}
                <span className="font-medium">{data.clio.accountName}</span>
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center">
          {isSuccess && (
            <CheckCircleIcon
              style={{ width: 24 }}
              className="text-green-500 ml-3"
            />
          )}
          <Button
            className="btn-primary btn-sm ml-3"
            disabled={isSubmitting}
            type="submit"
          >
            Save
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <div className="flex items-center gap-2">
            <FolderIcon width={24} height={24} />
            <h2 className="text-xl">Folder IDs</h2>
          </div>
          <HookInput label="Oregon" name="folders.Oregon" />
          <HookInput label="Washington" name="folders.Washington" />
          <HookInput label="Notices" name="folders.Notices" />
          <HookInput label="Intake" name="folders.intake" />
        </div>
        <div>
          <div className="flex items-center gap-2">
            <BookmarkIcon width={24} height={24} />
            <h2 className="text-xl">Custom Fields</h2>
          </div>
          <HookInput label="Subsidies Picklist" name="picklist.subsidies.id" />
          <ul className="ml-3 mb-4 pl-3 border-l-primary border-l-2">
            <li>
              <HookInput label="Yes" name="picklist.subsidies.yes" />
            </li>
            <li>
              <HookInput label="No" name="picklist.subsidies.no" />
            </li>
            <li>
              <HookInput label="Unknown" name="picklist.subsidies.unknown" />
            </li>
          </ul>
          <HookInput
            label="Opposing Counsel Picklist"
            name="picklist.opposingCounsel.id"
          />
          <ul className="ml-3 pl-3 border-l-primary border-l-2">
            <li>
              <HookInput label="Yes" name="picklist.opposingCounsel.yes" />
            </li>
            <li>
              <HookInput label="No" name="picklist.opposingCounsel.no" />
            </li>
          </ul>
          <HookInput
            label="Conflict Search Performed By"
            name="textOneLine.conflictSearchPerformedBy.id"
          />
          <HookInput
            label="No Conflict Found"
            name="checkbox.noConflictFound.id"
          />
        </div>
      </div>
    </HookForm>
  );
}
