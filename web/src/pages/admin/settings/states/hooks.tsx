import { State } from "@shared/db/schema/states";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
export const useCreateState = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (stateData: any) => axios.post(`/api/states`, stateData),
    mutationKey: ["states"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["base-data"] }),
    ...options,
  });

  return { createState: mutateAsync, ...mutationProps };
};

export const useUpdateState = (state: State, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) => axios.put(`/api/states/${state.id}`, changes),
    mutationKey: ["states", state.id],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["base-data"] }),
    ...options,
  });

  return { updateState: mutateAsync, ...mutationProps };
};
