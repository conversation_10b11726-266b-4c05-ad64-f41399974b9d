import { Button } from "@web/src/components/buttons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useBaseData } from "@web/src/lib/hooks";
import { Link, useNavigate } from "react-router-dom";

export default function StateSettings() {
  const navigate = useNavigate();
  const { data } = useBaseData();
  const states = data?.states;
  const { Table } = useTableWithQueryParams({
    data: states,
  });

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1">
        <h1 className="text-3xl font-light">States</h1>
        <div>
          <Link to="create" className="btn btn-sm btn-primary">
            Add State
          </Link>
        </div>
      </div>
      <Table className="table table-sm">
        <thead>
          <tr>
            <th className="bg-white">Full Name</th>
            <th className="bg-white">Abbreviation</th>
            <th className="bg-white"></th>
          </tr>
        </thead>
        <tbody>
          {states.map((state) => {
            return (
              <tr key={state.id} className="even:bg-gray-50 odd:bg-white">
                <td className="text-base">
                  <span>{state.fullName}</span>
                </td>
                <td>
                  <span>{state.name}</span>
                </td>
                <td>
                  <div className="flex justify-end">
                    <Button
                      className="btn-primary btn-xs"
                      onClick={() => navigate(`${state.id}/edit`)}
                    >
                      Edit
                    </Button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );
}
