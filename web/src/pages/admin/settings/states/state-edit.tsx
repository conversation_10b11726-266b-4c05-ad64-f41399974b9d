import { State } from "@shared/db/schema/states";
import { Button } from "@web/src/components/buttons";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { useNavigate, useParams } from "react-router-dom";
import { useUpdateState } from "./hooks";
import StateForm from "./state-form";

export default function StateEdit() {
  const navigate = useNavigate();
  const { stateId } = useParams();
  const { data } = useBaseData();
  const states = data?.states;
  const state = states?.find((state) => state.id === Number(stateId));
  const { updateState, isPending } = useUpdateState(new State(state));
  const defaultValues = {
    fullName: state.fullName || "",
    name: state.name || "",
  };
  const onSubmit = async (stateFormData) => {
    try {
      await updateState(stateFormData);
      navigate(-1);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">Edit State: {state.name}</h1>
      </div>
      <div className="grid grid-cols-3 gap-3">
        <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
          <StateForm>
            <FormButtons>
              <Button className="btn-ghost" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary"
                disabled={isPending}
                type="submit"
              >
                Update State
              </Button>
            </FormButtons>
          </StateForm>
        </HookForm>
      </div>
    </div>
  );
}
