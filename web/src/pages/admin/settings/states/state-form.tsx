import { HookInput } from "@web/src/components/forms/forms";

export default function StateForm({ children: buttons }: { children?: any }) {
  return (
    <>
      <HookInput
        name="fullName"
        label="Full Name"
        placeholder="Full Name"
        rules={{ required: "Full name is required" }}
      />
      <HookInput
        name="name"
        label="Abbreviation"
        placeholder="Abbreviation"
        rules={{ required: "Abbreviation is required" }}
      />
      {buttons}
    </>
  );
}
