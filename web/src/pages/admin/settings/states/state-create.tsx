import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { Button } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import { useCreateState } from "./hooks";
import StateForm from "./state-form";

export default function StateCreate() {
  const navigate = useNavigate();
  const { createState, isPending } = useCreateState();
  const defaultValues = { fullName: "", name: "" };

  const onSubmit = async (stateFormData) => {
    try {
      await createState(stateFormData);
      navigate(-1);
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };
  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">New State</h1>
      </div>
      <div className="grid grid-cols-3 gap-3">
        <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
          <StateForm>
            <FormButtons>
              <Button className="btn-ghost" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary"
                disabled={isPending}
                type="submit"
              >
                Create State
              </Button>
            </FormButtons>
          </StateForm>
        </HookForm>
      </div>
    </div>
  );
}
