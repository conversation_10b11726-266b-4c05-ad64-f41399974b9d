import { But<PERSON> } from "@web/src/components/buttons";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useNavigate, useParams } from "react-router-dom";
import { useTemplate, useUpdateTemplate } from "./hooks";
import TemplateForm from "./template-form";

export default function TemplateEdit() {
  const { templateId } = useParams();
  const { template } = useTemplate(templateId);
  const navigate = useNavigate();
  const { update: updateTemplate, isPending } = useUpdateTemplate(template!);

  if (!template) return null;

  const defaultValues = {
    id: template.id || null,
    name: template.name || "",
    state: template.state || "",
    downloadFormat: template.downloadFormat || "",
    county: template.county || "",
    courtType: template.courtType || "",
    category: template.category || "",
    customFields: template.customFields || [],
  };

  const onSubmit = async (templateFormData) => {
    try {
      await updateTemplate(templateFormData);
      navigate(`/admin/settings/templates/${template.id}/view`, {
        state: { from: templateFormData.state },
      });
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">Edit Template: {template.name}</h1>
      </div>
      <div style={{ maxWidth: 650 }}>
        <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
          <TemplateForm>
            <FormButtons>
              <Button className="btn-ghost btn-sm" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary btn-sm"
                disabled={isPending}
                type="submit"
              >
                Update Template
              </Button>
            </FormButtons>
          </TemplateForm>
        </HookForm>
      </div>
    </div>
  );
}
