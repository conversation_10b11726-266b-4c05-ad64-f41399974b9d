import { But<PERSON> } from "@web/src/components/buttons";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useUrlQuery } from "@web/src/lib/hooks";
import { useNavigate } from "react-router-dom";
import { useCreateTemplate, useTemplate } from "./hooks";
import TemplateForm from "./template-form";

export default function TemplateCopy() {
  const navigate = useNavigate();
  const { params } = useUrlQuery();
  const copyTemplateId = String(params?.templateId);
  const { template, isFetching } = useTemplate(copyTemplateId);
  const { create: createTemplate, isPending } = useCreateTemplate();
  const defaultValues = {
    name: template?.name || "",
    state: template?.state || "",
    courtType: template?.courtType || "",
    category: template?.category || "",
    downloadFormat: template?.downloadFormat || "",
    customFields: template?.customFields || [],
  };

  const onSubmit = async (templateFormData) => {
    try {
      const { template } = await createTemplate(templateFormData);
      navigate(`/admin/settings/templates/${template.id}/view`, {
        state: { from: template.state },
      });
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">New Template</h1>
      </div>
      <div style={{ maxWidth: 650 }}>
        {!isFetching && (
          <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
            <TemplateForm>
              <FormButtons>
                <Button className="btn-ghost" onClick={() => navigate(-1)}>
                  Cancel
                </Button>
                <Button
                  className="btn-primary"
                  disabled={isPending}
                  type="submit"
                >
                  Create Template
                </Button>
              </FormButtons>
            </TemplateForm>
          </HookForm>
        )}
      </div>
    </div>
  );
}
