import { Template } from "@shared/db/schema/templates";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useCreateTemplate = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (template: any) => {
      const response = await axios.post("/api/templates", template);
      return response.data;
    },
    mutationKey: ["templates"],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
    },
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useUpdateTemplate = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (template: any) => {
      const response = await axios.put("/api/templates", template);
      return response.data;
    },
    mutationKey: ["templates"],
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
    },
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useTemplates = (params: object = {}) => {
  const { data, ...passedProps } = useQuery({
    queryKey: ["templates", params],
    queryFn: async (): Promise<{ templates: any }> => {
      const response = await axios.get("/api/templates", { params });
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const templates: Template[] = useMemo(() => {
    if (!data?.templates) return null;
    return data.templates.map((template) => new Template(template));
  }, [data?.templates]);

  return {
    templates,
    ...passedProps,
  };
};

export const useTemplate = (id: string | undefined) => {
  const { data: templateRaw, ...passedProps } = useQuery({
    queryKey: ["templates", id],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/templates/${id}`);
      return response.data;
    },
    enabled: Boolean(id),
  });

  const template = useMemo(() => {
    if (!templateRaw) return null;
    return new Template(templateRaw);
  }, [templateRaw]);

  return {
    template,
    ...passedProps,
  };
};
