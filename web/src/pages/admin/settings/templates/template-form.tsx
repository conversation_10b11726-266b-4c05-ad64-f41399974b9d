import {
  CUSTOM_FIELD_TYPE_OPTIONS,
  OR_COURT_TYPE_OPTIONS,
  TEMPLATE_CATEGORIES,
  TEMPLATE_DOWNLOAD_FORMATS,
  TEMPLATE_STATE_OPTIONS,
} from "@shared/constants";
import { Button } from "@web/src/components/buttons";
import { Placeholder } from "@web/src/components/content";
import {
  HookCountySelect,
  HookInput,
  HookSelect,
} from "@web/src/components/forms/forms";
import { useFieldArray, useFormContext } from "react-hook-form";

export default function TemplateForm({ children: buttons }) {
  const formContext = useFormContext();
  const state = formContext.watch("state");
  const courtType = formContext.watch("courtType");
  const { fields, append, remove } = useFieldArray({
    control: formContext.control,
    name: "customFields",
  });
  const isOregon = state === "OR";
  const hasCourtType = courtType && courtType !== "undefined";

  return (
    <div>
      <HookInput name="name" placeholder="Name" required />
      <HookSelect
        placeholder="Select State . . ."
        options={TEMPLATE_STATE_OPTIONS}
        name="state"
        required
      />
      <HookSelect
        placeholder="Select Download Format . . ."
        options={TEMPLATE_DOWNLOAD_FORMATS}
        name="downloadFormat"
        required
      />
      {isOregon || hasCourtType ? (
        <HookSelect
          placeholder="Select Court Type (Optional) . . ."
          options={OR_COURT_TYPE_OPTIONS}
          name="courtType"
        />
      ) : null}
      <HookSelect
        placeholder="Select Category . . ."
        options={TEMPLATE_CATEGORIES}
        name="category"
        required
      />
      <HookCountySelect
        state={state}
        name={"county"}
        placeholder="Select County (Optional) . . ."
      />
      {fields && (
        <div>
          <div className="flex justify-between items-center py-1 mb-2 border-b border-b-slate-300">
            <h2 className="text-lg">Custom Fields</h2>
            <div>
              <Button
                type="button"
                onClick={() =>
                  append({
                    key: "",
                    label: "",
                    type: "",
                  })
                }
                className="btn-xs btn-primary"
              >
                Add Custom Field
              </Button>
            </div>
          </div>
          {fields.length === 0 && (
            <Placeholder className="my-3" minHeight={100}>
              no custom fields
            </Placeholder>
          )}
          {fields.map((field, index) => (
            <div key={field.id} className="mb-1">
              <div className="flex gap-3">
                <HookInput
                  name={`customFields.${index}.key`}
                  placeholder="Key"
                />
                <HookInput
                  name={`customFields.${index}.label`}
                  placeholder="Label"
                />
                <HookSelect
                  name={`customFields.${index}.type`}
                  options={CUSTOM_FIELD_TYPE_OPTIONS}
                  placeholder="Type"
                />
                <Button
                  onClick={() => remove(index)}
                  className="btn-outline btn-secondary"
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
      {buttons}
    </div>
  );
}
