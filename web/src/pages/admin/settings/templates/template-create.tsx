import { TEMPLATE_DEFAULTS } from "@shared/constants";
import { Button } from "@web/src/components/buttons";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useNavigate } from "react-router-dom";
import { useCreateTemplate } from "./hooks";
import TemplateForm from "./template-form";

export default function TemplateCreate() {
  const navigate = useNavigate();
  const { create: createTemplate, isPending } = useCreateTemplate();
  const defaultValues = { ...TEMPLATE_DEFAULTS };
  const onSubmit = async (templateFormData) => {
    try {
      const { template } = await createTemplate(templateFormData);
      navigate(`/admin/settings/templates/${template.id}/view`, {
        state: { from: template.state },
      });
    } catch (error) {
      console.log(`-------------error--------------`, error);
    }
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">New Template</h1>
      </div>
      <div style={{ maxWidth: 650 }}>
        <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
          <TemplateForm>
            <FormButtons>
              <Button className="btn-ghost" onClick={() => navigate(-1)}>
                Cancel
              </Button>
              <Button
                className="btn-primary"
                disabled={isPending}
                type="submit"
              >
                Create Template
              </Button>
            </FormButtons>
          </TemplateForm>
        </HookForm>
      </div>
    </div>
  );
}
