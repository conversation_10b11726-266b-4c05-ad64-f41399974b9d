import { Tab, Tabs } from "@web/src/components/layout";
import { ListSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useBaseData } from "@web/src/lib/hooks";
import { capitalize, times } from "lodash";
import { Link, useParams } from "react-router-dom";

export default function AllTemplates() {
  return (
    <>
      <Tabs height="3rem" hideStart hideEnd>
        <Tab style={{ gridColumnStart: 1 }} className="!pl-0 !pr-8">
          <h1 className="text-3xl font-light">Templates</h1>
        </Tab>
        <Tab to="/admin/settings/templates/OR" end={false}>
          Oregon
        </Tab>
        <Tab to="/admin/settings/templates/WA" end={false}>
          Washington
        </Tab>
        <Tab />
        <Tab className="!pr-0">
          <Link to="create" className="btn btn-sm btn-primary">
            Add Template
          </Link>
        </Tab>
      </Tabs>
    </>
  );
}

export function TemplateTable() {
  const { state } = useParams();
  const currentState = state || "OR";
  const { data, isFetching } = useBaseData();
  if (!data) return null;
  const templates = data.templatesByState[currentState!];
  const { Table } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: templates?.length || 0,
    data: templates,
  });

  if (!templates) return null;

  return (
    <Table className="table table-sm">
      <thead>
        <tr>
          <th className="bg-white">Name</th>
          <th className="bg-white">Details</th>
          <th className="bg-white"></th>
        </tr>
      </thead>
      <tbody>
        {templates.length > 0 &&
          templates.map((template, index) => {
            return (
              <tr key={template.id} className="even:bg-gray-50 odd:bg-white">
                <td>{template.name}</td>
                <td>
                  <span>
                    {template.state}{" "}
                    {template.courtType &&
                      `${capitalize(template.courtType)} Court`}
                    {template.county && ` - ${template.county} County`}
                  </span>
                </td>
                <td>
                  <div className="flex gap-2 justify-end">
                    <Link
                      to={`/admin/settings/templates/${template.id}/edit`}
                      className="btn-xs border rounded-md bg-primary text-white"
                    >
                      Edit
                    </Link>
                    <Link
                      to={`/admin/settings/templates/copy?templateId=${template.id}`}
                      className="btn-xs border rounded-md bg-primary text-white"
                    >
                      New From Copy
                    </Link>
                  </div>
                </td>
              </tr>
            );
          })}

        {isFetching &&
          templates.length == 0 &&
          times(30, (index) => (
            <tr>
              <td></td>
              <td>
                <ListSkeleton />
              </td>
              <td></td>
            </tr>
          ))}
      </tbody>
    </Table>
  );
}
