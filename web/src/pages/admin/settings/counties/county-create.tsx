import { convertDateToTimeString } from "@shared/utils";
import { But<PERSON> } from "@web/src/components/buttons";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import CountyForm from "./county-form";
import { useCreateCounty } from "./hooks";

export default function CountyCreate() {
  const navigate = useNavigate();
  const defaultValues = {
    name: "",
    state: "",
    courtName: "",
  };
  const formContext = useForm({ defaultValues });
  const { create: createCounty, isPending } = useCreateCounty();

  const onSubmit = async ({ justiceCourt, alternateCourt, ...formData }) => {
    const newCounty = {
      ...formData,
      faTime: convertDateToTimeString(formData.faTime),
      justiceCourt: justiceCourt && {
        ...justiceCourt,
        faTime: convertDateToTimeString(justiceCourt.faTime),
      },
      alternateCourt: alternateCourt && {
        ...alternateCourt,
        faTime: convertDateToTimeString(alternateCourt.faTime),
        zips: alternateCourt.zips.map((zip) => zip.zip),
      },
    };

    const county = await createCounty(newCounty);
    navigate(`/admin/settings/counties/${county.id}/view`, {
      state: { from: county.state },
    });
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">New County</h1>
      </div>
      <HookForm onSubmit={onSubmit} context={formContext}>
        <CountyForm />
        <FormButtons>
          <Button className="btn-ghost" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button className="btn-primary" disabled={isPending} type="submit">
            Add County
          </Button>
        </FormButtons>
      </HookForm>
    </div>
  );
}
