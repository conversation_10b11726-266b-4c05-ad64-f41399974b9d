import {
  convertDateToTimeString,
  convertTimeStringToDate,
} from "@shared/utils";
import { FormButtons, HookForm } from "@web/src/components/forms/forms";
import { Button } from "react-daisyui";
import { useNavigate, useParams } from "react-router-dom";
import CountyForm from "./county-form";
import { useCounty, useEditCounty } from "./hooks";

export default function CountyEdit() {
  const navigate = useNavigate();
  const { countyId } = useParams();
  const { county } = useCounty(countyId);
  const { update: updateCounty, isPending } = useEditCounty();
  if (!county) return null;

  const defaultValues = {
    id: county.id || null,
    name: county.name || "",
    state: county.state || "",
    courtName: county.courtName || "",
    courtAddress: county.courtAddress || "",
    faTime: convertTimeStringToDate(county.faTime),
    defaultCourtType: county.defaultCourtType || "circuit",
    justiceCourt: county.justiceCourt && {
      ...county.justiceCourt,
      faTime: convertTimeStringToDate(county.justiceCourt?.faTime),
    },
    alternateCourt: county.alternateCourt && {
      ...county.alternateCourt,
      faTime: convertTimeStringToDate(county.alternateCourt?.faTime),
      zips: county.alternateCourt?.zips?.map((zip) => ({ zip })) || [],
    },
  };

  const onSubmit = async ({ justiceCourt, alternateCourt, ...formData }) => {
    const updatedCounty = {
      ...formData,
      faTime: convertDateToTimeString(formData.faTime),
      justiceCourt: justiceCourt && {
        ...justiceCourt,
        faTime: convertDateToTimeString(justiceCourt.faTime),
      },
      alternateCourt: alternateCourt && {
        ...alternateCourt,
        faTime: convertDateToTimeString(alternateCourt.faTime),
        zips: alternateCourt.zips.map((zip) => zip.zip),
      },
    };

    await updateCounty(updatedCounty);
    navigate(-1);
  };

  return (
    <div>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">Edit County: {county.name}</h1>
      </div>
      <HookForm onSubmit={onSubmit} defaultValues={defaultValues}>
        <CountyForm />
        <FormButtons>
          <Button className="btn-ghost" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button className="btn-primary" disabled={isPending} type="submit">
            Update County
          </Button>
        </FormButtons>
      </HookForm>
    </div>
  );
}
