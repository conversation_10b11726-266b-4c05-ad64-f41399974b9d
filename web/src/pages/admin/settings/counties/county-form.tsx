import { OR_COURT_TYPE_OPTIONS } from "@shared/constants";
import { Button } from "@web/src/components/buttons";
import { Placeholder } from "@web/src/components/content";
import {
  HookDatePicker,
  HookInput,
  HookLimitedStateSelect,
  HookSelect,
} from "@web/src/components/forms/forms";
import { capitalize } from "lodash";
import { useFieldArray, useFormContext } from "react-hook-form";

export default function CountyForm() {
  const formContext = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control: formContext.control,
    name: "alternateCourt.zips",
  });
  const errors = formContext.formState.errors;
  const [state] = formContext.watch(["state"]);
  const isOregon = state === "OR";
  const isWashington = state === "WA";
  let mainCourt = "";
  if (isOregon) mainCourt = "circuit";
  if (isWashington) mainCourt = "superior";
  const secondaryCourt = "justice";

  return (
    <div className="grid grid-cols-2 gap-6">
      <div>
        <HookInput
          name="name"
          label="County Name"
          placeholder="county name"
          rules={{ required: "County name is required" }}
        />
        <HookLimitedStateSelect
          name="state"
          label="County State"
          rules={{ required: "County state is required" }}
        />
        {isOregon && (
          <HookSelect
            label="Default Court"
            options={OR_COURT_TYPE_OPTIONS}
            name="defaultCourtType"
          />
        )}

        <div>
          <label className="label py-1">
            <span className="label-text">
              {capitalize(mainCourt)} Courthouse Info
            </span>
          </label>
          <HookInput
            name="courtName"
            placeholder="Name"
            rules={{ required: "Courthouse name is required" }}
          />
          <HookInput
            name="courtAddress"
            placeholder="Address"
            rules={{ required: "court address is required" }}
          />
          <HookDatePicker
            name="faTime"
            placeholder="FA Time"
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            timeCaption="Time"
            dateFormat="h:mm aa"
          />
        </div>

        {isOregon && (
          <div>
            <label className="label py-1">
              <span className="label-text">
                {capitalize(secondaryCourt)} Courthouse Info
              </span>
            </label>
            <HookInput
              name={`${secondaryCourt}Court.courtName`}
              placeholder="Name"
            />
            <HookInput
              name={`${secondaryCourt}Court.address`}
              placeholder="Address"
            />

            <HookDatePicker
              name={`${secondaryCourt}Court.faTime`}
              placeholder="FA Time"
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={15}
              timeCaption="Time"
              dateFormat="h:mm aa"
            />
          </div>
        )}
      </div>
      <div>
        <div>
          <label className="label py-1">
            <span className="label-text">
              Alternate {capitalize(mainCourt)} Courthouse Info
            </span>
          </label>
          <HookInput name="alternateCourt.courtName" placeholder="Name" />
          <HookInput name="alternateCourt.address" placeholder="Address" />
          <HookDatePicker
            name="alternateCourt.faTime"
            placeholder="FA Time"
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            timeCaption="Time"
            dateFormat="h:mm aa"
          />
        </div>

        {fields && (
          <div>
            <div className="flex justify-between items-center py-1 mt-3 mb-2 border-b border-b-slate-300">
              <h2 className="text-lg">Alternate Court Zip Codes</h2>
              <div>
                <Button
                  onClick={() => append({ zip: "" })}
                  className="btn-xs btn-primary"
                >
                  Add Alternate Court Zip
                </Button>
              </div>
            </div>
          </div>
        )}

        {fields.length === 0 && (
          <Placeholder className="my-3" minHeight={100}>
            no zip codes
          </Placeholder>
        )}

        {fields.map((field, index) => (
          <div key={field.id} className="mb-1">
            <div className="flex gap-3">
              <HookInput
                name={`alternateCourt.zips.${index}.zip`}
                placeholder="Zip Code"
                rules={{
                  pattern: {
                    value: /^\d+$/,
                    message: "Zip codes must be numbers",
                  },
                  minLength: {
                    message: "Zip codes must be 5 characters long",
                    value: 5,
                  },
                  maxLength: {
                    message: "Zip codes must be 5 characters long",
                    value: 5,
                  },
                }}
              />
              <Button
                onClick={() => remove(index)}
                className="btn-outline btn-secondary"
              >
                Remove
              </Button>
            </div>
            <p>{errors.root?.message}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
