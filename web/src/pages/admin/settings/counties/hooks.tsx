import { County } from "@shared/db/schema/counties";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const useCreateCounty = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (county: any) => {
      const response = await axios.post("/api/counties", county);
      return response.data;
    },
    mutationKey: ["counties"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["counties"] }),
    ...options,
  });

  return { create: mutateAsync, ...mutationProps };
};

export const useEditCounty = (options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: async (county: any) => {
      const response = await axios.put("/api/counties", county);
      return response.data;
    },
    mutationKey: ["counties"],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["counties"] }),
    ...options,
  });

  return { update: mutateAsync, ...mutationProps };
};

export const useCounty = (id: string | undefined) => {
  const { data: countyRaw, ...passedProps } = useQuery({
    queryKey: ["counties", id],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/counties/${id}`);
      return response.data;
    },
    enabled: Boolean(id),
  });

  const county = useMemo(() => {
    if (!countyRaw) return null;
    return new County(countyRaw);
  }, [countyRaw]);

  return {
    county,
    ...passedProps,
  };
};
