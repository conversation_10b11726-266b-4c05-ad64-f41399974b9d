import { Tab, Tabs } from "@web/src/components/layout";
import { ListSkeleton } from "@web/src/components/skeletons";
import { useTableWithQueryParams } from "@web/src/components/table";
import { useBaseData } from "@web/src/lib/hooks";
import { capitalize, times } from "lodash";
import { Link, useParams } from "react-router-dom";

export default function AllCounties() {
  return (
    <>
      <Tabs height="3rem" hideStart hideEnd>
        <Tab style={{ gridColumnStart: 1 }} className="!pl-0 !pr-8">
          <h1 className="text-3xl font-light">Counties</h1>
        </Tab>
        <Tab to="/admin/settings/counties/OR" end={false}>
          Oregon
        </Tab>
        <Tab to="/admin/settings/counties/WA" end={false}>
          Washington
        </Tab>
        <Tab />
        <Tab className="!pr-0">
          <Link to="create" className="btn btn-sm btn-primary">
            Add County
          </Link>
        </Tab>
      </Tabs>
    </>
  );
}

export function CountyTable() {
  const { state } = useParams();
  const { data, isFetching } = useBaseData();
  if (!data) return null;

  const countiesByState = data.counties.filter(
    (county) => county.state === state
  );
  const { Table } = useTableWithQueryParams({
    defaultSort: "name",
    totalRecordCount: countiesByState?.length || 0,
    data: countiesByState,
  });

  return (
    <Table className="table table-sm">
      <thead>
        <tr>
          <th className="bg-white">Name</th>
          <th className="bg-white">Default Court</th>
          <th className="bg-white"></th>
        </tr>
      </thead>
      <tbody>
        {countiesByState.length > 0 &&
          countiesByState.map((county) => {
            return (
              <tr key={county.id} className="even:bg-gray-50 odd:bg-white">
                <td>{county.name}</td>
                <td>
                  <span>
                    {county.defaultCourtType &&
                      `${capitalize(county.defaultCourtType)} Court`}
                  </span>
                </td>
                <td className="text-right">
                  <Link
                    to={`/admin/settings/counties/${county.id}/edit`}
                    className="btn btn-xs btn-primary"
                  >
                    Edit
                  </Link>
                </td>
              </tr>
            );
          })}

        {isFetching &&
          countiesByState.length == 0 &&
          times(30, (index) => (
            <tr>
              <td></td>
              <td>
                <ListSkeleton />
              </td>
              <td></td>
            </tr>
          ))}
      </tbody>
    </Table>
  );
}
