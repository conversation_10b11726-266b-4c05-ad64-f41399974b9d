import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

export const useUpdateSetting = (key: string, options: object = {}) => {
  const queryClient = useQueryClient();
  const { mutateAsync, ...mutationProps } = useMutation({
    mutationFn: (changes: any) => axios.put(`/api/settings/${key}`, changes),
    mutationKey: ["setting", key],
    onSettled: () => queryClient.invalidateQueries({ queryKey: ["base-data"] }),
    ...options,
  });

  return { updateSetting: mutateAsync, ...mutationProps };
};

export const useSetting = (key: string | undefined) => {
  const { data: setting, ...passedProps } = useQuery({
    queryKey: ["setting", key],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(`/api/settings/${key}`);
      return response.data;
    },
    enabled: <PERSON><PERSON><PERSON>(key),
  });
  return {
    setting,
    ...passedProps,
  };
};
