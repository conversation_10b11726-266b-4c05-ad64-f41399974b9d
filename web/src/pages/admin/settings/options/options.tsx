import { InformationCircleIcon } from "@heroicons/react/24/outline";
import { useBaseData } from "@web/src/lib/hooks";
import { startCase, toLower } from "lodash";
import { Link } from "react-router-dom";

export default function OptionSettings() {
  const { data } = useBaseData();

  return (
    <>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">Types, Terms & Statuses</h1>
      </div>
      <OptionsView dataKey="first_appearance_terms" data={data} />
      <OptionsView dataKey="judgment_type" data={data} />
      <OptionsView dataKey="stipulation_type" data={data} />
      <OptionsView dataKey="residence_type" data={data} />
      <OptionsView dataKey="lease_type" data={data} />
      <OptionsView dataKey="notice_types" data={data} />
      <OptionsView dataKey="defendant_status" data={data} />
    </>
  );
}

function OptionsView({ dataKey, data }: { dataKey: string; data: any }) {
  return (
    <div className="mb-5">
      <div className="flex align-items justify-between w-96">
        <div className="flex">
          <InformationCircleIcon height={24} width={24} className="mr-4" />
          <h2 className="text-lg">{startCase(toLower(dataKey))}</h2>
        </div>
        <Link
          to={`/admin/settings/options/${dataKey}/edit`}
          className="btn btn-xs btn-primary ml-4"
        >
          Edit
        </Link>
      </div>
      <div className="ml-3 pl-7 mt-1 py-1 border-l border-slate-400">
        {data[dataKey]?.map((option) => (
          <div key={Math.random()}>{option}</div>
        ))}
      </div>
    </div>
  );
}
