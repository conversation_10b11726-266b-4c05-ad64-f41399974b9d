import { Button } from "@web/src/components/buttons";
import { Placeholder } from "@web/src/components/content";
import {
  FormButtons,
  HookForm,
  HookInput,
} from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { startCase, toLower } from "lodash";
import { useFieldArray, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useUpdateSetting } from "../hooks";

export default function OptionEdit() {
  const navigate = useNavigate();
  const { key } = useParams();
  const settingName = startCase(toLower(key));
  const { data } = useBaseData();
  const { updateSetting } = useUpdateSetting(key!);
  const defaultValues = {
    value: data[key!].map((option) => ({
      option,
    })),
  };
  const formContext = useForm({ defaultValues });
  const isSubmitting = formContext.formState.isSubmitting;

  const { fields, append, remove } = useFieldArray({
    control: formContext.control,
    name: "value",
  });

  const onSubmit = async ({ value }) => {
    const options = value.map((option) => option.option);
    await updateSetting(options);
    navigate(-1);
  };

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <div className="flex justify-between border-slate-300 border-b pb-1 mb-3">
        <h1 className="text-3xl font-light">{settingName}</h1>
        <div>
          <Button
            onClick={() => append(`New ${settingName}`)}
            className="btn-sm btn-primary"
          >
            Add
          </Button>
        </div>
      </div>
      <div>
        {fields.length === 0 && (
          <Placeholder className="my-3" minHeight={100}>
            no settings
          </Placeholder>
        )}

        {fields.map((field, index) => (
          <div key={field.id} className="mb-1 flex gap-3">
            <HookInput name={`value.${index}.option`} />
            <Button
              onClick={() => remove(index)}
              className="btn-outline btn-secondary"
            >
              Remove
            </Button>
          </div>
        ))}
      </div>
      <FormButtons>
        <Button
          className="btn-ghost"
          onClick={() => navigate("/admin/settings/options")}
        >
          Cancel
        </Button>
        <Button
          className="btn-primary ml-3"
          disabled={isSubmitting}
          type="submit"
        >
          Save
        </Button>
      </FormButtons>
    </HookForm>
  );
}
