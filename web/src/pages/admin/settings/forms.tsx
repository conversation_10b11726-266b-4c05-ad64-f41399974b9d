import {
  AtSymbolIcon,
  CheckCircleIcon,
  ClipboardDocumentIcon,
} from "@heroicons/react/24/outline";
import { Button } from "@web/src/components/buttons";
import { HookForm, HookInput } from "@web/src/components/forms/forms";
import { useBaseData } from "@web/src/lib/hooks";
import { useForm } from "react-hook-form";
import { useUpdateSetting } from "./hooks";

export default function FirmSettings() {
  const { data } = useBaseData();
  const { updateSetting, isSuccess } = useUpdateSetting("cognito");

  const defaultValues = { ...data.cognito };
  const formContext = useForm({ defaultValues });
  const isSubmitting = formContext.formState.isSubmitting;

  const onSubmit = async (data) => {
    try {
      await updateSetting(data);
    } catch (error) {}
  };

  return (
    <HookForm context={formContext} onSubmit={onSubmit}>
      <div className="flex items-center justify-between mb-3 pb-1 border-b border-slate-300">
        <h1 className="text-3xl font-light">Intake Form Settings</h1>
        <div className="flex items-center">
          {isSuccess && (
            <CheckCircleIcon
              style={{ width: 24 }}
              className="text-green-500 ml-3"
            />
          )}
          <Button
            className="btn-primary btn-sm ml-3"
            disabled={isSubmitting}
            type="submit"
          >
            Save
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-5">
        <div>
          <div className="flex items-center gap-2">
            <AtSymbolIcon width={24} height={24} />
            <h2 className="text-xl">Emails</h2>
          </div>
          <HookInput
            label="Notice Request"
            name="emails.notice_request_email"
          />
          <HookInput
            label="Oregon Eviction Request"
            name="emails.oregon_eviction_request_email"
          />
          <HookInput
            label="Oregon Eviction Follow Up"
            name="emails.oregon_eviction_follow_up_email"
          />
          <HookInput
            label="Washington Eviction Request"
            name="emails.washington_eviction_request_email"
          />
          <HookInput
            label="Washington Eviction Follow Up"
            name="emails.washington_eviction_follow_up_email"
          />
          <HookInput
            label="Pickup Request"
            name="emails.pickup_request_email"
          />
        </div>
        <div>
          <div className="flex items-center gap-2">
            <ClipboardDocumentIcon width={24} height={24} />
            <h2 className="text-xl">Form IDs</h2>
          </div>
          <HookInput label="Notice Request" name="form_ids.notice_request" />
          <HookInput
            label="Oregon Eviction Request"
            name="form_ids.oregon_eviction_request"
          />
          <HookInput
            label="Oregon Eviction Follow Up"
            name="form_ids.oregon_eviction_follow_up"
          />
          <HookInput
            label="Washington Eviction Request"
            name="form_ids.washington_eviction_request"
          />
          <HookInput
            label="Washington Eviction Follow Up"
            name="form_ids.washington_eviction_follow_up"
          />
          <HookInput label="Pickup Request" name="form_ids.pickup_request" />
        </div>
      </div>
    </HookForm>
  );
}
