import {
  AtSymbolIcon,
  BriefcaseIcon,
  BugAntIcon,
  BuildingOfficeIcon,
  DocumentDuplicateIcon,
  InboxArrowDownIcon,
  MapPinIcon,
  WrenchIcon,
} from "@heroicons/react/24/outline";
import { VerticalTab, VerticalTabs } from "@web/src/components/layout";
import { useBaseData } from "@web/src/lib/hooks";

export default function Setting() {
  const { data } = useBaseData();

  if (!data) return null;

  return (
    <div className="p-4">
      <VerticalTabs>
        <VerticalTab to="/admin/settings/firm" label="Firm">
          <BuildingOfficeIcon color="#2980b9" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/clio" label="Clio">
          <img src="/static/images/clio.png" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/forms" label="Forms">
          <InboxArrowDownIcon color="#27ae60" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/states" label="States">
          <MapPinIcon color="#c0392b" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/counties" label="Counties">
          <BriefcaseIcon color="#f39c12" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/templates" label="Docs">
          <DocumentDuplicateIcon color="#0e7490" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/email-templates" label="Emails">
          <AtSymbolIcon color="#34495e" />
        </VerticalTab>
        <VerticalTab to="/admin/settings/options" label="Options">
          <WrenchIcon color="#8e44ad" />
        </VerticalTab>
        {data.isDebugMode && (
          <VerticalTab to="/admin/settings/debug-tool" label="Debug">
            <BugAntIcon color="#e74c3c" />
          </VerticalTab>
        )}
      </VerticalTabs>
    </div>
  );
}
