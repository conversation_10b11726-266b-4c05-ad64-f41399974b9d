import classNames from "classnames";
import { useEffect, useState } from "react";
import {
  InfoSidebar,
  RequestForm,
  ServiceSelection,
  StateSelection,
} from "./components";

export default function ClientIntakePage() {
  const [selectedState, setSelectedState] = useState<string | null>(null);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load saved state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem("selectedState");
    if (savedState) {
      setSelectedState(savedState);
      setCurrentStep(2);
    }
  }, []);

  // Save selected state to localStorage
  useEffect(() => {
    if (selectedState) {
      localStorage.setItem("selectedState", selectedState);
    }
  }, [selectedState]);

  // Add beforeunload event handler
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // NOTE: This message isn't used in modern browsers, but is required
        const message =
          "You have unsaved changes. Are you sure you want to leave?";
        event.preventDefault();
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges, currentStep]);

  // Handle state selection
  const handleStateSelect = (state: string) => {
    setSelectedState(state);
    setCurrentStep(2);
  };

  // Handle service selection
  const handleServiceSelect = (service: string) => {
    setSelectedService(service);
    setCurrentStep(3);
  };

  // Handle state change
  const resetState = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You may have unsaved changes that will be lost if you change states now. Are you sure you want to continue?"
      );
      if (!confirmed) return;
      setHasUnsavedChanges(false);
    }

    localStorage.removeItem("selectedState");
    setCurrentStep(1);
  };

  // Handle service change
  const resetService = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You may have unsaved changes that will be lost if you change services now. Are you sure you want to continue?"
      );
      if (!confirmed) return;
      setHasUnsavedChanges(false);
    }

    setCurrentStep(2);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex min-h-screen flex-col px-4 py-4 md:px-8 md:py-8 mx-auto">
        {/* Header section */}
        <header className="mb-8 md:mb-12">
          <h1 className="mb-4 flex items-center text-3xl md:text-4xl text-gray-800">
            <img
              src="/static/images/andor_logo_full_black.png"
              alt="Andor Logo"
              className="w-[200px]"
            />
          </h1>
          <p className="max-w-3xl pl-1 text-lg md:text-xl text-gray-600">
            We're here to help with your tenant issue.
            <br />
            Tell us a bit about what you need, and we'll get started.
          </p>
        </header>

        {/* Client intake steps */}
        <div className="flex flex-col lg:flex-row w-full gap-8 lg:gap-12">
          <div className="w-full lg:max-w-[800px]">
            <ol>
              {/* Step 1 */}
              <li
                className={classNames(
                  "mb-6 flex transition-[font-size]",
                  currentStep === 1
                    ? "text-4xl font-bold items-top"
                    : "text-2xl font-semibold opacity-60 items-center"
                )}
              >
                <div className="mr-1 text-right" style={{ minWidth: 35 }}>
                  1.
                </div>
                <div className="flex-grow text-base font-normal pl-3">
                  {currentStep === 1 ? (
                    <>
                      <div className="text-4xl font-light mb-3">
                        Select the state where the property is located
                      </div>
                      <div className="w-full">
                        <StateSelection handleStateSelect={handleStateSelect} />
                      </div>
                    </>
                  ) : (
                    <div
                      className="border-2 border-gray-300 hover:border-primary bg-white rounded-lg px-4 py-2 cursor-pointer hover:shadow-lg transition w-full"
                      onClick={resetState}
                      aria-label="Change selected state"
                    >
                      Selected state:{" "}
                      <span className="font-semibold">{selectedState}</span>{" "}
                      (click to change)
                    </div>
                  )}
                </div>
              </li>

              {/* Step 2 */}
              <li
                className={classNames(
                  "mb-6 flex transition-[font-size]",
                  currentStep === 2
                    ? "text-4xl font-bold items-top"
                    : "text-2xl font-semibold opacity-60 items-center"
                )}
              >
                <div className="mr-1 text-right" style={{ minWidth: 35 }}>
                  2.
                </div>
                <div className="flex-grow text-base font-normal pl-3">
                  {currentStep <= 2 ? (
                    <>
                      <div
                        className={classNames(
                          "font-light",
                          currentStep === 2 ? "text-4xl mb-3" : "text-2xl"
                        )}
                      >
                        Select the service you need
                      </div>
                      {currentStep === 2 && (
                        <div className="w-full">
                          <ServiceSelection
                            handleServiceSelect={handleServiceSelect}
                            selectedState={selectedState}
                          />
                        </div>
                      )}
                    </>
                  ) : (
                    <div
                      className="border-2 border-gray-300 hover:border-primary bg-white rounded-lg px-4 py-2 cursor-pointer hover:shadow-lg transition w-full"
                      onClick={resetService}
                      aria-label="Change selected service"
                    >
                      Selected service:{" "}
                      <span className="font-semibold">{selectedService}</span>{" "}
                      (click to change)
                    </div>
                  )}
                </div>
              </li>

              {/* Step 3 */}
              <li
                className={classNames(
                  "mb-6 flex transition-[font-size]",
                  currentStep === 3
                    ? "text-4xl font-bold items-top"
                    : "text-2xl font-semibold opacity-60 items-center"
                )}
              >
                <div className="mr-1 text-right" style={{ minWidth: 35 }}>
                  3.
                </div>
                <div className="flex-grow text-base font-normal pl-3">
                  {currentStep <= 3 && (
                    <>
                      <div
                        className={classNames(
                          "font-light",
                          currentStep === 3 ? "text-4xl mb-3" : "text-2xl"
                        )}
                      >
                        Complete request form
                      </div>
                      {currentStep === 3 &&
                        selectedService &&
                        selectedState && (
                          <RequestForm
                            selectedService={selectedService}
                            selectedState={selectedState}
                            setHasUnsavedChanges={setHasUnsavedChanges}
                          />
                        )}
                    </>
                  )}
                </div>
              </li>
            </ol>
          </div>

          {/* Sidebar */}
          <div className="w-full lg:max-w-[400px]">
            <InfoSidebar />
          </div>
        </div>
      </div>
    </div>
  );
}
