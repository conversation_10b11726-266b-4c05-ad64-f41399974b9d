import { useSS<PERSON> } from "@web/src/lib/hooks";
import { MsLiveAuthIcon } from "@web/src/pages/admin/users/components";

export default function LoginWrapper({ children }: { children: any }) {
  const { signIn, signOut, isPending, user } = useSSO();

  const currentPath = window.location.pathname;
  const publicPath = "/portal";
  const isPublicRoute = currentPath.startsWith(publicPath);

  if (isPublicRoute) return children;
  if (isPending) return null;
  if (user && user.isActive()) return children;

  return (
    <>
      <div
        style={{ height: "100vh", width: "100vw" }}
        className="h-full w-full flex items-center justify-center bg-primary"
      >
        <div className="flex flex-col items-center">
          <img
            src="/static/images/andor_logo_full_white.png"
            alt="Andor Logo"
            className="w-[300px]"
          />
          {!user && (
            <div className="flex justify-center mt-10">
              <button
                onClick={signIn}
                className="btn btn-lg font-normal rounded-full"
              >
                <MsLiveAuthIcon />
                Andor Team - SSO
              </button>
            </div>
          )}
        </div>
        {user && !user.isActive() && (
          <div>
            Access Denied - you are logged in, but
            <br />
            your user account is not enabled.
            <br />
            Please contact your administrator.
            <div className="mt-3">
              <button onClick={signOut} className="btn btn-outline">
                Click To Log Out
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
