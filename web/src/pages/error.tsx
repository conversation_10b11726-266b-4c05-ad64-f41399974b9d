import {
  ArrowLeftCircleIcon,
  ArrowPathIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import { useNavigate } from "react-router-dom";
import { Button } from "../components/buttons";

export default function ErrorPage() {
  const navigate = useNavigate();

  const back = () => {
    navigate(-1);
  };

  return (
    <div className="flex items-center justify-center h-full">
      <div className="flex justify-center">
        <div className="p-8 border-r border-r-slate-400">
          <ExclamationCircleIcon className="w-32 text-red-500" />
        </div>
        <div className="w-1/3 p-8">
          There was an error loading the page. You can try to refresh the page,
          or go back. If the error persists, please contact support.
          <div className="mt-6 flex">
            <Button className="btn-ghost" onClick={back}>
              <ArrowLeftCircleIcon className="w-5 mr-1" />
              Back
            </Button>
            <Button
              className="btn-primary ml-3"
              onClick={() => window.location.reload()}
            >
              <ArrowPathIcon className="w-5 mr-1" />
              Reload
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
