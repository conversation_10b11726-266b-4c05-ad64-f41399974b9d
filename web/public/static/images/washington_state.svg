<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2000 2000" width="2000" height="2000">
	<title>washington_state</title>
	<defs>
		<clipPath clipPathUnits="userSpaceOnUse" id="cp1">
			<path d="m247 499h1506v1002h-1506z"/>
		</clipPath>
		<clipPath clipPathUnits="userSpaceOnUse" id="cp2">
			<path d="m605.52 867.66c5.87-20.56 1.47-27.9 14.69-33.77 2.5-1.12 4.85-2.02 7.01-2.89 9.23-3.72 15.01-6.88 15.01-23.54 0-20.56 5.88-51.4-4.4-55.8-10.28-4.41 2.94-10.28-41.12 0-44.05 10.28-123.34-14.69-123.34-14.69 0 0-29.37 13.22-49.93 0-20.56-13.21-49.92-32.3-49.92-32.3l-24.97-16.15c0 0-27.9-22.03-45.52-22.03-17.62 0-14.68-24.96-30.83 0-16.16 24.96-22.03 20.56-23.5 38.18-1.47 17.62-2.93 17.62 0 36.71 2.94 19.09 22.03 51.39 30.84 67.55 8.81 16.15 23.49 19.08 23.49 41.11 0 22.03-2.93 36.71 0 61.67 2.94 24.97 5.88 36.71 14.69 52.87 8.81 16.15 16.15 11.74 16.15 39.64 0 27.9-10.28 45.52-8.81 51.4 1.47 5.87 36.71 4.4 44.05 8.81 7.34 4.4 11.75 22.02 8.81 29.36-2.94 7.35-19.09 13.22-20.56 26.44-1.47 13.21 11.75 14.68 16.16 23.49 4.4 8.81 5.87 16.15 5.87 27.9 0 11.75-4.41 36.71-19.09 51.39-14.68 14.69-44.05 32.31-24.96 44.06 19.09 11.74 57.27 14.68 57.27 14.68 0 0 39.62-10.28 52.84-7.34 13.23 2.93 32.32 13.21 55.82 30.83 23.49 17.62 38.18 5.88 54.33 24.97 16.15 19.09 30.83 8.81 30.83 51.39 0 42.58 1.47 60.2 7.35 71.95 5.87 11.75 0 8.24 23.49 23.21 23.5 14.97 19.09 19.38 38.18 19.38 19.09 0 48.46-5.88 48.46-5.88 0 0 71.95-48.46 116-35.24 44.05 13.22 61.67 23.49 70.48 27.9 8.81 4.41 35.24 27.9 64.61 13.22 29.37-14.69 52.86-33.78 85.17-41.12 32.3-7.34 19.09-8.81 58.73-10.28 39.65-1.47 49.93 1.47 88.11-8.81 38.18-10.28 80.76-39.65 96.91-45.52 16.15-5.87 2.94-17.62 42.59-11.75 39.64 5.88 425.83 0 425.83 0 0 0 1.47-17.62 0-27.9-1.47-10.27-13.21-24.96-20.56-38.17-7.34-13.22-11.74-7.35-11.74-32.31 0-24.96 11.74-754.76 11.74-754.76 0 0-1080.74-16.15-1107.17-26.43 0 0-10.28 52.87 10.28 58.74 20.56 5.87 38.18 13.21 41.12 20.56 2.93 7.34 10.28 27.9 5.87 51.39-4.41 23.5-24.96 24.96 2.94 63.14 27.89 38.18 35.24 23.5 41.11 58.74 5.87 35.24 8.81 38.18-7.34 69.01-16.15 30.84-16.15 24.97-22.03 55.8-5.87 30.84-13.21 32.31-13.21 60.21 0 27.9 5.87 35.24-10.28 57.26-16.15 22.03-20.56 33.78-55.8 33.78-35.24 0-60.2-4.41-70.48-33.78-10.28-29.36-23.5-33.77-7.34-55.8 16.15-22.02 24.96-24.96 38.18-38.17"/>
		</clipPath>
		<clipPath clipPathUnits="userSpaceOnUse" id="cp3">
			<path d="m605.52 867.66c5.87-20.56 1.47-27.9 14.69-33.77 2.5-1.12 4.85-2.02 7.01-2.89 9.23-3.72 15.01-6.88 15.01-23.54 0-20.56 5.88-51.4-4.4-55.8-10.28-4.41 2.94-10.28-41.12 0-44.05 10.28-123.34-14.69-123.34-14.69 0 0-29.37 13.22-49.93 0-20.56-13.21-49.92-32.3-49.92-32.3l-24.97-16.15c0 0-27.9-22.03-45.52-22.03-17.62 0-14.68-24.96-30.83 0-16.16 24.96-22.03 20.56-23.5 38.18-1.47 17.62-2.93 17.62 0 36.71 2.94 19.09 22.03 51.39 30.84 67.55 8.81 16.15 23.49 19.08 23.49 41.11 0 22.03-2.93 36.71 0 61.67 2.94 24.97 5.88 36.71 14.69 52.87 8.81 16.15 16.15 11.74 16.15 39.64 0 27.9-10.28 45.52-8.81 51.4 1.47 5.87 36.71 4.4 44.05 8.81 7.34 4.4 11.75 22.02 8.81 29.36-2.94 7.35-19.09 13.22-20.56 26.44-1.47 13.21 11.75 14.68 16.16 23.49 4.4 8.81 5.87 16.15 5.87 27.9 0 11.75-4.41 36.71-19.09 51.39-14.68 14.69-44.05 32.31-24.96 44.06 19.09 11.74 57.27 14.68 57.27 14.68 0 0 39.62-10.28 52.84-7.34 13.23 2.93 32.32 13.21 55.82 30.83 23.49 17.62 38.18 5.88 54.33 24.97 16.15 19.09 30.83 8.81 30.83 51.39 0 42.58 1.47 60.2 7.35 71.95 5.87 11.75 0 8.24 23.49 23.21 23.5 14.97 19.09 19.38 38.18 19.38 19.09 0 48.46-5.88 48.46-5.88 0 0 71.95-48.46 116-35.24 44.05 13.22 61.67 23.49 70.48 27.9 8.81 4.41 35.24 27.9 64.61 13.22 29.37-14.69 52.86-33.78 85.17-41.12 32.3-7.34 19.09-8.81 58.73-10.28 39.65-1.47 49.93 1.47 88.11-8.81 38.18-10.28 80.76-39.65 96.91-45.52 16.15-5.87 2.94-17.62 42.59-11.75 39.64 5.88 425.83 0 425.83 0 0 0 1.47-17.62 0-27.9-1.47-10.27-13.21-24.96-20.56-38.17-7.34-13.22-11.74-7.35-11.74-32.31 0-24.96 11.74-754.76 11.74-754.76 0 0-1080.74-16.15-1107.17-26.43 0 0-10.28 52.87 10.28 58.74 20.56 5.87 38.18 13.21 41.12 20.56 2.93 7.34 10.28 27.9 5.87 51.39-4.41 23.5-24.96 24.96 2.94 63.14 27.89 38.18 35.24 23.5 41.11 58.74 5.87 35.24 8.81 38.18-7.34 69.01-16.15 30.84-16.15 24.97-22.03 55.8-5.87 30.84-13.21 32.31-13.21 60.21 0 27.9 5.87 35.24-10.28 57.26-16.15 22.03-20.56 33.78-55.8 33.78-35.24 0-60.2-4.41-70.48-33.78-10.28-29.36-23.5-33.77-7.34-55.8 16.15-22.02 24.96-24.96 38.18-38.17"/>
		</clipPath>
	</defs>
	<style>
		tspan { white-space:pre } 
		.s0 { fill: #ffffff } 
		.s1 { fill: #0033d9 } 
		.t2 { font-size: 150px;fill: #ffffff;font-weight: 400;font-family: "DejaVuSans", "DejaVu Sans" } 
	</style>
	<path fill-rule="evenodd" class="s0" d="m-200-200h2400v2400h-2400z"/>
	<g id="Clip-Path" clip-path="url(#cp1)">
		<g>
			<g id="Clip-Path" clip-path="url(#cp2)">
				<g>
					<g id="Clip-Path" clip-path="url(#cp3)">
						<g>
							<path class="s1" d="m245.8 499.1v1011.7h1508v-1011.7z"/>
						</g>
					</g>
				</g>
			</g>
		</g>
		<text id="Text layer 14" style="transform: matrix(1,0,0,1,738,909)" >
			<tspan x="0" y="114" class="t2">W</tspan><tspan  y="114" class="t2">a</tspan><tspan  y="114" class="t2">s</tspan><tspan  y="114" class="t2">h</tspan><tspan  y="114" class="t2">i</tspan><tspan  y="114" class="t2">n</tspan><tspan  y="114" class="t2">g</tspan><tspan  y="114" class="t2">t</tspan><tspan  y="114" class="t2">o</tspan><tspan  y="114" class="t2">n
</tspan>
		</text>
	</g>
</svg>